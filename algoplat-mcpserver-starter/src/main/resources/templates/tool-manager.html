<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bml Agent Tool Manager</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css">
    <!-- Summernote CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <link rel="stylesheet" href="/css/tool-manager.css">
    <style>
        .top-header {
            background-color: #f8f9fa;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
            width: 100%;
            position: fixed;
            top: 0;
            z-index: 1030;
        }

        /* 文本类型选择按钮组样式 */
        .btn-group-toggle .btn {
            border-radius: 6px !important;
            margin: 0 3px;
            padding: 8px 12px;
            transition: all 0.2s ease;
            border: 1px solid #dee2e6;
            background: #fff;
            color: #6c757d;
            text-align: center;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 0.9rem;
        }

        .btn-group-toggle .btn:hover {
            border-color: #007bff;
            background: #f8f9ff;
            color: #007bff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
        }

        .btn-group-toggle .btn.active {
            border-color: #007bff;
            background: #007bff;
            color: #fff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.25);
        }

        .btn-group-toggle .btn.active:hover {
            background: #0056b3;
            border-color: #0056b3;
        }

        .btn-group-toggle .btn .btn-title {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2px;
        }

        .btn-group-toggle .btn .btn-title i {
            font-size: 0.9rem;
            margin-right: 4px;
        }

        .btn-group-toggle .btn small {
            font-size: 0.7rem;
            opacity: 0.8;
            line-height: 1.2;
        }

        .btn-group-toggle .btn.active small {
            opacity: 1;
        }

        /* 文本框样式优化 */
        #toolDescriptionPlain, #updateToolDescriptionPlain {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.5;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            resize: vertical;
            min-height: 120px;
        }

        #toolDescriptionPlain:focus, #updateToolDescriptionPlain:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
            outline: none;
        }

        /* 编辑器容器动画 */
        #plainTextEditor, #richTextEditor, #updatePlainTextEditor, #updateRichTextEditor {
            transition: all 0.3s ease;
        }
        .app-container {
            margin-top: 70px;
        }
        .platform-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #333;
            letter-spacing: 0.5px;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.05);
            padding-left: 0;
        }
        .header-container {
            width: 100%;
            padding: 0 20px;
        }
        .top-header .btn-outline-info {
            font-weight: 500;
            border-width: 2px;
        }
        .header-flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        /* 固定侧边栏 */
        .sidebar {
            position: fixed;
            top: 70px; /* 顶部导航栏高度 */
            left: 0;
            bottom: 0;
            width: 250px;
            overflow-y: auto;
            z-index: 1020;
        }

        /* 调整主内容区域的位置 */
        .main-content {
            margin-left: 250px; /* 侧边栏宽度 */
            padding-top: 56px; /* 为固定的面包屑导航留出空间 */
        }

        /* 固定位置标记（面包屑导航） */
        .navbar {
            position: fixed;
            top: 70px; /* 顶部导航栏高度 */
            left: 250px; /* 侧边栏宽度 */
            right: 0;
            background-color: white;
            z-index: 1010;
            padding: 10px 20px; /* 增加水平内边距 */
            border-bottom: 1px solid #e9ecef;
            height: 56px; /* 固定高度 */
            box-shadow: 0 2px 4px rgba(0,0,0,0.05); /* 添加轻微阴影效果 */
            display: flex;
            align-items: center; /* 确保内容垂直居中 */
            transition: all 0.3s ease; /* 添加平滑过渡效果 */
            width: calc(100% - 250px); /* 确保宽度占满除侧边栏外的所有空间 */
        }

        .breadcrumb {
            margin-bottom: 0; /* 移除面包屑的底部边距 */
            padding: 0; /* 移除内边距 */
            background-color: transparent; /* 确保背景透明 */
            width: 100%; /* 使面包屑占满导航栏宽度 */
            display: flex;
            align-items: center; /* 确保内容垂直居中 */
        }

        .breadcrumb-item {
            font-size: 1.1rem; /* 增加字体大小 */
            font-weight: 500; /* 加粗字体 */
        }

        .breadcrumb-item a {
            color: #007bff;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        .breadcrumb-item.active {
            color: #6c757d;
            font-weight: 500;
        }

        /* 参数表单样式 */
        .param-item {
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }

        .param-item .form-row {
            flex-wrap: nowrap;
            margin-bottom: 10px;
        }

        .param-item .form-group {
            margin-bottom: 10px;
        }

        .param-item label {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .param-item .remove-param-btn {
            margin-top: 5px;
        }

        .header-item {
            margin-bottom: 10px !important;
        }

        /* 确保参数类型不换行 */
        .param-type {
            min-width: 120px;
            width: 100%;
        }

        /* 调整参数描述框宽度 */
        .param-description {
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 确保更新表单中的参数项也能正确显示 */
        .update-param-list .param-item {
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }

        .content-area {
            padding-top: 15px; /* 添加上边距，避免内容被面包屑导航遮挡 */
        }

        /* 响应式调整 */
        @media (max-width: 992px) {
            /* 在中等屏幕上调整参数表单布局 */
            .param-item .form-row {
                flex-wrap: wrap;
            }

            .param-item .form-row .col-md-3,
            .param-item .form-row .col-md-5,
            .param-item .form-row .col-md-2 {
                flex: 0 0 100%;
                max-width: 100%;
                margin-bottom: 10px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }
            .main-content {
                margin-left: 200px;
            }
            .navbar {
                left: 200px;
            }
        }

        @media (max-width: 576px) {
            .sidebar {
                width: 0;
                display: none;
            }
            .main-content {
                margin-left: 0;
            }
            .navbar {
                left: 0;
            }

            /* 移动设备上参数表单的样式 */
            .param-item .form-row .col-md-3,
            .param-item .form-row .col-md-5,
            .param-item .form-row .col-md-2 {
                flex: 0 0 100%;
                max-width: 100%;
                margin-bottom: 10px;
            }
        }

        /* 禁用按钮样式 */
        .btn:disabled, .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* 橙色按钮样式 */
        .btn-outline-warning:hover {
            color: #fff;
            background-color: #fd7e14;
            border-color: #fd7e14;
        }
    </style>
</head>
<body>
    <!-- 全局顶部导航 -->
    <div class="top-header">
        <div class="header-container">
            <div class="header-flex">
                <div>
                    <strong class="platform-title">Bml Agent Tool Manager</strong>
                </div>
                <div>
                    <a href="https://km.sankuai.com/collabpage/2708847578" target="_blank" class="btn btn-outline-info">
                        <i class="fa fa-book"></i> 平台使用说明
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="app-container">
        <!-- 侧边栏 - 业务场景列表 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>业务场景</h3>
                <button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#createBusinessLineModal">
                    <i class="fa fa-plus"></i>
                </button>
            </div>
            <div class="sidebar-search">
                <div class="input-group">
                    <input type="text" id="searchBusinessLineName" class="form-control" placeholder="搜索业务场景...">
                    <div class="input-group-append">
                        <button class="btn btn-outline-secondary" id="searchBusinessLineBtn" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div id="businessLinesList" class="sidebar-list">
                <!-- 业务场景列表将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 内部导航 -->
            <nav class="navbar navbar-expand-lg navbar-light">
                <ol class="breadcrumb bg-transparent mb-0">
                    <li class="breadcrumb-item"><a href="#" id="homeLink">首页</a></li>
                    <li class="breadcrumb-item active" id="currentBusinessLine">选择业务场景</li>
                </ol>
            </nav>

            <!-- 提示信息容器 -->
            <div id="alertContainer" class="mt-3 px-3"></div>

            <!-- 内容区域 -->
            <div id="contentArea" class="content-area">
                <!-- 初始欢迎页面 -->
                <div id="welcomePage" class="welcome-page">
                    <div class="jumbotron">
                        <h1 class="display-4">欢迎使用</h1>
                        <h1 class="display-4">Bml Agent Tool Manager</h1>
                        <p>本平台用于管理Bml Agent的工具配置，包括业务场景、McpServer和工具的创建、编辑和删除。</p>
                        <hr class="my-4">
                        <p class="lead">请从左侧选择一个业务场景，或创建新的业务场景开始使用。</p>
                    </div>
                </div>

                <!-- McpServer列表区域 -->
                <div id="mcpServerListArea" class="mcpserver-list-area" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 id="mcpServerListTitle">McpServer列表</h2>
                        <button class="btn btn-success" id="createMcpServerBtn">
                            <i class="fa fa-plus"></i> 添加McpServer
                        </button>
                    </div>

                    <!-- 添加McpServer搜索区域 -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">McpServer名称</span>
                                        </div>
                                        <input type="text" id="searchMcpServerName" class="form-control" placeholder="输入McpServer名称...">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">负责人</span>
                                        </div>
                                        <input type="text" id="searchMcpServerOwner" class="form-control" placeholder="输入负责人...">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="btn-group">
                                        <button class="btn btn-primary" id="searchMcpServerBtn">
                                            <i class="fa fa-search"></i> 搜索
                                        </button>
                                        <button class="btn btn-outline-secondary" id="resetMcpServerSearchBtn">
                                            <i class="fa fa-refresh"></i> 重置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="mcpServerCards" class="row">
                        <!-- McpServer卡片将通过JavaScript动态加载 -->
                    </div>
                </div>

                <!-- 工具列表区域 -->
                <div id="toolListArea" class="tool-list-area" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <button class="btn btn-outline-secondary mr-2" id="backToMcpServerBtn">
                                <i class="fa fa-arrow-left"></i> 返回
                            </button>
                            <h2 id="toolListTitle" class="d-inline">工具列表</h2>
                        </div>
                        <div>
                            <div class="input-group mr-2 d-inline-flex" style="width: 300px;">
                                <input type="text" id="toolSearchInput" class="form-control" placeholder="搜索工具...">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" id="toolSearchBtn">
                                        <i class="fa fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <button class="btn btn-success" id="createToolBtn">
                                <i class="fa fa-plus"></i> 添加工具
                            </button>
                            <button class="btn btn-info ml-2" id="selectDefaultToolsBtn">
                                <i class="fa fa-list"></i> 选择默认工具
                            </button>
                        </div>
                    </div>

                    <div class="tool-filters mb-4">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary tool-type-filter active" data-type="all">所有类型</button>
                            <button type="button" class="btn btn-outline-info tool-type-filter" data-type="HTTP">HTTP</button>
                            <button type="button" class="btn btn-outline-primary tool-type-filter" data-type="THRIFT">THRIFT</button>
                            <button type="button" class="btn btn-outline-warning tool-type-filter" data-type="PIGEON">PIGEON</button>
                            <button type="button" class="btn btn-outline-dark tool-type-filter" data-type="SystemDefault">SystemDefault</button>
                            <button type="button" class="btn btn-outline-success tool-type-filter" data-type="ToolAnnotation">ToolAnnotation</button>
                        </div>
                        <select id="ownerFilter" class="form-control ml-2 d-inline-block" style="width: 200px;">
                            <option value="">所有负责人</option>
                        </select>
                    </div>

                    <div id="toolCards" class="row">
                        <!-- 工具卡片将通过JavaScript动态加载 -->
                    </div>

                    <div id="toolPagination" class="mt-4"></div>
                </div>
            </div>

            <!-- 页脚 -->
            <footer class="footer">
                <div class="container-fluid text-center">
                    <p class="text-muted">本服务由Bml Agent探索小组提供，未经允许请勿修改内容！</p>
                </div>
            </footer>
        </div>
    </div>

    <!-- 创建业务场景模态框 -->
    <div class="modal fade" id="createBusinessLineModal" tabindex="-1" role="dialog" aria-labelledby="createBusinessLineModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createBusinessLineModalLabel">创建业务场景</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="createBusinessLineForm">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="businessLineName" class="required-field">业务场景名称</label>
                            <input type="text" class="form-control" id="businessLineName" required maxlength="50">
                            <small class="form-text text-muted">业务场景名称不能超过50个字符，且不能包含中文。创建后不可修改！</small>
                        </div>
                        <div class="form-group">
                            <label for="businessLineDescription" class="required-field">业务场景描述</label>
                            <textarea class="form-control" id="businessLineDescription" rows="3" required maxlength="100"></textarea>
                            <small class="form-text text-muted">业务场景描述不能超过100个字符</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">创建</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 更新业务场景模态框 -->
    <div class="modal fade" id="updateBusinessLineModal" tabindex="-1" role="dialog" aria-labelledby="updateBusinessLineModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateBusinessLineModalLabel">更新业务场景</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="updateBusinessLineForm">
                    <div class="modal-body">
                        <input type="hidden" id="updateBusinessLineId">
                        <div class="form-group">
                            <label for="updateBusinessLineName" class="required-field">业务场景名称</label>
                            <input type="text" class="form-control" id="updateBusinessLineName" readonly>
                            <small class="form-text text-muted">业务场景名称不可修改</small>
                        </div>
                        <div class="form-group">
                            <label for="updateBusinessLineDescription" class="required-field">业务场景描述</label>
                            <textarea class="form-control" id="updateBusinessLineDescription" rows="3" required maxlength="100"></textarea>
                            <small class="form-text text-muted">业务场景描述不能超过100个字符</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">更新</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除业务场景确认模态框 -->
    <div class="modal fade" id="confirmDeleteBusinessModal" tabindex="-1" role="dialog" aria-labelledby="confirmDeleteBusinessModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmDeleteBusinessModalLabel">确认删除业务场景</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除业务场景 <strong class="business-name"></strong> 吗？</p>
                    <p class="text-danger">警告：此操作将会删除该业务场景下的所有McpServer和工具，且无法恢复！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger confirm-delete-business-btn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建McpServer模态框 -->
    <div class="modal fade" id="createMcpServerModal" tabindex="-1" role="dialog" aria-labelledby="createMcpServerModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createMcpServerModalLabel">创建McpServer</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="createMcpServerForm">
                    <div class="modal-body">
                        <input type="hidden" id="businessLineId">
                        <div class="form-group">
                            <label for="mcpServerName" class="required-field">McpServer名称</label>
                            <input type="text" class="form-control" id="mcpServerName" required>
                            <small class="form-text text-muted">McpServer名称不能包含中文，创建后不可修改！</small>
                        </div>
                        <div class="form-group">
                            <label for="mcpServerOwner" class="required-field">负责人</label>
                            <input type="text" class="form-control" id="mcpServerOwner" required>
                        </div>
                        <div class="form-group">
                            <label for="mcpServerDescription">描述</label>
                            <textarea class="form-control" id="mcpServerDescription" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">创建</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 更新McpServer模态框 -->
    <div class="modal fade" id="updateMcpServerModal" tabindex="-1" role="dialog" aria-labelledby="updateMcpServerModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateMcpServerModalLabel">更新McpServer</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="updateMcpServerForm">
                    <div class="modal-body">
                        <input type="hidden" id="updateMcpServerId">
                        <div class="form-group">
                            <label for="updateMcpServerName" class="required-field">McpServer名称</label>
                            <input type="text" class="form-control" id="updateMcpServerName" readonly>
                            <small class="form-text text-muted">McpServer名称不可修改</small>
                        </div>
                        <div class="form-group">
                            <label for="updateMcpServerOwner" class="required-field">负责人</label>
                            <input type="text" class="form-control" id="updateMcpServerOwner" required>
                        </div>
                        <div class="form-group">
                            <label for="updateMcpServerDescription">描述</label>
                            <textarea class="form-control" id="updateMcpServerDescription" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">更新</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除McpServer确认模态框 -->
    <div class="modal fade" id="confirmDeleteMcpServerModal" tabindex="-1" role="dialog" aria-labelledby="confirmDeleteMcpServerModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmDeleteMcpServerModalLabel">确认删除McpServer</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除McpServer <strong class="mcpserver-name"></strong> 吗？</p>
                    <p class="text-danger">警告：此操作将会删除该McpServer下的所有工具，且无法恢复！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger confirm-delete-mcpserver-btn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建工具模态框 -->
    <div class="modal fade" id="createToolModal" tabindex="-1" role="dialog" aria-labelledby="createToolModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createToolModalLabel">创建工具</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="createToolForm">
                    <div class="modal-body">
                        <input type="hidden" id="mcpServerId">

                        <!-- 通用字段 -->
                        <div class="form-group">
                            <label for="toolType" class="required-field">工具类型</label>
                            <select class="form-control" id="toolType" required>
                                <option value="HTTP">HTTP</option>
                                <option value="THRIFT">THRIFT</option>
                                <option value="PIGEON">PIGEON</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="toolName" class="required-field">工具名称</label>
                            <input type="text" class="form-control" id="toolName" required>
                        </div>

                        <div class="form-group">
                            <label for="toolDescription" class="required-field">工具描述</label>
                            <div class="form-group mb-3">
                                <label class="mb-2">选择编辑模式</label>
                                <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                    <label class="btn btn-outline-primary active" for="plainTextType">
                                        <input type="radio" name="textType" id="plainTextType" value="2" checked>
                                        <div class="btn-title">
                                            <i class="fa fa-align-left"></i>普通文本
                                        </div>
                                        <small class="d-block text-muted">简洁易用，适合纯文本描述</small>
                                    </label>
                                    <label class="btn btn-outline-primary" for="richTextType">
                                        <input type="radio" name="textType" id="richTextType" value="1">
                                        <div class="btn-title">
                                            <i class="fa fa-edit"></i>富文本
                                        </div>
                                        <small class="d-block text-muted">支持格式化，适合复杂描述</small>
                                    </label>
                                </div>
                            </div>
                            <div id="plainTextEditor">
                                <textarea class="form-control" id="toolDescriptionPlain" rows="8" placeholder="请输入工具描述...&#10;&#10;提示：&#10;• 详细描述工具的功能和用途&#10;• 说明主要参数和返回值&#10;• 提供使用示例或注意事项"></textarea>
                            </div>
                            <div id="richTextEditor" style="display: none;">
                                <textarea class="summernote" id="toolDescription"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="toolOwner" class="required-field">负责人</label>
                            <input type="text" class="form-control" id="toolOwner" required>
                        </div>

                        <div class="form-group">
                            <label for="toolTimeout">超时时间(ms)</label>
                            <input type="number" class="form-control" id="toolTimeout" value="6000">
                        </div>

                        <!-- HTTP工具表单 -->
                        <div id="HTTPForm" class="tool-form">
                            <h5 class="mt-4">HTTP配置</h5>
                            <hr>

                            <div class="form-group">
                                <label for="httpUrl" class="required-field">URL</label>
                                <input type="text" class="form-control" id="httpUrl" required>
                            </div>

                            <div class="form-group">
                                <label for="httpMethod" class="required-field">请求方法</label>
                                <select class="form-control" id="httpMethod" required>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>请求头</label>
                                <div id="httpHeaderList" class="header-list"></div>
                                <button type="button" class="btn btn-sm btn-primary add-header-btn mt-2">
                                    <i class="fa fa-plus"></i> 添加请求头
                                </button>
                            </div>

                            <div class="form-group">
                                <label>参数列表</label>
                                <div id="HTTPParamList" class="param-list"></div>
                                <button type="button" class="btn btn-sm btn-primary add-param-btn mt-2" data-tool-type="HTTP">
                                    <i class="fa fa-plus"></i> 添加参数
                                </button>
                            </div>
                        </div>

                        <!-- THRIFT工具表单 -->
                        <div id="THRIFTForm" class="tool-form" style="display: none;">
                            <h5 class="mt-4">THRIFT配置</h5>
                            <hr>

                            <div class="form-group">
                                <label for="isLongTermTask">
                                    是否是长连接任务
                                    <a href="javascript:void(0)" class="ml-1 text-info" data-toggle="tooltip" title="当工具执行需要较长时间时，请标记该工具为长连接任务，并通过提交和获取工具执行结果的形式实现工具的调用。">
                                        <i class="fa fa-question-circle"></i>
                                    </a>
                                </label>
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input" id="isLongTermTask">
                                    <label class="custom-control-label" for="isLongTermTask">否</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="thriftAppKey" class="required-field">服务AppKey</label>
                                <input type="text" class="form-control" id="thriftAppKey" required>
                            </div>

                            <div class="form-group">
                                <label for="thriftInterfaceName" class="required-field">接口全路径</label>
                                <input type="text" class="form-control interface-name" id="thriftInterfaceName" required>
                            </div>

                            <div class="form-group">
                                <label for="thriftMethodName" class="required-field">方法名称</label>
                                <input type="text" class="form-control method-name" id="thriftMethodName" required>
                            </div>

                            <div class="form-group">
                                <button type="button" class="btn btn-info ai-generate-btn" data-tool-type="THRIFT" data-mode="create">
                                    <i class="fa fa-magic"></i> AI生成工具信息
                                </button>
                                <small class="form-text text-muted">需要先填写接口全路径和方法名称</small>
                            </div>

                            <div class="form-group">
                                <label for="thriftCell">泳道</label>
                                <input type="text" class="form-control" id="thriftCell">
                                <small class="form-text text-muted">可选</small>
                            </div>

                            <div class="form-group">
                                <label for="thriftIp">IP地址</label>
                                <input type="text" class="form-control" id="thriftIp">
                                <small class="form-text text-muted">可选</small>
                            </div>

                            <div class="form-group">
                                <label for="thriftPort">端口号</label>
                                <input type="text" class="form-control" id="thriftPort">
                                <small class="form-text text-muted">可选</small>
                            </div>

                            <div class="form-group">
                                <label>参数列表</label>
                                <div id="THRIFTParamList" class="param-list"></div>
                                <button type="button" class="btn btn-sm btn-primary add-param-btn mt-2" data-tool-type="THRIFT">
                                    <i class="fa fa-plus"></i> 添加参数
                                </button>
                            </div>

                            <!-- Avitor脚本编辑器 -->
                            <div class="form-group mt-4">
                                <label for="thriftAvitorScript" class="d-flex align-items-center">
                                    <i class="fa fa-code mr-2"></i> Avitor脚本处理器
                                </label>
                                <textarea class="form-control avitor-script" id="thriftAvitorScript" rows="8" placeholder="// 在这里编写Avitor脚本，用于处理参数"></textarea>
                                <small class="form-text text-muted">可以使用Avitor脚本对参数进行预处理</small>
                            </div>
                        </div>

                        <!-- PIGEON工具表单 -->
                        <div id="PIGEONForm" class="tool-form" style="display: none;">
                            <h5 class="mt-4">PIGEON配置</h5>
                            <hr>

                            <div class="form-group">
                                <label for="pigeonAppKey" class="required-field">服务AppKey</label>
                                <input type="text" class="form-control" id="pigeonAppKey" required>
                            </div>

                            <div class="form-group">
                                <label for="pigeonInterfaceName" class="required-field">接口全路径</label>
                                <input type="text" class="form-control interface-name" id="pigeonInterfaceName" required>
                            </div>

                            <div class="form-group">
                                <label for="pigeonMethodName" class="required-field">方法名称</label>
                                <input type="text" class="form-control method-name" id="pigeonMethodName" required>
                            </div>

                            <div class="form-group">
                                <button type="button" class="btn btn-info ai-generate-btn" data-tool-type="PIGEON" data-mode="create">
                                    <i class="fa fa-magic"></i> AI生成工具信息
                                </button>
                                <small class="form-text text-muted">需要先填写接口全路径和方法名称</small>
                            </div>

                            <div class="form-group">
                                <label for="pigeonCell">泳道</label>
                                <input type="text" class="form-control" id="pigeonCell">
                                <small class="form-text text-muted">可选</small>
                            </div>

                            <div class="form-group">
                                <label>参数列表</label>
                                <div id="PIGEONParamList" class="param-list"></div>
                                <button type="button" class="btn btn-sm btn-primary add-param-btn mt-2" data-tool-type="PIGEON">
                                    <i class="fa fa-plus"></i> 添加参数
                                </button>
                            </div>

                            <!-- Avitor脚本编辑器 -->
                            <div class="form-group mt-4">
                                <label for="pigeonAvitorScript" class="d-flex align-items-center">
                                    <i class="fa fa-code mr-2"></i> Avitor脚本处理器
                                </label>
                                <textarea class="form-control avitor-script" id="pigeonAvitorScript" rows="8" placeholder="// 在这里编写Avitor脚本，用于处理参数"></textarea>
                                <small class="form-text text-muted">可以使用Avitor脚本对参数进行预处理</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">创建</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 更新工具模态框 -->
    <div class="modal fade" id="updateToolModal" tabindex="-1" role="dialog" aria-labelledby="updateToolModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="updateToolModalLabel">更新工具</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="updateToolForm">
                    <div class="modal-body">
                        <input type="hidden" id="updateToolId">
                        <input type="hidden" id="updateToolMcpServerId">

                        <!-- 通用字段 -->
                        <div class="form-group">
                            <label for="updateToolType" class="required-field">工具类型</label>
                            <select class="form-control" id="updateToolType" required>
                                <option value="HTTP">HTTP</option>
                                <option value="THRIFT">THRIFT</option>
                                <option value="PIGEON">PIGEON</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="updateToolName" class="required-field">工具名称</label>
                            <input type="text" class="form-control" id="updateToolName" required>
                        </div>

                        <div class="form-group">
                            <label for="updateToolDescription" class="required-field">工具描述</label>
                            <div class="form-group mb-3">
                                <label class="mb-2">选择编辑模式</label>
                                <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                    <label class="btn btn-outline-primary" for="updatePlainTextType">
                                        <input type="radio" name="updateTextType" id="updatePlainTextType" value="2">
                                        <div class="btn-title">
                                            <i class="fa fa-align-left"></i>普通文本
                                        </div>
                                        <small class="d-block text-muted">简洁易用，适合纯文本描述</small>
                                    </label>
                                    <label class="btn btn-outline-primary" for="updateRichTextType">
                                        <input type="radio" name="updateTextType" id="updateRichTextType" value="1">
                                        <div class="btn-title">
                                            <i class="fa fa-edit"></i>富文本
                                        </div>
                                        <small class="d-block text-muted">支持格式化，适合复杂描述</small>
                                    </label>
                                </div>
                            </div>
                            <div id="updatePlainTextEditor" style="display: none;">
                                <textarea class="form-control" id="updateToolDescriptionPlain" rows="8" placeholder="请输入工具描述...&#10;&#10;提示：&#10;• 详细描述工具的功能和用途&#10;• 说明主要参数和返回值&#10;• 提供使用示例或注意事项"></textarea>
                            </div>
                            <div id="updateRichTextEditor" style="display: none;">
                                <textarea class="summernote" id="updateToolDescription"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="updateToolOwner" class="required-field">负责人</label>
                            <input type="text" class="form-control" id="updateToolOwner" required>
                        </div>

                        <div class="form-group">
                            <label for="updateToolTimeout">超时时间(ms)</label>
                            <input type="number" class="form-control" id="updateToolTimeout" value="6000">
                        </div>

                        <div class="form-group">
                            <label for="updateIsLongTermTask">
                                是否是长连接任务
                                <a href="javascript:void(0)" class="ml-1 text-info" data-toggle="tooltip" title="当工具执行需要较长时间时，请标记该工具为长连接任务，并通过提交和获取工具执行结果的形式实现工具的调用。">
                                    <i class="fa fa-question-circle"></i>
                                </a>
                            </label>
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="updateIsLongTermTask">
                                <label class="custom-control-label" for="updateIsLongTermTask">否</label>
                            </div>
                        </div>

                        <!-- HTTP工具表单 -->
                        <div id="updateHTTPForm" class="updatetool-form">
                            <h5 class="mt-4">HTTP配置</h5>
                            <hr>

                            <div class="form-group">
                                <label for="updateHttpUrl" class="required-field">URL</label>
                                <input type="text" class="form-control" id="updateHttpUrl" required>
                            </div>

                            <div class="form-group">
                                <label for="updateHttpMethod" class="required-field">请求方法</label>
                                <select class="form-control" id="updateHttpMethod" required>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>请求头</label>
                                <div id="updateHttpHeaderList" class="header-list"></div>
                                <button type="button" class="btn btn-sm btn-primary add-header-btn mt-2">
                                    <i class="fa fa-plus"></i> 添加请求头
                                </button>
                            </div>

                            <div class="form-group">
                                <label>参数列表</label>
                                <div id="updateHTTPParamList" class="update-param-list"></div>
                                <button type="button" class="btn btn-sm btn-primary add-param-btn mt-2" data-tool-type="HTTP">
                                    <i class="fa fa-plus"></i> 添加参数
                                </button>
                            </div>
                        </div>

                        <!-- THRIFT工具表单 -->
                        <div id="updateTHRIFTForm" class="updatetool-form" style="display: none;">
                            <h5 class="mt-4">THRIFT配置</h5>
                            <hr>

                            <div class="form-group">
                                <label for="updateThriftAppKey" class="required-field">服务AppKey</label>
                                <input type="text" class="form-control" id="updateThriftAppKey" name="updateThriftAppKey" required>
                            </div>

                            <div class="form-group">
                                <label for="updateThriftInterfaceName" class="required-field">接口全路径</label>
                                <input type="text" class="form-control interface-name" id="updateThriftInterfaceName" name="updateThriftInterfaceName" required>
                            </div>

                            <div class="form-group">
                                <label for="updateThriftMethodName" class="required-field">方法名称</label>
                                <input type="text" class="form-control method-name" id="updateThriftMethodName" name="updateThriftMethodName" required>
                            </div>

                            <div class="form-group">
                                <button type="button" class="btn btn-info ai-generate-btn" data-tool-type="THRIFT" data-mode="update">
                                    <i class="fa fa-magic"></i> AI生成工具信息
                                </button>
                                <small class="form-text text-muted">需要先填写接口全路径和方法名称</small>
                            </div>

                            <div class="form-group">
                                <label for="updateThriftCell">泳道</label>
                                <input type="text" class="form-control" id="updateThriftCell" name="updateThriftCell">
                                <small class="form-text text-muted">可选</small>
                            </div>

                            <div class="form-group">
                                <label for="updateThriftIp">IP地址</label>
                                <input type="text" class="form-control" id="updateThriftIp" name="updateThriftIp">
                                <small class="form-text text-muted">可选</small>
                            </div>

                            <div class="form-group">
                                <label for="updateThriftPort">端口号</label>
                                <input type="text" class="form-control" id="updateThriftPort" name="updateThriftPort">
                                <small class="form-text text-muted">可选</small>
                            </div>

                            <div class="form-group">
                                <label>参数列表</label>
                                <div id="updateTHRIFTParamList" class="update-param-list"></div>
                                <button type="button" class="btn btn-sm btn-primary add-param-btn mt-2" data-tool-type="THRIFT">
                                    <i class="fa fa-plus"></i> 添加参数
                                </button>
                            </div>

                            <!-- Avitor脚本编辑器 -->
                            <div class="form-group mt-4">
                                <label for="updateThriftAvitorScript" class="d-flex align-items-center">
                                    <i class="fa fa-code mr-2"></i> Avitor脚本处理器
                                </label>
                                <textarea class="form-control avitor-script" id="updateThriftAvitorScript" rows="8" placeholder="// 在这里编写Avitor脚本，用于处理参数"></textarea>
                                <small class="form-text text-muted">可以使用Avitor脚本对参数进行预处理</small>
                            </div>
                        </div>

                        <!-- PIGEON工具表单 -->
                        <div id="updatePIGEONForm" class="updatetool-form" style="display: none;">
                            <h5 class="mt-4">PIGEON配置</h5>
                            <hr>

                            <div class="form-group">
                                <label for="updatePigeonAppKey" class="required-field">服务AppKey</label>
                                <input type="text" class="form-control" id="updatePigeonAppKey" name="updatePigeonAppKey" required>
                            </div>

                            <div class="form-group">
                                <label for="updatePigeonInterfaceName" class="required-field">接口全路径</label>
                                <input type="text" class="form-control interface-name" id="updatePigeonInterfaceName" name="updatePigeonInterfaceName" required>
                            </div>

                            <div class="form-group">
                                <label for="updatePigeonMethodName" class="required-field">方法名称</label>
                                <input type="text" class="form-control method-name" id="updatePigeonMethodName" name="updatePigeonMethodName" required>
                            </div>

                            <div class="form-group">
                                <button type="button" class="btn btn-info ai-generate-btn" data-tool-type="PIGEON" data-mode="update">
                                    <i class="fa fa-magic"></i> AI生成工具信息
                                </button>
                                <small class="form-text text-muted">需要先填写接口全路径和方法名称</small>
                            </div>

                            <div class="form-group">
                                <label for="updatePigeonCell">泳道</label>
                                <input type="text" class="form-control" id="updatePigeonCell" name="updatePigeonCell">
                                <small class="form-text text-muted">可选</small>
                            </div>

                            <div class="form-group">
                                <label>参数列表</label>
                                <div id="updatePIGEONParamList" class="update-param-list"></div>
                                <button type="button" class="btn btn-sm btn-primary add-param-btn mt-2" data-tool-type="PIGEON">
                                    <i class="fa fa-plus"></i> 添加参数
                                </button>
                            </div>

                            <!-- Avitor脚本编辑器 -->
                            <div class="form-group mt-4">
                                <label for="updatePigeonAvitorScript" class="d-flex align-items-center">
                                    <i class="fa fa-code mr-2"></i> Avitor脚本处理器
                                </label>
                                <textarea class="form-control avitor-script" id="updatePigeonAvitorScript" rows="8" placeholder="// 在这里编写Avitor脚本，用于处理参数"></textarea>
                                <small class="form-text text-muted">可以使用Avitor脚本对参数进行预处理</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">更新</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- 查看工具详情模态框 -->
    <div class="modal fade" id="viewToolModal" tabindex="-1" role="dialog" aria-labelledby="viewToolModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewToolModalLabel">工具详情</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- 工具详情内容将通过JavaScript动态填充 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除工具确认模态框 -->
    <div class="modal fade" id="confirmDeleteToolModal" tabindex="-1" role="dialog" aria-labelledby="confirmDeleteToolModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmDeleteToolModalLabel">确认删除工具</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除工具 <strong class="tool-name"></strong> 吗？</p>
                    <p class="text-danger">警告：此操作无法恢复！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger confirm-delete-tool-btn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <!-- Summernote JS -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
    <!-- CodeMirror JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/matchbrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/edit/closebrackets.min.js"></script>
    <script src="/js/common.js"></script>
    <script src="/js/business-line.js"></script>
    <script src="/js/mcpserver.js"></script>
    <script src="/js/tool-manager.js"></script>
    <script src="/js/default-tools.js"></script>
    <script src="/js/tool-fix.js"></script>
    <script src="/js/avitor-script.js"></script>
    <script src="/js/tool-manager-fix.js?v=v2025-08-20-1"></script>
    <script src="/js/business-mcpserver-fix.js"></script>
    <div class="modal fade" id="selectDefaultToolsModal" tabindex="-1" role="dialog" aria-labelledby="selectDefaultToolsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="selectDefaultToolsModalLabel">选择默认工具</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 选择系统提供的默认工具，添加到当前 McpServer
                    </div>

                    <!-- 添加隐藏表单，用于直接提交请求 -->
                    <form id="defaultToolsForm" action="/mcp/api/tools/getAllDefaultTools" method="GET" style="display:none;">
                        <input type="hidden" id="formMcpServerId" name="mcpServerId" value="">
                        <button type="submit" id="submitDefaultToolsForm">提交</button>
                    </form>

                    <!-- 添加保存默认工具的表单 -->
                    <form id="saveDefaultToolsForm" action="/mcp/api/tools/saveDefaultToolForMcpServer" method="POST" style="display:none;">
                        <input type="hidden" id="saveFormMcpServerId" name="mcpServerId" value="">
                        <div id="selectedToolsContainer"></div>
                        <button type="submit" id="submitSaveDefaultToolsForm">提交</button>
                    </form>

                    <div id="defaultToolsList" class="mt-3">
                        <!-- 默认工具列表将通过JavaScript动态加载 -->
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <p>加载默认工具列表...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveDefaultToolsBtn">保存选择</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 内联脚本，确保关键函数在全局作用域中可用 -->
    <script>
        // 确保loadDefaultTools和saveDefaultTools函数在全局作用域中可用
        window.loadDefaultTools = window.loadDefaultTools || function(mcpServerId) {
            console.log("内联脚本中的loadDefaultTools被调用，McpServerId:", mcpServerId);

            if (!mcpServerId) {
                showAlert('McpServerId 为空，无法加载默认工具', 'danger');
                return;
            }

            // 确保 mcpServerId 是数字类型
            mcpServerId = parseInt(mcpServerId, 10);
            if (isNaN(mcpServerId)) {
                showAlert('McpServerId 不是有效的数字', 'danger');
                return;
            }

            $('#defaultToolsList').html(`
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <p>加载默认工具列表...</p>
                </div>
            `);

            // 使用AJAX方式发送请求
            $.ajax({
                url: '/mcp/api/tools/getAllDefaultTools',
                type: 'GET',
                data: {
                    mcpServerId: mcpServerId,
                    _t: new Date().getTime() // 添加时间戳避免缓存
                },
                success: function(response) {
                    console.log("加载默认工具列表响应:", response);
                    if (response.code === 0) {
                        let defaultTools = [];
                        try {
                            if (typeof response.data === 'string') {
                                defaultTools = JSON.parse(response.data);
                            } else if (Array.isArray(response.data)) {
                                defaultTools = response.data;
                            } else if (response.data) {
                                defaultTools = [response.data];
                            }

                            if (!defaultTools) {
                                defaultTools = [];
                            }
                        } catch (e) {
                            console.error("解析默认工具数据失败", e);
                            defaultTools = [];
                        }

                        // 渲染默认工具列表
                        renderDefaultTools(defaultTools);
                    } else {
                        console.error("加载默认工具列表失败:", response.message);
                        showAlert('加载默认工具列表失败: ' + response.message, 'danger');
                        $('#defaultToolsList').html(`
                            <div class="alert alert-danger">
                                <i class="fa fa-exclamation-circle"></i> 加载默认工具列表失败: ${response.message}
                            </div>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("加载默认工具列表请求错误:", error);
                    showAlert('加载默认工具列表失败: ' + error, 'danger');
                    $('#defaultToolsList').html(`
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> 加载默认工具列表失败: ${error}
                        </div>
                    `);
                }
            });
        };

        // 渲染默认工具列表
        function renderDefaultTools(tools) {
            const container = $('#defaultToolsList');
            container.empty();

            if (!tools || tools.length === 0) {
                container.html(`
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i> 暂无可用的默认工具
                    </div>
                `);
                return;
            }

            // 创建表格显示默认工具
            let html = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%"><input type="checkbox" id="selectAllDefaultTools"></th>
                                <th width="20%">工具名称</th>
                                <th width="15%">类型</th>
                                <th width="60%">描述</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            tools.forEach(function(tool) {
                const toolTypeClass = getToolTypeClass(tool.type);
                html += `
                    <tr>
                        <td>
                            <input type="checkbox" class="default-tool-checkbox" value="${tool.name}">
                        </td>
                        <td>${tool.name}</td>
                        <td><span class="tool-type ${toolTypeClass}">${tool.type}</span></td>
                        <td>${tool.description || '无描述'}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.html(html);

            // 绑定全选/取消全选事件
            $('#selectAllDefaultTools').on('change', function() {
                const isChecked = $(this).prop('checked');
                $('.default-tool-checkbox').prop('checked', isChecked);
            });
        }

        // 保存选择的默认工具
        window.saveDefaultTools = window.saveDefaultTools || function(mcpServerId) {
            console.log("内联脚本中的saveDefaultTools被调用，McpServerId:", mcpServerId);

            if (!mcpServerId) {
                showAlert('McpServerId 为空，无法保存默认工具', 'danger');
                return;
            }

            // 确保 mcpServerId 是数字类型
            mcpServerId = parseInt(mcpServerId, 10);
            if (isNaN(mcpServerId)) {
                showAlert('McpServerId 不是有效的数字', 'danger');
                return;
            }

            // 获取所有选中的默认工具
            const selectedTools = [];
            $('.default-tool-checkbox:checked').each(function() {
                selectedTools.push($(this).val());
            });

            console.log("选中的默认工具:", selectedTools);

            if (selectedTools.length === 0) {
                showAlert('请至少选择一个默认工具', 'warning');
                return;
            }

            // 显示加载状态
            $('#saveDefaultToolsBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 保存中...');

            // 创建表单数据
            const formData = new FormData();
            formData.append('mcpServerId', mcpServerId);

            // 添加多个同名参数
            selectedTools.forEach(tool => {
                formData.append('useDefaultToolNameList', tool);
            });

            // 使用fetch API发送请求
            fetch('/mcp/api/tools/saveDefaultToolForMcpServer', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(response => {
                console.log("保存默认工具响应:", response);
                // 恢复按钮状态
                $('#saveDefaultToolsBtn').prop('disabled', false).html('保存选择');

                if (response.code === 0) {
                    showAlert('默认工具保存成功', 'success');
                    $('#selectDefaultToolsModal').modal('hide');
                    // 重新加载工具列表
                    if (typeof loadToolsByMcpServerId === 'function') {
                        loadToolsByMcpServerId(mcpServerId);
                    } else {
                        console.error("loadToolsByMcpServerId 函数未定义");
                        // 尝试刷新页面
                        location.reload();
                    }
                } else {
                    console.error("保存默认工具失败:", response.message);
                    showAlert('默认工具保存失败: ' + response.message, 'danger');
                }
            })
            .catch(error => {
                console.error("保存默认工具请求错误:", error);
                // 恢复按钮状态
                $('#saveDefaultToolsBtn').prop('disabled', false).html('保存选择');
                showAlert('默认工具保存失败: ' + error, 'danger');
            });
        };

        // 获取工具类型对应的CSS类
        function getToolTypeClass(type) {
            switch (type) {
                case 'HTTP':
                    return 'tool-type-http';
                case 'THRIFT':
                    return 'tool-type-thrift';
                case 'PIGEON':
                    return 'tool-type-pigeon';
                case 'SystemDefault':
                    return 'tool-type-systemdefault';
                case 'ToolAnnotation':
                    return 'tool-type-toolannotation';
                default:
                    return 'tool-type-system';
            }
        }
    </script>
</body>
</html>
