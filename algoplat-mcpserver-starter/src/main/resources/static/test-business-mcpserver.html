<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务场景和McpServer修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <!-- 引入jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <!-- 引入修复脚本 -->
    <script src="/js/business-mcpserver-fix.js"></script>

    <div class="container">
        <h1>业务场景和McpServer修复效果测试</h1>

        <div class="test-section">
            <h3>修复状态检查</h3>
            <div id="fixStatus" class="status info">正在检查修复状态...</div>
            <button onclick="checkFixStatus()">重新检查</button>
        </div>

        <div class="test-grid">
            <div class="test-section">
                <h3>业务场景防重复提交测试</h3>
                <p>测试业务场景的创建、更新、删除操作的防重复提交机制</p>
                <button onclick="testBusinessLineOperations()">测试业务场景操作</button>
                <div id="businessLineTestResult" class="status info">点击按钮开始测试</div>
            </div>

            <div class="test-section">
                <h3>McpServer防重复提交测试</h3>
                <p>测试McpServer的创建、更新、删除操作的防重复提交机制</p>
                <button onclick="testMcpServerOperations()">测试McpServer操作</button>
                <div id="mcpServerTestResult" class="status info">点击按钮开始测试</div>
            </div>
        </div>

        <div class="test-section">
            <h3>控制台日志</h3>
            <div id="consoleLog" class="log">等待测试开始...</div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 日志记录函数
        function log(message) {
            const logDiv = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('consoleLog').innerHTML = '日志已清空<br>';
        }

        // 检查修复状态
        function checkFixStatus() {
            const statusDiv = document.getElementById('fixStatus');

            // 检查jQuery是否加载
            const hasJQuery = typeof $ !== 'undefined';

            // 检查修复脚本的功能是否可用
            const hasFixFunctions = window.businessMcpServerFix &&
                                  typeof window.businessMcpServerFix.isCreatingBusinessLine === 'function' &&
                                  typeof window.businessMcpServerFix.isCreatingMcpServer === 'function';

            if (hasJQuery && hasFixFunctions) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✓ 业务场景和McpServer修复脚本已加载并正常工作';
                log('修复脚本检查: 已加载并正常工作');
            } else if (hasJQuery && !hasFixFunctions) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '✗ jQuery已加载，但修复脚本功能不可用';
                log('修复脚本检查: 功能不可用');
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '✗ 缺少必要的依赖';
                log('修复脚本检查: 缺少依赖');
            }
        }

        // 测试业务场景操作
        function testBusinessLineOperations() {
            const resultDiv = document.getElementById('businessLineTestResult');
            resultDiv.className = 'status info';
            resultDiv.innerHTML = '正在测试业务场景操作...';

            log('=== 开始业务场景操作测试 ===');

            if (!window.businessMcpServerFix) {
                resultDiv.className = 'status error';
                resultDiv.innerHTML = '✗ 修复脚本不可用';
                log('业务场景测试失败: 修复脚本不可用');
                return;
            }

            let testResults = {
                create: 0,
                update: 0,
                delete: 0
            };

            // 测试创建业务场景
            log('测试创建业务场景防重复提交...');
            window.businessMcpServerFix.setCreatingBusinessLine(false);

            for (let i = 0; i < 5; i++) {
                if (!window.businessMcpServerFix.isCreatingBusinessLine()) {
                    window.businessMcpServerFix.setCreatingBusinessLine(true);
                    testResults.create++;
                    log(`创建业务场景测试 ${i+1}: 请求已发送`);
                } else {
                    log(`创建业务场景测试 ${i+1}: 被防重复提交机制阻止`);
                }
            }

            // 重置状态
            setTimeout(() => {
                window.businessMcpServerFix.setCreatingBusinessLine(false);

                // 测试更新业务场景
                log('测试更新业务场景防重复提交...');
                for (let i = 0; i < 5; i++) {
                    if (!window.businessMcpServerFix.isUpdatingBusinessLine()) {
                        window.businessMcpServerFix.setUpdatingBusinessLine(true);
                        testResults.update++;
                        log(`更新业务场景测试 ${i+1}: 请求已发送`);
                    } else {
                        log(`更新业务场景测试 ${i+1}: 被防重复提交机制阻止`);
                    }
                }

                // 重置状态
                setTimeout(() => {
                    window.businessMcpServerFix.setUpdatingBusinessLine(false);

                    // 测试删除业务场景
                    log('测试删除业务场景防重复提交...');
                    for (let i = 0; i < 5; i++) {
                        if (!window.businessMcpServerFix.isDeletingBusinessLine()) {
                            window.businessMcpServerFix.setDeletingBusinessLine(true);
                            testResults.delete++;
                            log(`删除业务场景测试 ${i+1}: 请求已发送`);
                        } else {
                            log(`删除业务场景测试 ${i+1}: 被防重复提交机制阻止`);
                        }
                    }

                    // 重置状态并显示结果
                    setTimeout(() => {
                        window.businessMcpServerFix.setDeletingBusinessLine(false);

                        const allPassed = testResults.create === 1 && testResults.update === 1 && testResults.delete === 1;

                        if (allPassed) {
                            resultDiv.className = 'status success';
                            resultDiv.innerHTML = '✓ 业务场景防重复提交测试通过';
                            log('✓ 业务场景防重复提交测试通过');
                        } else {
                            resultDiv.className = 'status error';
                            resultDiv.innerHTML = `✗ 业务场景防重复提交测试失败 (创建:${testResults.create}, 更新:${testResults.update}, 删除:${testResults.delete})`;
                            log(`✗ 业务场景防重复提交测试失败`);
                        }

                        log('=== 业务场景操作测试完成 ===');
                    }, 50);
                }, 50);
            }, 50);
        }

        // 测试McpServer操作
        function testMcpServerOperations() {
            const resultDiv = document.getElementById('mcpServerTestResult');
            resultDiv.className = 'status info';
            resultDiv.innerHTML = '正在测试McpServer操作...';

            log('=== 开始McpServer操作测试 ===');

            if (!window.businessMcpServerFix) {
                resultDiv.className = 'status error';
                resultDiv.innerHTML = '✗ 修复脚本不可用';
                log('McpServer测试失败: 修复脚本不可用');
                return;
            }

            let testResults = {
                create: 0,
                update: 0,
                delete: 0
            };

            // 测试创建McpServer
            log('测试创建McpServer防重复提交...');
            window.businessMcpServerFix.setCreatingMcpServer(false);

            for (let i = 0; i < 5; i++) {
                if (!window.businessMcpServerFix.isCreatingMcpServer()) {
                    window.businessMcpServerFix.setCreatingMcpServer(true);
                    testResults.create++;
                    log(`创建McpServer测试 ${i+1}: 请求已发送`);
                } else {
                    log(`创建McpServer测试 ${i+1}: 被防重复提交机制阻止`);
                }
            }

            // 重置状态
            setTimeout(() => {
                window.businessMcpServerFix.setCreatingMcpServer(false);

                // 测试更新McpServer
                log('测试更新McpServer防重复提交...');
                for (let i = 0; i < 5; i++) {
                    if (!window.businessMcpServerFix.isUpdatingMcpServer()) {
                        window.businessMcpServerFix.setUpdatingMcpServer(true);
                        testResults.update++;
                        log(`更新McpServer测试 ${i+1}: 请求已发送`);
                    } else {
                        log(`更新McpServer测试 ${i+1}: 被防重复提交机制阻止`);
                    }
                }

                // 重置状态
                setTimeout(() => {
                    window.businessMcpServerFix.setUpdatingMcpServer(false);

                    // 测试删除McpServer
                    log('测试删除McpServer防重复提交...');
                    for (let i = 0; i < 5; i++) {
                        if (!window.businessMcpServerFix.isDeletingMcpServer()) {
                            window.businessMcpServerFix.setDeletingMcpServer(true);
                            testResults.delete++;
                            log(`删除McpServer测试 ${i+1}: 请求已发送`);
                        } else {
                            log(`删除McpServer测试 ${i+1}: 被防重复提交机制阻止`);
                        }
                    }

                    // 重置状态并显示结果
                    setTimeout(() => {
                        window.businessMcpServerFix.setDeletingMcpServer(false);

                        const allPassed = testResults.create === 1 && testResults.update === 1 && testResults.delete === 1;

                        if (allPassed) {
                            resultDiv.className = 'status success';
                            resultDiv.innerHTML = '✓ McpServer防重复提交测试通过';
                            log('✓ McpServer防重复提交测试通过');
                        } else {
                            resultDiv.className = 'status error';
                            resultDiv.innerHTML = `✗ McpServer防重复提交测试失败 (创建:${testResults.create}, 更新:${testResults.update}, 删除:${testResults.delete})`;
                            log(`✗ McpServer防重复提交测试失败`);
                        }

                        log('=== McpServer操作测试完成 ===');
                    }, 50);
                }, 50);
            }, 50);
        }

        // 页面加载完成后自动检查修复状态
        window.addEventListener('load', function() {
            setTimeout(checkFixStatus, 1000);
            log('业务场景和McpServer测试页面加载完成');
        });
    </script>
</body>
</html>

