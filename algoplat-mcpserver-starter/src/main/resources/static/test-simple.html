<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>简单修复测试</title>
</head>
<body>
    <h1>修复脚本测试</h1>
    <div id="status">检查中...</div>
    <button onclick="testFix()">测试修复功能</button>
    <div id="result"></div>

    <!-- 引入jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <!-- 引入修复脚本 -->
    <script src="/js/tool-manager-fix.js"></script>

    <script>
        function checkStatus() {
            const statusDiv = document.getElementById('status');

            if (typeof $ !== 'undefined') {
                statusDiv.innerHTML = '✓ jQuery已加载';

                if (window.toolManagerFix) {
                    statusDiv.innerHTML += '<br>✓ 修复脚本已加载';

                    if (typeof window.toolManagerFix.isCreating === 'function' &&
                        typeof window.toolManagerFix.isUpdating === 'function' &&
                        typeof window.toolManagerFix.isDeleting === 'function') {
                        statusDiv.innerHTML += '<br>✓ 修复功能正常（包含创建、更新、删除）';
                    } else {
                        statusDiv.innerHTML += '<br>✗ 修复功能异常';
                    }
                } else {
                    statusDiv.innerHTML += '<br>✗ 修复脚本未加载';
                }
            } else {
                statusDiv.innerHTML = '✗ jQuery未加载';
            }
        }

        function testFix() {
            const resultDiv = document.getElementById('result');

            if (window.toolManagerFix) {
                // 确保初始状态为false
                window.toolManagerFix.setUpdating(false);

                // 测试防重复提交（同步执行，模拟快速连续点击）
                let testCount = 0;

                for (let i = 0; i < 5; i++) {
                    if (!window.toolManagerFix.isUpdating()) {
                        window.toolManagerFix.setUpdating(true);
                        testCount++;
                    }
                }

                // 重置状态
                setTimeout(() => {
                    window.toolManagerFix.setUpdating(false);
                }, 50);

                resultDiv.innerHTML = `测试结果: ${testCount === 1 ? '✓ 防重复提交正常' : '✗ 防重复提交异常，执行了' + testCount + '次'}`;
            } else {
                resultDiv.innerHTML = '✗ 修复脚本不可用';
            }
        }

        // 页面加载完成后检查状态
        setTimeout(checkStatus, 1000);
    </script>
</body>
</html>

