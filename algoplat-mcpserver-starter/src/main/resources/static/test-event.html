<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>事件绑定测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 8px 16px; margin: 5px; }
        .log { background: #f8f9fa; padding: 10px; border: 1px solid #ddd; height: 200px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <h1>事件绑定测试</h1>

    <div id="status" class="status info">检查中...</div>

    <button onclick="testEventBinding()">测试事件绑定</button>
    <button onclick="clearLog()">清空日志</button>

    <h3>测试日志</h3>
    <div id="log" class="log"></div>

    <!-- 引入jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <!-- 引入修复脚本 -->
    <script src="/js/tool-manager-fix.js"></script>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function checkStatus() {
            const statusDiv = document.getElementById('status');

            if (typeof $ !== 'undefined' && window.toolManagerFix) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✓ jQuery和修复脚本都已加载';
                log('初始化完成：jQuery和修复脚本都已加载');
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '✗ 缺少必要的依赖';
                log('初始化失败：缺少必要的依赖');
            }
        }

        function testEventBinding() {
            log('=== 开始事件绑定测试 ===');

            // 创建测试按钮
            const testBtn = $('<button class="test-event-btn" data-test="true">测试按钮</button>');
            $('body').append(testBtn);
            log('创建了测试按钮');

            let clickCount = 0;

            // 绑定事件处理器
            const handler = function(e) {
                e.preventDefault();
                clickCount++;
                log(`事件处理器被触发，第${clickCount}次`);
            };

            // 使用事件委托绑定事件
            $(document).on('click', '.test-event-btn', handler);
            log('绑定了第1个事件处理器');

            // 再次绑定相同的事件（模拟重复绑定问题）
            $(document).on('click', '.test-event-btn', handler);
            log('绑定了第2个事件处理器（重复绑定）');

            // 模拟点击
            setTimeout(() => {
                log('模拟点击测试按钮');
                testBtn.trigger('click');

                setTimeout(() => {
                    // 清理
                    $(document).off('click', '.test-event-btn', handler);
                    testBtn.remove();

                    // 检查结果
                    if (clickCount === 1) {
                        log('✓ 测试通过：事件只被触发1次');
                        document.getElementById('status').className = 'status success';
                        document.getElementById('status').innerHTML = '✓ 事件绑定测试通过';
                    } else {
                        log(`✗ 测试失败：事件被触发${clickCount}次（应该只有1次）`);
                        document.getElementById('status').className = 'status error';
                        document.getElementById('status').innerHTML = `✗ 事件绑定测试失败（触发${clickCount}次）`;
                    }

                    log('=== 事件绑定测试完成 ===');
                }, 100);
            }, 100);
        }

        // 页面加载完成后检查状态
        setTimeout(checkStatus, 1000);
    </script>
</body>
</html>

