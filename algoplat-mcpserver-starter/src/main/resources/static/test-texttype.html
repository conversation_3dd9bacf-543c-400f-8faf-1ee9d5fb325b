<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TextType 字段测试</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>TextType 字段测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>测试表单</h3>
                <form id="testForm">
                    <div class="form-group">
                        <label>文本类型选择</label>
                        <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                            <label class="btn btn-outline-primary active" for="plainTextType">
                                <input type="radio" name="textType" id="plainTextType" value="2" checked>
                                普通文本
                            </label>
                            <label class="btn btn-outline-primary" for="richTextType">
                                <input type="radio" name="textType" id="richTextType" value="1">
                                富文本
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group" id="plainTextEditor">
                        <label>普通文本描述</label>
                        <textarea class="form-control" id="toolDescriptionPlain" rows="4" placeholder="请输入普通文本描述...">这是普通文本描述</textarea>
                    </div>
                    
                    <div class="form-group" id="richTextEditor" style="display: none;">
                        <label>富文本描述</label>
                        <textarea class="summernote" id="toolDescription"></textarea>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="testTextTypeRetrieval()">测试获取 TextType</button>
                    <button type="button" class="btn btn-success" onclick="testCreateToolData()">测试创建工具数据</button>
                </form>
            </div>
            
            <div class="col-md-6">
                <h3>测试结果</h3>
                <div id="testResults"></div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // 初始化Summernote编辑器
            $('#toolDescription').summernote({
                height: 150,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ]
            });
            
            // 设置默认富文本内容
            $('#toolDescription').summernote('code', '<p>这是<strong>富文本</strong>描述</p>');
            
            // 文本类型切换事件
            $('input[name="textType"]').on('change', function() {
                const textType = $(this).val();
                const $richEditor = $('#richTextEditor');
                const $plainEditor = $('#plainTextEditor');
                
                if (textType === '1') {
                    // 切换到富文本
                    $plainEditor.fadeOut(200, function() {
                        $richEditor.fadeIn(200);
                    });
                } else {
                    // 切换到普通文本
                    $richEditor.fadeOut(200, function() {
                        $plainEditor.fadeIn(200);
                    });
                }
            });
        });
        
        function addResult(message, type) {
            const resultDiv = $('<div>').addClass('test-result ' + type).text(message);
            $('#testResults').append(resultDiv);
        }
        
        function clearResults() {
            $('#testResults').empty();
        }
        
        function testTextTypeRetrieval() {
            clearResults();
            
            // 测试获取 textType
            const textType = $('input[name="textType"]:checked').val() || '2';
            addResult('获取到的 textType: ' + textType, 'info');
            
            // 测试获取描述内容
            let toolDescription;
            if (textType === '1') {
                toolDescription = $('#toolDescription').summernote('code');
                addResult('富文本内容: ' + toolDescription, 'info');
            } else {
                toolDescription = $('#toolDescriptionPlain').val();
                addResult('普通文本内容: ' + toolDescription, 'info');
            }
            
            addResult('✓ TextType 获取测试完成', 'success');
        }
        
        function testCreateToolData() {
            clearResults();
            
            // 模拟 createToolWithCallback 函数中的数据构建逻辑
            const textType = $('input[name="textType"]:checked').val() || '2';
            
            let toolDescription;
            if (textType === '1') {
                toolDescription = $('#toolDescription').summernote('code');
            } else {
                toolDescription = $('#toolDescriptionPlain').val();
            }
            
            // 构建工具数据
            let toolData = {
                mcpServerId: 60,
                name: 'TestTool',
                type: 'HTTP',
                description: toolDescription,
                owner: 'TestUser',
                timeOut: '6000',
                textType: Number(textType)
            };
            
            addResult('构建的工具数据:', 'info');
            addResult(JSON.stringify(toolData, null, 2), 'info');
            
            // 检查 textType 字段
            if (toolData.textType) {
                addResult('✓ textType 字段存在: ' + toolData.textType, 'success');
            } else {
                addResult('✗ textType 字段缺失', 'error');
            }
            
            // 检查 description 字段
            if (toolData.description && toolData.description.trim() !== '') {
                addResult('✓ description 字段存在且不为空', 'success');
            } else {
                addResult('✗ description 字段缺失或为空', 'error');
            }
            
            addResult('✓ 创建工具数据测试完成', 'success');
        }
    </script>
</body>
</html>
