/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.container-fluid {
    padding-top: 20px;
    padding-bottom: 40px;
}

/* 卡片样式 */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    transition: all 0.2s;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: #3498db; /* 蓝色 */
    border-color: #3498db;
}

.btn-primary:hover {
    background-color: #2980b9; /* 深蓝色 */
    border-color: #2980b9;
}

.btn-success {
    background-color: #2ecc71; /* 绿色 */
    border-color: #2ecc71;
}

.btn-success:hover {
    background-color: #27ae60; /* 深绿色 */
    border-color: #27ae60;
}

.btn-danger {
    background-color: #e74c3c; /* 红色 */
    border-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b; /* 深红色 */
    border-color: #c0392b;
}

.btn-info {
    background-color: #1abc9c; /* 青色 */
    border-color: #1abc9c;
}

.btn-info:hover {
    background-color: #16a085; /* 深青色 */
    border-color: #16a085;
}

.btn-outline-info {
    color: #1abc9c;
    border-color: #1abc9c;
}

.btn-outline-info:hover {
    background-color: #1abc9c;
    border-color: #1abc9c;
    color: white;
}

.btn-outline-primary {
    color: #3498db;
    border-color: #3498db;
}

.btn-outline-primary:hover {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
}

.btn-outline-secondary {
    color: #7f8c8d;
    border-color: #7f8c8d;
}

.btn-outline-secondary:hover {
    background-color: #7f8c8d;
    border-color: #7f8c8d;
    color: white;
}

.btn-outline-warning {
    color: #e67e22;
    border-color: #e67e22;
}

.btn-outline-warning:hover {
    background-color: #e67e22;
    border-color: #e67e22;
    color: white;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
}

.badge-success {
    background-color: #28a745;
}

.badge-warning {
    background-color: #ffc107;
}

.badge-danger {
    background-color: #dc3545;
}

.badge-info {
    background-color: #17a2b8;
}

/* 工具卡片样式 */
.tool-card {
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tool-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.tool-card .card-title {
    margin-bottom: 0;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
}

.tool-card .full-description {
    display: none;
}

/* 业务线和McpServer卡片样式 */
.business-card, .mcpserver-card {
    margin-bottom: 20px;
}

.business-card .card-header, .mcpserver-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.business-card .card-title, .mcpserver-card .card-title {
    margin-bottom: 0;
    font-weight: 600;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
}

/* 表单样式 */
.form-group label {
    font-weight: 500;
    color: #495057;
}

.form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 富文本编辑器样式 */
.note-editor.note-frame {
    border-radius: 4px;
    border: 1px solid #ced4da;
}

.note-editor.note-frame .note-statusbar {
    border-top: 1px solid #ced4da;
}

/* 参数列表样式 */
.param-list {
    margin-top: 15px;
}

.param-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.param-item .form-row {
    margin-bottom: 10px;
}

/* 页脚样式 */
.footer {
    background-color: #343a40;
    color: #adb5bd;
    padding: 20px 0;
    margin-top: 30px;
    border-top: 1px solid #e9ecef;
    color: #6c757d;
}

/* 工具类型标签样式 */
.tool-type {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    color: white;
    margin-right: 8px;
}

.tool-type-http {
    background-color: #17a2b8;
}

.tool-type-thrift {
    background-color: #007bff;
}

.tool-type-pigeon {
    background-color: #ffc107;
    color: #212529;
}

.tool-type-system {
    background-color: #7f8c8d;
}

.tool-type-systemdefault {
    background-color: #2c3e50; /* 深蓝灰色 */
}

.tool-type-toolannotation {
    background-color: #27ae60; /* 绿色 */
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background-color: #28a745;
}

.status-inactive {
    background-color: #dc3545;
}

/* 工具描述悬停效果 */
.tool-description {
    position: relative;
}

.tool-description .full-description {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    width: 300px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tool-description:hover .full-description {
    display: block;
}

/* 分页样式 */
.pagination {
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-item .page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
}

/* 加载指示器 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 提示框样式 */
.tooltip-inner {
    max-width: 300px;
    padding: 10px;
    background-color: #343a40;
    font-size: 14px;
}

/* 通用样式 */
body {
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.required-field::after {
    content: " *";
    color: #e74c3c;
}

/* 应用容器 */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    background-color: #2c3e50; /* 深蓝灰色 */
    color: white;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    z-index: 1020;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 70px; /* 顶部导航栏高度 */
    left: 0;
    bottom: 0;
    overflow-y: auto;
}

.sidebar-header {
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
    font-size: 0.9rem;
    margin-bottom: 0;
    font-weight: 600;
}

.sidebar-search {
    padding: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-search input {
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    font-size: 0.9rem;
}

.sidebar-search input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.sidebar-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.sidebar-item {
    padding: 10px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
    font-size: 0.9rem;
}

.sidebar-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-item.active {
    background-color: rgba(255, 255, 255, 0.15);
    font-weight: 600;
    border-left: 3px solid #3498db;
}

.sidebar-item-actions {
    display: none;
}

.sidebar-item:hover .sidebar-item-actions {
    display: block;
}

.sidebar-item-actions button {
    background: none;
    border: none;
    color: white;
    margin-left: 5px;
    padding: 0;
    font-size: 12px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 0;
    overflow-y: auto;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
    margin-left: 250px; /* 侧边栏宽度 */
}

.content-area {
    padding: 20px;
    flex: 1;
}

/* 顶部导航栏 */
.navbar {
    border-bottom: 1px solid #e9ecef;
    padding: 0.5rem 1rem;
    background-color: #fff !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 70px; /* 顶部导航栏高度 */
    z-index: 1010;
}

.navbar-brand {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    padding: 0.5rem 0;
    margin-right: 2rem;
}

.breadcrumb {
    margin-bottom: 0;
    padding: 0.5rem 0;
    font-size: 0.9rem;
}

/* 欢迎页面 */
.welcome-page {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.jumbotron {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

/* McpServer卡片样式 */
.mcpserver-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    background-color: #fff;
    height: 100%;
}

.mcpserver-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.mcpserver-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px;
    border-radius: 8px 8px 0 0;
}

.mcpserver-card .card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    font-weight: 600;
}

.mcpserver-card .card-body {
    padding: 15px;
}

.mcpserver-card .card-body > div {
    margin-bottom: 12px;
}

.mcpserver-card .card-body > div:last-child {
    margin-bottom: 0;
}

.mcpserver-card .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 12px 15px;
    border-radius: 0 0 8px 8px;
}

.mcpserver-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 10px;
}

.mcpserver-status-active {
    background-color: #27ae60; /* 绿色 */
    color: white;
}

.mcpserver-status-inactive {
    background-color: #f39c12; /* 橙色 */
    color: white;
}

/* 工具卡片样式 */
.tool-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    background-color: #fff;
    height: 100%;
}

.tool-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.tool-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px;
    border-radius: 8px 8px 0 0;
}

.tool-card .card-title {
    margin-bottom: 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    font-weight: 600;
}

.tool-card .card-body {
    padding: 15px;
    flex: 1;
}

.tool-card .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 12px 15px;
    border-radius: 0 0 8px 8px;
}

.tool-card .full-description {
    display: none;
}

/* 工具类型标签 */
.tool-type {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    margin-right: 8px;
}

.tool-type-http {
    background-color: #3498db; /* 蓝色 */
}

.tool-type-thrift {
    background-color: #9b59b6; /* 紫色 */
}

.tool-type-pigeon {
    background-color: #e67e22; /* 橙色 */
}

.tool-type-system {
    background-color: #7f8c8d; /* 灰色 */
}

.tool-type-systemdefault {
    background-color: #2c3e50; /* 深蓝灰色 */
}

.tool-type-toolannotation {
    background-color: #27ae60; /* 绿色 */
}

/* 工具筛选区域 */
.tool-filters {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 参数项样式 */
.param-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

/* 请求头项样式 */
.header-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
}

/* 工具详情样式 */
.tool-details {
    padding: 10px;
}

.tool-details table {
    margin-bottom: 0;
}

/* 分页样式 */
.pagination {
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-item .page-link {
    color: #3498db;
    border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #3498db;
    border-color: #3498db;
    color: #fff;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
}

/* 工具预览样式 */
.tool-preview {
    margin-top: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 8px;
    border: 1px solid #e9ecef;
}

.tool-preview-item {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.tool-preview-item:last-child {
    border-bottom: none;
}

.access-info {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    margin-bottom: 10px;
    font-family: monospace;
    font-size: 0.85rem;
    word-break: break-all;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
    }
    .sidebar {
        width: 100%;
        height: auto;
    }
    .main-content {
        width: 100%;
    }
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    .card-header .btn-group {
        margin-top: 10px;
    }
    .mcpserver-card .card-body > div {
        margin-bottom: 15px;
    }
    .tool-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    .tool-filters .btn-group,
    .tool-filters select {
        margin-bottom: 10px;
        width: 100%;
    }
}

/* 页脚样式 */
.footer {
    padding: 15px 0;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

.footer p {
    margin-bottom: 0;
    font-size: 0.85rem;
    color: #6c757d;
}

/* Avitor脚本编辑器样式 */
.CodeMirror {
    border: 1px solid #ddd;
    border-radius: 4px;
    height: 200px !important;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 14px;
    margin-bottom: 10px;
    width: 100%;
}

.CodeMirror-focused {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 确保编辑器在模态框中正确显示 */
.modal-body .CodeMirror {
    max-height: 200px;
    min-height: 200px;
}

/* 编辑器标签样式 */
.form-group label[for^="thriftAvitorScript"],
.form-group label[for^="pigeonAvitorScript"],
.form-group label[for^="updateThriftAvitorScript"],
.form-group label[for^="updatePigeonAvitorScript"] {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.form-group label[for^="thriftAvitorScript"]::before,
.form-group label[for^="pigeonAvitorScript"]::before,
.form-group label[for^="updateThriftAvitorScript"]::before,
.form-group label[for^="updatePigeonAvitorScript"]::before {
    content: "\f121"; /* FontAwesome code icon */
    font-family: FontAwesome;
    margin-right: 8px;
    color: #007bff;
}

/* 工具详情中的代码显示 */
.tool-details pre {
    background-color: #272822;
    color: #f8f8f2;
    padding: 15px;
    border-radius: 4px;
    overflow: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 14px;
    line-height: 1.5;
    max-height: 300px;
}

/* 确保textarea在CodeMirror初始化前也有合理的高度 */
textarea.avitor-script {
    min-height: 200px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
    font-size: 14px;
}
