/* Toast 样式 */
.toast-container {
    z-index: 9999 !important;
}

.toast {
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast-body {
    font-size: 14px;
    padding: 12px 15px;
}

/* Toast 背景色 */
.bg-success {
    background-color: #28a745 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

/* Toast 动画 */
.toast.showing {
    opacity: 0;
}

.toast.show {
    opacity: 1;
    transition: opacity 0.15s linear;
}

.toast.hide {
    display: none;
}

/* 浮动提示框样式 */
.alert-float {
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 999999 !important;
    min-width: 300px !important;
    padding: 15px 20px !important;
    border-radius: 4px !important;
    text-align: center !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25), 0 4px 8px rgba(0, 0, 0, 0.15) !important;  /* 更强的阴影效果 */
    font-size: 14px !important;
    line-height: 1.5 !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: none !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
}

/* 提示框类型样式 */
.alert-float.alert-success {
    background-color: rgba(40, 167, 69, 0.95) !important;
    color: #fff !important;
    border: 1px solid #1e7e34 !important;
}

.alert-float.alert-danger {
    background-color: rgba(220, 53, 69, 0.95) !important;
    color: #fff !important;
    border: 1px solid #bd2130 !important;
}

.alert-float.alert-warning {
    background-color: rgba(255, 193, 7, 0.95) !important;
    color: #000 !important;
    border: 1px solid #d39e00 !important;
}

.alert-float.alert-info {
    background-color: rgba(23, 162, 184, 0.95) !important;
    color: #fff !important;
    border: 1px solid #138496 !important;
}

/* 提示框文本样式 */
.alert-float .alert-message {
    display: inline-block !important;
    vertical-align: middle !important;
    color: inherit !important;
}

/* 关闭按钮样式 */
.alert-float .btn-close {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: currentColor;
    opacity: 0.8;
}

/* 动画效果 */
@keyframes slideInDown {
    from {
        transform: translate(-50%, -100%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

.alert-float {
    animation: slideInDown 0.3s ease-out !important;
}

/* 淡出动画 */
.alert-float.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* 确保提示框在最顶层 */
.modal-backdrop {
    z-index: 1040 !important;
}

.modal {
    z-index: 1050 !important;
}

.alert-float {
    z-index: 999999 !important;
}

/* 提示容器样式 */
#alertContainer {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999999 !important; /* 提高 z-index，确保高于模态框 */
    width: auto;
    min-width: 300px;
    pointer-events: none;
}

/* 提示框样式 */
.alert-float {
    margin-bottom: 10px;
    padding: 15px 20px;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    line-height: 1.5;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 9999999 !important; /* 确保提示框本身也有高 z-index */
}

/* 提示框类型样式 */
.alert-float.alert-success {
    background-color: rgba(40, 167, 69, 0.95) !important;
    color: #fff !important;
    border: 1px solid #1e7e34 !important;
}

.alert-float.alert-danger {
    background-color: rgba(220, 53, 69, 0.95) !important;
    color: #fff !important;
    border: 1px solid #bd2130 !important;
}

.alert-float.alert-warning {
    background-color: rgba(255, 193, 7, 0.95) !important;
    color: #000 !important;
    border: 1px solid #d39e00 !important;
}

.alert-float.alert-info {
    background-color: rgba(23, 162, 184, 0.95) !important;
    color: #fff !important;
    border: 1px solid #138496 !important;
}

/* 提示框文本样式 */
.alert-float .alert-message {
    display: inline-block !important;
    vertical-align: middle !important;
    color: inherit !important;
}

/* 动画效果 */
@keyframes slideInDown {
    from {
        transform: translate(-50%, -100%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

.alert-float {
    animation: slideInDown 0.3s ease-out !important;
}

/* 模态框样式覆盖 */
.modal {
    z-index: 1050 !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}
