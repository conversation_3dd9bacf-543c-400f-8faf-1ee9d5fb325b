/**
 * Avitor脚本编辑器初始化
 */
$(document).ready(function() {
    console.log("Avitor脚本编辑器初始化开始");

    // 初始化所有Avitor脚本编辑器
    setTimeout(function() {
        initAvitorScriptEditors();
    }, 500);

    // 监听模态框显示事件，确保编辑器正确渲染
    $('#createToolModal').on('shown.bs.modal', function() {
        console.log("创建工具模态框显示，初始化编辑器");
        setTimeout(function() {
            initAvitorScriptEditors();
        }, 300);
    });

    $('#updateToolModal').on('shown.bs.modal', function() {
        console.log("更新工具模态框显示，初始化编辑器");
        setTimeout(function() {
            initAvitorScriptEditors();
        }, 300);
    });

    // 工具类型切换时，重新初始化编辑器
    $('#toolType').on('change', function() {
        console.log("工具类型切换为:", $(this).val());
        setTimeout(function() {
            initAvitorScriptEditors();
        }, 300);
    });

    $('#updateToolType').on('change', function() {
        console.log("更新工具类型切换为:", $(this).val());
        setTimeout(function() {
            initAvitorScriptEditors();
        }, 300);
    });
});

// 编辑器实例映射
const editorInstances = {};

/**
 * 初始化所有Avitor脚本编辑器
 */
function initAvitorScriptEditors() {
    console.log("初始化Avitor脚本编辑器");

    // 创建工具表单中的编辑器
    initEditor('thriftAvitorScript');
    initEditor('pigeonAvitorScript');
    // 更新工具表单中的编辑器
    initEditor('updateThriftAvitorScript');
    initEditor('updatePigeonAvitorScript');
}

/**
 * 初始化单个编辑器
 * @param {string} elementId 编辑器元素ID
 */
function initEditor(elementId) {
    console.log("尝试初始化编辑器:", elementId);
    const element = document.getElementById(elementId);
    if (!element) {
        console.log("未找到元素:", elementId);
        return;
    }

    console.log("找到元素:", elementId);
    // 如果已经初始化过，则销毁旧实例
    if (editorInstances[elementId]) {
        console.log("销毁旧的编辑器实例:", elementId);
        editorInstances[elementId].toTextArea();
        delete editorInstances[elementId];
    }

    try {
        // 创建新的编辑器实例
        console.log("创建新的编辑器实例:", elementId);
        const editor = CodeMirror.fromTextArea(element, {
            mode: "javascript",
            theme: "monokai",
            lineNumbers: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            indentUnit: 4,
            tabSize: 4,
            indentWithTabs: false,
            lineWrapping: true
        });

        // 设置编辑器高度
        editor.setSize(null, 200);

        // 保存实例引用
        editorInstances[elementId] = editor;

        // 当编辑器内容变化时，更新原始textarea的值
        editor.on("change", function() {
            editor.save();
        });

        console.log("编辑器初始化成功:", elementId);
    } catch (error) {
        console.error("初始化编辑器失败:", elementId, error);
    }
}

/**
 * 获取编辑器内容
 * @param {string} elementId 编辑器元素ID
 * @returns {string} 编辑器内容
 */
function getEditorContent(elementId) {
    console.log("获取编辑器内容:", elementId);
    if (editorInstances[elementId]) {
        return editorInstances[elementId].getValue();
    }
    return document.getElementById(elementId)?.value || '';
}

/**
 * 设置编辑器内容
 * @param {string} elementId 编辑器元素ID
 * @param {string} content 要设置的内容
 */
function setEditorContent(elementId, content) {
    console.log("设置编辑器内容:", elementId);
    if (editorInstances[elementId]) {
        editorInstances[elementId].setValue(content || '');
    } else {
        const element = document.getElementById(elementId);
        if (element) {
            element.value = content || '';
        }
    }
}
