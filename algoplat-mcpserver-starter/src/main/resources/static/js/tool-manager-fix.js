/**
 * 工具管理修复脚本
 * 解决重复提交和事件绑定问题
 */

    console.log("tool-manager-fix.js loaded version", "v2025-08-20-1");
$(document).ready(function() {
    // 防重复提交的状态管理
    let isCreating = false;
    let isUpdating = false;
    let isDeleting = false;

    // 将状态变量暴露到全局作用域，用于测试
    window.toolManagerFix = {
        isCreating: function() { return isCreating; },
        isUpdating: function() { return isUpdating; },
        isDeleting: function() { return isDeleting; },
        setCreating: function(value) { isCreating = value; },
        setUpdating: function(value) { isUpdating = value; },
        setDeleting: function(value) { isDeleting = value; }
    };

    // 解绑所有可能重复的事件绑定
    function unbindDuplicateEvents() {
        // 解绑创建工具表单提交事件
        $('#createToolForm').off('submit');
        $('#createToolModal .modal-footer button[type="submit"]').off('click');

        // 解绑编辑工具按钮事件
        $('.edit-tool-btn').off('click');
        $(document).off('click', '.edit-tool-btn');

        // 解绑删除工具按钮事件
        $('.delete-tool-btn').off('click');
        $(document).off('click', '.delete-tool-btn');

        // 解绑更新工具表单提交事件
        $('#updateToolForm').off('submit');
        $('#updateToolModal .modal-footer button[type="submit"]').off('click');

        // 解绑删除确认按钮事件
        $('.confirm-delete-tool-btn').off('click');
    }

    // 重新绑定事件（只绑定一次）
    function bindToolEvents() {
        // 绑定创建工具表单提交事件
        $('#createToolForm').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isCreating) {
                console.log("正在创建工具中，忽略重复提交");
                return false;
            }

            console.log("创建工具表单提交事件触发");

            // 验证工具描述
            const textType = $('input[name="textType"]:checked').val() || '2';
            let toolDescription;

            console.log("tool-manager-fix - 当前选择的文本类型:", textType);

            if (textType === '1') {
                // 富文本
                toolDescription = $('#toolDescription').summernote('code');
                console.log("tool-manager-fix - 富文本内容:", toolDescription);

                const text = $('<div>').html(toolDescription).text().trim();
                if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                    showAlert('请填写工具描述', 'warning');
                    $('#toolDescription').closest('.form-group').addClass('has-error');
                    $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                    return false;
                }
            } else {
                // 普通文本（默认）
                toolDescription = $('#toolDescriptionPlain').val();
                console.log("tool-manager-fix - 普通文本内容:", toolDescription);

                if (!toolDescription || toolDescription.trim() === '') {
                    showAlert('请填写工具描述', 'warning');
                    $('#toolDescriptionPlain').addClass('is-invalid');
                    return false;
                }
            }

            console.log("tool-manager-fix - 最终工具描述:", toolDescription);

            // 设置创建状态
            isCreating = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $('#createToolModal .modal-footer button[type="submit"]');
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 创建中...');

            // 调用创建函数
            createToolWithCallback(function(success) {
                // 恢复按钮状态
                isCreating = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#createToolModal').modal('hide');
                    $('#createToolForm')[0].reset();
                    // 重置富文本编辑器
                    if ($('#toolDescription').summernote) {
                        $('#toolDescription').summernote('code', '');
                    }
                }
            });

            return false;
        });

        // 绑定创建工具提交按钮点击事件（先解绑之前的事件）
        $('#createToolModal .modal-footer button[type="submit"]').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isCreating) {
                console.log("正在创建工具中，忽略重复点击");
                return false;
            }

            console.log("创建工具提交按钮点击事件触发");

            // 获取文本类型和验证工具描述
            const textType = $('input[name="textType"]:checked').val() || '2';
            let toolDescription;

            console.log("提交按钮 - 当前选择的文本类型:", textType);

            if (textType === '1') {
                // 富文本
                toolDescription = $('#toolDescription').summernote('code');
                console.log("提交按钮 - 富文本内容:", toolDescription);

                // 富文本验证 - 移除HTML标签后检查是否为空
                const text = $('<div>').html(toolDescription).text().trim();
                if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                    showAlert('请填写工具描述', 'warning');
                    $('#toolDescription').closest('.form-group').addClass('has-error');
                    $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                    return false;
                }
            } else {
                // 普通文本（默认）
                toolDescription = $('#toolDescriptionPlain').val();
                console.log("提交按钮 - 普通文本内容:", toolDescription);
                console.log("提交按钮 - 普通文本框是否存在:", $('#toolDescriptionPlain').length);
                console.log("提交按钮 - 普通文本框是否可见:", $('#toolDescriptionPlain').is(':visible'));
                console.log("提交按钮 - 普通文本框父容器是否可见:", $('#toolDescriptionPlain').parent().is(':visible'));

                // 普通文本验证
                if (!toolDescription || toolDescription.trim() === '') {
                    console.log("普通文本验证失败 - 内容为空");
                    showAlert('请填写工具描述', 'warning');
                    $('#toolDescriptionPlain').addClass('is-invalid');
                    return false;
                } else {
                    console.log("普通文本验证通过 - 内容:", toolDescription);
                }
            }

            console.log("提交按钮 - 最终工具描述:", toolDescription);
            console.log("提交按钮 - 文本类型:", textType);

            // 设置创建状态
            isCreating = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $(this);
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 创建中...');

            // 调用创建函数
            createToolWithCallback(function(success) {
                // 恢复按钮状态
                isCreating = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#createToolModal').modal('hide');
                    $('#createToolForm')[0].reset();
                    // 重置富文本编辑器
                    if ($('#toolDescription').summernote) {
                        $('#toolDescription').summernote('code', '');
                    }
                }
            });

            return false;
        });

        // 使用事件委托绑定编辑工具按钮
        $(document).on('click', '.edit-tool-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 检查按钮是否被禁用
            if ($(this).prop('disabled') || $(this).hasClass('disabled')) {
                return false;
            }

            const mcpServerId = $(this).data('mcpserver-id');
            const toolName = $(this).data('name');

            console.log("编辑工具 - mcpServerId:", mcpServerId, "toolName:", toolName);

            if (mcpServerId && toolName) {
                loadToolForEdit(mcpServerId, toolName);
            }

            return false;
        });

        // 使用事件委托绑定删除工具按钮
        $(document).on('click', '.delete-tool-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 检查按钮是否被禁用
            if ($(this).prop('disabled') || $(this).hasClass('disabled')) {
                return false;
            }

            const mcpServerId = $(this).data('mcpserver-id');
            const toolName = $(this).data('name');
            const toolType = $(this).data('type');
            const owner = $(this).data('owner');

            // 检查工具类型，确保只有ToolAnnotation类型工具不能被删除
            if (toolType === 'ToolAnnotation') {
                showAlert('ToolAnnotation类型工具不允许删除', 'warning');
                return false;
            }

            // 显示删除确认对话框
            $('#confirmDeleteToolModal .tool-name').text(toolName);
            $('#confirmDeleteToolModal .confirm-delete-tool-btn')
                .data('mcpserver-id', mcpServerId)
                .data('name', toolName)
                .data('type', toolType)
                .data('owner', owner);
            $('#confirmDeleteToolModal').modal('show');

            return false;
        });

        // 绑定更新工具表单提交事件（只绑定一次）
        $('#updateToolForm').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isUpdating) {
                console.log("正在更新中，忽略重复提交");
                return false;
            }

            console.log("=== 更新工具表单提交事件触发 ===");

            // 获取文本类型和验证工具描述
            const textType = $('input[name="updateTextType"]:checked').val() || '2';
            let toolDescription;

            console.log("表单提交 - 当前选择的文本类型:", textType);
            console.log("表单提交 - radio按钮数量:", $('input[name="updateTextType"]').length);
            console.log("表单提交 - 选中的radio按钮数量:", $('input[name="updateTextType"]:checked').length);

            if (textType === '1') {
                // 富文本
                toolDescription = $('#updateToolDescription').summernote('code');
                console.log("表单提交 - 富文本内容:", toolDescription);

                // 富文本验证 - 移除HTML标签后检查是否为空
                const text = $('<div>').html(toolDescription).text().trim();
                if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                    showAlert('请填写工具描述', 'warning');
                    $('#updateToolDescription').closest('.form-group').addClass('has-error');
                    $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                    return false;
                }
            } else {
                // 普通文本（默认）
                toolDescription = $('#updateToolDescriptionPlain').val();
                console.log("表单提交 - 普通文本内容:", toolDescription);
                console.log("表单提交 - 普通文本内容长度:", toolDescription ? toolDescription.length : 0);
                console.log("表单提交 - 普通文本框是否存在:", $('#updateToolDescriptionPlain').length);
                console.log("表单提交 - 普通文本框是否可见:", $('#updateToolDescriptionPlain').is(':visible'));
                console.log("表单提交 - 普通文本框父容器是否可见:", $('#updateToolDescriptionPlain').parent().is(':visible'));
                console.log("表单提交 - 普通文本框实际值:", $('#updateToolDescriptionPlain')[0] ? $('#updateToolDescriptionPlain')[0].value : 'DOM元素不存在');

                // 普通文本验证
                if (!toolDescription || toolDescription.trim() === '') {
                    console.log("表单提交 - 普通文本验证失败 - 内容为空");
                    console.log("表单提交 - 验证失败详情:", {
                        原始值: toolDescription,
                        是否为空: !toolDescription,
                        trim后是否为空: toolDescription ? toolDescription.trim() === '' : '原始值为空'
                    });
                    showAlert('请填写工具描述', 'warning');
                    $('#updateToolDescriptionPlain').addClass('is-invalid');
                    return false;
                } else {
                    console.log("表单提交 - 普通文本验证通过 - 内容:", toolDescription);
                }
            }

            console.log("表单提交 - 最终工具描述:", toolDescription);
            console.log("表单提交 - 文本类型:", textType);

            // 设置更新状态
            isUpdating = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $('#updateToolModal .modal-footer button[type="submit"]');
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 更新中...');

            // 调用更新函数
            updateToolWithCallback(function(success) {
                // 恢复按钮状态
                isUpdating = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#updateToolModal').modal('hide');
                }
            });

            return false;
        });

        // 绑定更新工具提交按钮点击事件
        $('#updateToolModal .modal-footer button[type="submit"]').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isUpdating) {
                console.log("正在更新工具中，忽略重复点击");
                return false;
            }

            console.log("🚨 tool-manager-fix.js - 更新工具提交按钮点击事件触发");

            try {
                // 获取文本类型并验证工具描述
                const textType = $('input[name="updateTextType"]:checked').val() || '2';
                let toolDescription;

                console.log("提交按钮 - 当前选择的文本类型:", textType);
                console.log("提交按钮 - radio按钮数量:", $('input[name="updateTextType"]').length);
                console.log("提交按钮 - 选中的radio按钮数量:", $('input[name="updateTextType"]:checked').length);
                console.log("提交按钮 - 普通文本框是否存在:", $('#updateToolDescriptionPlain').length);
                console.log("提交按钮 - 普通文本框是否可见:", $('#updateToolDescriptionPlain').is(':visible'));

            // 轻量检查 -> 统一在 updateToolWithCallback 内进行严格校验
            if (textType === '1') {
                toolDescription = $('#updateToolDescription').summernote('code');
                const text = $('<div>').html(toolDescription || '').text().trim();
                if (!text) {
                    console.warn('轻量校验：富文本描述为空');
                }
            } else {
                toolDescription = $('#updateToolDescriptionPlain').val();
                if (!toolDescription || toolDescription.trim() === '') {
                    console.warn('轻量校验：普通文本描述为空');
                }
            }

            // 设置更新状态
            isUpdating = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $(this);
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 更新中...');

            // 调用更新函数（严格校验在此函数中完成）
            updateToolWithCallback(function(success) {
                // 恢复按钮状态
                isUpdating = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#updateToolModal').modal('hide');
                }
            });

            return false;
            } catch (error) {
                console.error("🚨 tool-manager-fix.js - 更新工具按钮点击事件发生错误:", error);
                console.error("错误堆栈:", error.stack);
                showAlert('更新工具时发生错误: ' + error.message, 'danger');
                return false;
            }
        });

        // 绑定删除确认按钮事件
        $(document).on('click', '.confirm-delete-tool-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isDeleting) {
                console.log("正在删除中，忽略重复提交");
                return false;
            }

            const mcpServerId = $(this).data('mcpserver-id');
            const toolName = $(this).data('name');
            const toolType = $(this).data('type');
            const owner = $(this).data('owner');

            // 设置删除状态
            isDeleting = true;

            // 禁用删除按钮并显示loading状态
            const $deleteBtn = $(this);
            const originalText = $deleteBtn.text();
            $deleteBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 删除中...');

            // 调用删除函数
            deleteToolWithCallback(mcpServerId, toolName, toolType, owner, function(success) {
                // 恢复按钮状态
                isDeleting = false;
                $deleteBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#confirmDeleteToolModal').modal('hide');
                }
            });

            return false;
        });
    }

    // 带回调的更新工具函数
    function updateToolWithCallback(callback) {
        // 获取通用字段
        const toolId = $('#updateToolId').val();
        const mcpServerId = $('#updateToolMcpServerId').val();
        const toolType = $('#updateToolType').val();
        const toolName = $('#updateToolName').val().trim();
        let textType = $('input[name="updateTextType"]:checked').val();
        if (!textType) {
            // 兜底：根据按钮选中态推断，左侧普通文本按钮在HTML上是第一个
            const plainActive = $('#updatePlainTextType').closest('label').hasClass('active');
            textType = plainActive ? '2' : '1';
        }
        if (!textType) textType = '2'; // 仍无则默认为普通文本

        let toolDescription;
        if (textType === '1') {
            console.log('updateToolWithCallback: 使用富文本模式');
            toolDescription = $('#updateToolDescription').summernote('code');
        } else {
            console.log('updateToolWithCallback: 使用普通文本模式');
            toolDescription = $('#updateToolDescriptionPlain').val();
        }

        const toolOwner = $('#updateToolOwner').val().trim();
        const toolTimeout = $('#updateToolTimeout').val().trim() || '6000';

        console.log("更新工具 - textType:", textType);
        console.log("更新工具 - toolDescription:", toolDescription);

        // 验证必填字段
        if (!toolName) {
            showAlert('请填写工具名称', 'warning');
            callback(false);
            return;
        }

        if (!toolOwner) {
            showAlert('请填写负责人', 'warning');
            callback(false);
            return;
        }

        // 验证工具描述
        if (textType === '1') {
            // 富文本验证 - 移除HTML标签后检查是否为空
            const text = $('<div>').html(toolDescription).text().trim();
            if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                console.log("updateToolWithCallback - 富文本验证失败 - 内容为空");
                showAlert('请填写工具描述', 'warning');
                $('#updateToolDescription').closest('.form-group').addClass('has-error');
                $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                callback(false);
                return;
            } else {
                console.log("updateToolWithCallback - 富文本验证通过 - 内容:", toolDescription);
            }
        } else {
            // 普通文本验证
            if (!toolDescription || toolDescription.trim() === '') {
                console.log("updateToolWithCallback - 普通文本验证失败 - 内容为空");
                showAlert('请填写工具描述', 'warning');
                $('#updateToolDescriptionPlain').addClass('is-invalid');
                callback(false);
                return;
            } else {
                console.log("updateToolWithCallback - 普通文本验证通过 - 内容:", toolDescription);
            }
        }

        // 准备工具数据
        let toolData = {
            id: toolId,
            mcpServerId: Number(mcpServerId),
            name: toolName,
            type: toolType,
            description: toolDescription,
            owner: toolOwner,
            timeOut: toolTimeout,
            textType: Number(textType) // 添加文本类型字段
        };

        console.log("更新工具 - 准备的工具数据:", JSON.stringify(toolData, null, 2));

        // 根据工具类型获取特定字段
        if (toolType === 'HTTP') {
            const httpUrl = $('#updateHttpUrl').val().trim();
            const httpMethod = $('#updateHttpMethod').val();

            if (!httpUrl) {
                showAlert('请填写URL', 'warning');
                callback(false);
                return;
            }

            toolData = {
                ...toolData,
                url: httpUrl,
                methodName: httpMethod,
                headers: {}
            };

            // 获取请求头
            $('#updateHttpHeaderList .header-item').each(function() {
                const headerName = $(this).find('.header-name').val().trim();
                const headerValue = $(this).find('.header-value').val().trim();
                if (headerName && headerValue) {
                    toolData.headers[headerName] = headerValue;
                }
            });

            // 获取参数
            const params = [];
            $('#updateHTTPParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();
                if (paramName && paramDescription) {
                    params.push({
                        name: paramName,
                        description: paramDescription,
                        required: paramRequired,
                        type: paramType || 'String'
                    });
                }
            });
            toolData.toolParams = params;
        } else if (toolType === 'THRIFT') {
            const thriftAppKey = $('#updateThriftAppKey').val().trim();
            const thriftInterfaceName = $('#updateThriftInterfaceName').val().trim();
            const thriftMethodName = $('#updateThriftMethodName').val().trim();
            const thriftCell = $('#updateThriftCell').val().trim();
            const thriftIp = $('#updateThriftIp').val().trim();
            const thriftPort = $('#updateThriftPort').val().trim();

            if (!thriftAppKey || !thriftInterfaceName || !thriftMethodName) {
                showAlert('请填写AppKey、接口全路径和方法名称', 'warning');
                callback(false);
                return;
            }

            toolData.appKey = thriftAppKey;
            toolData.interfaceName = thriftInterfaceName;
            toolData.methodName = thriftMethodName;
            toolData.cell = thriftCell;
            toolData.ip = thriftIp;
            toolData.port = thriftPort;
            toolData.isLongTermTask = $('#updateIsLongTermTask').prop('checked') ? 1 : 0;

            // 获取Avitor脚本内容
            if (typeof getEditorContent === 'function') {
                toolData.avitorScriptCode = getEditorContent('updateThriftAvitorScript');
            }

            // 获取参数
            const params = [];
            $('#updateTHRIFTParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();
                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    params.push(paramData);
                }
            });
            toolData.toolParams = params;
        } else if (toolType === 'PIGEON') {
            const pigeonAppKey = $('#updatePigeonAppKey').val().trim();
            const pigeonInterfaceName = $('#updatePigeonInterfaceName').val().trim();
            const pigeonMethodName = $('#updatePigeonMethodName').val().trim();
            const pigeonCell = $('#updatePigeonCell').val().trim();

            if (!pigeonAppKey || !pigeonInterfaceName || !pigeonMethodName) {
                showAlert('请填写AppKey、接口全路径和方法名称', 'warning');
                callback(false);
                return;
            }

            toolData.appKey = pigeonAppKey;
            toolData.interfaceName = pigeonInterfaceName;
            toolData.methodName = pigeonMethodName;
            toolData.cell = pigeonCell;

            // 获取Avitor脚本内容
            if (typeof getEditorContent === 'function') {
                toolData.avitorScriptCode = getEditorContent('updatePigeonAvitorScript');
            }

            // 获取参数
            const params = [];
            $('#updatePIGEONParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();
                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    params.push(paramData);
                }
            });
            toolData.toolParams = params;
        }

        console.log("准备发送的更新工具数据:", toolData);

        // 确保textType字段存在且为数字类型
        if (!toolData.textType || typeof toolData.textType !== 'number') {
            console.warn("更新工具 - textType字段异常，重新设置为:", textType);
            toolData.textType = Number(textType) || 2;
        }

        console.log("最终发送的更新工具数据:", JSON.stringify(toolData, null, 2));

        // 发送更新工具请求
        $.ajax({
            url: '/mcp/api/tools/updateTool?mcpServerId=' + mcpServerId,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(toolData),
            timeout: 30000, // 30秒超时
            success: function(response) {
                console.log("更新工具响应:", response);
                if (response.code === 0) {
                    showAlert('工具更新成功', 'success');
                    // 重新加载工具列表
                    if (typeof loadToolsByMcpServerId === 'function') {
                        loadToolsByMcpServerId(mcpServerId);
                    }
                    callback(true);
                } else {
                    console.error("更新工具失败:", response);
                    showAlert('工具更新失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("更新工具错误:", {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
                showAlert('工具更新失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    // 带回调的删除工具函数
    function deleteToolWithCallback(mcpServerId, toolName, toolType, owner, callback) {
        // 只有ToolAnnotation类型工具不能被删除
        if (toolType === 'ToolAnnotation') {
            showAlert('ToolAnnotation类型工具不允许删除', 'warning');
            callback(false);
            return;
        }

        $.ajax({
            url: '/mcp/api/tools/delete',
            type: 'GET',
            data: {
                mcpServerId: mcpServerId,
                toolName: toolName,
                toolType: toolType,
                owner: owner
            },
            timeout: 30000, // 30秒超时
            success: function(response) {
                console.log("删除工具响应:", response);
                if (response.code === 0) {
                    showAlert('工具删除成功', 'success');
                    // 重新加载工具列表
                    if (typeof loadToolsByMcpServerId === 'function') {
                        loadToolsByMcpServerId(mcpServerId);
                    }
                    callback(true);
                } else {
                    showAlert('工具删除失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("删除工具错误:", {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
                showAlert('工具删除失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    // 初始化修复
    function initFix() {
        console.log("初始化工具管理修复脚本");

        // 解绑重复事件
        unbindDuplicateEvents();

        // 重新绑定事件
        bindToolEvents();

        console.log("工具管理修复脚本初始化完成");
    }

    // 页面加载完成后延迟执行修复
    setTimeout(initFix, 1000);

    // 额外延迟执行，确保覆盖其他脚本的事件绑定
    setTimeout(function() {
        console.log("执行额外的事件绑定修复");

        // 强制解绑并重新绑定创建工具按钮事件
        $('#createToolModal .modal-footer button[type="submit"]').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isCreating) {
                console.log("正在创建工具中，忽略重复点击");
                return false;
            }

            console.log("创建工具提交按钮点击事件触发（额外绑定）");

            // 获取文本类型和验证工具描述
            const textType = $('input[name="textType"]:checked').val() || '2';
            let toolDescription;

            console.log("额外绑定 - 当前选择的文本类型:", textType);

            if (textType === '1') {
                // 富文本
                toolDescription = $('#toolDescription').summernote('code');
                console.log("额外绑定 - 富文本内容:", toolDescription);

                // 富文本验证 - 移除HTML标签后检查是否为空
                const text = $('<div>').html(toolDescription).text().trim();
                if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                    showAlert('请填写工具描述', 'warning');
                    $('#toolDescription').closest('.form-group').addClass('has-error');
                    $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                    return false;
                }
            } else {
                // 普通文本（默认）
                toolDescription = $('#toolDescriptionPlain').val();
                console.log("额外绑定 - 普通文本内容:", toolDescription);
                console.log("额外绑定 - 普通文本框是否存在:", $('#toolDescriptionPlain').length);
                console.log("额外绑定 - 普通文本框是否可见:", $('#toolDescriptionPlain').is(':visible'));
                console.log("额外绑定 - 普通文本框父容器是否可见:", $('#toolDescriptionPlain').parent().is(':visible'));

                // 普通文本验证
                if (!toolDescription || toolDescription.trim() === '') {
                    console.log("额外绑定 - 普通文本验证失败 - 内容为空");
                    showAlert('请填写工具描述', 'warning');
                    $('#toolDescriptionPlain').addClass('is-invalid');
                    return false;
                } else {
                    console.log("额外绑定 - 普通文本验证通过 - 内容:", toolDescription);
                }
            }

            console.log("额外绑定 - 最终工具描述:", toolDescription);
            console.log("额外绑定 - 文本类型:", textType);

            // 设置创建状态
            isCreating = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $(this);
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 创建中...');

            // 调用创建函数
            createToolWithCallback(function(success) {
                // 恢复按钮状态
                isCreating = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#createToolModal').modal('hide');
                    $('#createToolForm')[0].reset();
                    // 重置富文本编辑器
                    if ($('#toolDescription').summernote) {
                        $('#toolDescription').summernote('code', '');
                    }
                }
            });

            return false;
        });

        // 强制重新绑定编辑和删除按钮事件
        $(document).off('click', '.edit-tool-btn').on('click', '.edit-tool-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 检查按钮是否被禁用
            if ($(this).prop('disabled') || $(this).hasClass('disabled')) {
                return false;
            }

            const mcpServerId = $(this).data('mcpserver-id');
            const toolName = $(this).data('name');

            console.log("编辑工具按钮点击（额外绑定） - mcpServerId:", mcpServerId, "toolName:", toolName);

            if (mcpServerId && toolName) {
                if (typeof loadToolForEdit === 'function') {
                    loadToolForEdit(mcpServerId, toolName);
                } else {
                    console.error("loadToolForEdit函数未定义");
                    showAlert('编辑功能不可用', 'error');
                }
            } else {
                console.error("缺少必要的数据属性");
                showAlert('无法获取工具信息', 'error');
            }

            return false;
        });

        $(document).off('click', '.delete-tool-btn').on('click', '.delete-tool-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 检查按钮是否被禁用
            if ($(this).prop('disabled') || $(this).hasClass('disabled')) {
                return false;
            }

            const mcpServerId = $(this).data('mcpserver-id');
            const toolName = $(this).data('name');
            const toolType = $(this).data('type');
            const owner = $(this).data('owner');

            console.log("删除工具按钮点击（额外绑定） - mcpServerId:", mcpServerId, "toolName:", toolName);

            // 检查工具类型，确保只有ToolAnnotation类型工具不能被删除
            if (toolType === 'ToolAnnotation') {
                showAlert('ToolAnnotation类型工具不允许删除', 'warning');
                return false;
            }

            // 显示删除确认对话框
            $('#confirmDeleteToolModal .tool-name').text(toolName);
            $('#confirmDeleteToolModal .confirm-delete-tool-btn')
                .data('mcpserver-id', mcpServerId)
                .data('name', toolName)
                .data('type', toolType)
                .data('owner', owner);
            $('#confirmDeleteToolModal').modal('show');

            return false;
        });

        // 强制重新绑定更新工具按钮事件
        $('#updateToolModal .modal-footer button[type="submit"]').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isUpdating) {
                console.log("正在更新工具中，忽略重复点击（额外绑定）");
                return false;
            }

            console.log("⚡ tool-manager-fix.js - 更新工具提交按钮点击事件触发（额外绑定）");

            // 获取文本类型并验证工具描述
            const textType = $('input[name="updateTextType"]:checked').val() || '2';
            let toolDescription;

            console.log("额外绑定 - 当前选择的文本类型:", textType);

            if (textType === '1') {
                // 富文本验证
                toolDescription = $('#updateToolDescription').summernote('code');
                const text = $('<div>').html(toolDescription).text().trim();

                if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                    showAlert('请填写工具描述', 'warning');
                    $('#updateToolDescription').closest('.form-group').addClass('has-error');
                    $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                    return false;
                }
            } else {
                // 普通文本验证
                toolDescription = $('#updateToolDescriptionPlain').val();
                console.log("额外绑定 - 普通文本内容:", toolDescription);

                if (!toolDescription || toolDescription.trim() === '') {
                    showAlert('请填写工具描述', 'warning');
                    $('#updateToolDescriptionPlain').addClass('is-invalid');
                    return false;
                } else {
                    console.log("额外绑定 - 普通文本验证通过 - 内容:", toolDescription);
                }
            }

            // 设置更新状态
            isUpdating = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $(this);
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 更新中...');

            // 调用更新函数
            updateToolWithCallback(function(success) {
                // 恢复按钮状态
                isUpdating = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#updateToolModal').modal('hide');
                }
            });

            return false;
        });

        console.log("额外的事件绑定修复完成");
    }, 2000);

    // 带回调的创建工具函数
    function createToolWithCallback(callback) {
        // 获取通用字段
        let mcpServerId = $('#mcpServerId').val();

        // 如果mcpServerId为空，尝试从页面元素中获取
        if (!mcpServerId) {
            mcpServerId = $('#toolListTitle').attr('data-mcpserver-id');
        }

        // 如果还是为空，尝试从全局变量获取
        if (!mcpServerId && typeof currentMcpServerId !== 'undefined') {
            mcpServerId = currentMcpServerId;
        }

        const toolType = $('#toolType').val();
        const toolName = $('#toolName').val().trim();
        const textType = $('input[name="textType"]:checked').val() || '2'; // 获取文本类型，默认为普通文本

        let toolDescription;
        if (textType === '1') {
            // 富文本
            toolDescription = $('#toolDescription').summernote('code');
        } else {
            // 普通文本（默认）
            toolDescription = $('#toolDescriptionPlain').val();
        }

        const toolOwner = $('#toolOwner').val().trim();
        const toolTimeout = $('#toolTimeout').val().trim() || '6000';

        console.log("创建工具 - mcpServerId:", mcpServerId);
        console.log("创建工具 - toolType:", toolType);
        console.log("创建工具 - toolName:", toolName);
        console.log("创建工具 - textType:", textType);
        console.log("创建工具 - textType类型:", typeof textType);
        console.log("创建工具 - toolDescription:", toolDescription);
        console.log("创建工具 - 普通文本框是否可见:", $('#toolDescriptionPlain').is(':visible'));
        console.log("创建工具 - 富文本框是否可见:", $('#toolDescription').closest('.note-editor').is(':visible'));

        // 验证必填字段
        if (!mcpServerId) {
            showAlert('无法获取McpServer ID，请重新选择McpServer', 'warning');
            callback(false);
            return;
        }

        if (!toolName) {
            showAlert('请填写工具名称', 'warning');
            callback(false);
            return;
        }

        if (!toolOwner) {
            showAlert('请填写负责人', 'warning');
            callback(false);
            return;
        }

        // 准备工具数据
        let toolData = {
            mcpServerId: Number(mcpServerId),
            name: toolName,
            type: toolType,
            description: toolDescription,
            owner: toolOwner,
            timeOut: toolTimeout,
            textType: Number(textType) // 添加文本类型字段
        };

        // 根据工具类型获取特定字段
        if (toolType === 'HTTP') {
            const httpUrl = $('#httpUrl').val().trim();
            const httpMethod = $('#httpMethod').val();

            if (!httpUrl) {
                showAlert('请填写URL', 'warning');
                callback(false);
                return;
            }

            toolData = {
                ...toolData,
                url: httpUrl,
                methodName: httpMethod,
                headers: {}
            };

            // 获取请求头
            $('#httpHeaderList .header-item').each(function() {
                const headerName = $(this).find('.header-name').val().trim();
                const headerValue = $(this).find('.header-value').val().trim();
                if (headerName && headerValue) {
                    toolData.headers[headerName] = headerValue;
                }
            });

            // 获取参数
            const params = [];
            $('#HTTPParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();
                if (paramName && paramDescription) {
                    params.push({
                        name: paramName,
                        description: paramDescription,
                        required: paramRequired,
                        type: paramType || 'String'
                    });
                }
            });
            toolData.toolParams = params;
        } else if (toolType === 'THRIFT') {
            const thriftAppKey = $('#thriftAppKey').val().trim();
            const thriftInterfaceName = $('#thriftInterfaceName').val().trim();
            const thriftMethodName = $('#thriftMethodName').val().trim();
            const thriftCell = $('#thriftCell').val().trim();
            const thriftIp = $('#thriftIp').val().trim();
            const thriftPort = $('#thriftPort').val().trim();

            if (!thriftAppKey || !thriftInterfaceName || !thriftMethodName) {
                showAlert('请填写AppKey、接口全路径和方法名称', 'warning');
                callback(false);
                return;
            }

            toolData.appKey = thriftAppKey;
            toolData.interfaceName = thriftInterfaceName;
            toolData.methodName = thriftMethodName;
            toolData.cell = thriftCell;
            toolData.ip = thriftIp;
            toolData.port = thriftPort;
            toolData.isLongTermTask = $('#isLongTermTask').prop('checked') ? 1 : 0;

            // 获取Avitor脚本内容
            if (typeof getEditorContent === 'function') {
                toolData.avitorScriptCode = getEditorContent('thriftAvitorScript');
            }

            // 获取参数
            const params = [];
            $('#THRIFTParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();
                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    params.push(paramData);
                }
            });
            toolData.toolParams = params;
        } else if (toolType === 'PIGEON') {
            const pigeonAppKey = $('#pigeonAppKey').val().trim();
            const pigeonInterfaceName = $('#pigeonInterfaceName').val().trim();
            const pigeonMethodName = $('#pigeonMethodName').val().trim();
            const pigeonCell = $('#pigeonCell').val().trim();

            if (!pigeonAppKey || !pigeonInterfaceName || !pigeonMethodName) {
                showAlert('请填写AppKey、接口全路径和方法名称', 'warning');
                callback(false);
                return;
            }

            toolData.appKey = pigeonAppKey;
            toolData.interfaceName = pigeonInterfaceName;
            toolData.methodName = pigeonMethodName;
            toolData.cell = pigeonCell;

            // 获取Avitor脚本内容
            if (typeof getEditorContent === 'function') {
                toolData.avitorScriptCode = getEditorContent('pigeonAvitorScript');
            }

            // 获取参数
            const params = [];
            $('#PIGEONParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();
                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    params.push(paramData);
                }
            });
            toolData.toolParams = params;
        }

        console.log("准备发送的创建工具数据:", toolData);

        // 确保textType字段存在且为数字类型
        if (!toolData.textType || typeof toolData.textType !== 'number') {
            console.warn("textType字段异常，重新设置为:", textType);
            toolData.textType = Number(textType) || 2;
        }

        console.log("最终发送的创建工具数据:", JSON.stringify(toolData, null, 2));

        // 发送创建工具请求
        $.ajax({
            url: '/mcp/api/tools/create',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(toolData),
            timeout: 30000, // 30秒超时
            success: function(response) {
                console.log("创建工具响应:", response);
                if (response.code === 0) {
                    showAlert('工具创建成功', 'success');
                    // 重新加载工具列表
                    if (typeof loadToolsByMcpServerId === 'function') {
                        loadToolsByMcpServerId(mcpServerId);
                    }
                    callback(true);
                } else {
                    console.error("创建工具失败:", response);
                    showAlert('工具创建失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("创建工具错误:", {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
                showAlert('工具创建失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    // 监听工具列表重新加载事件，重新应用修复
    $(document).on('toolsLoaded', function() {
        console.log("检测到工具列表重新加载，重新应用修复");
        setTimeout(function() {
            unbindDuplicateEvents();
            bindToolEvents();
        }, 500);
    });
});

