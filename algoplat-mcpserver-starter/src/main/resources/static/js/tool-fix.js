/**
 * 工具修复JavaScript文件
 */

$(document).ready(function(){
    // 先解绑所有AI生成按钮的点击事件，避免重复绑定
    $(document).off('click', '.ai-generate-btn');
    $('.ai-generate-btn').off('click');

    // 初始化时禁用所有AI生成按钮
    $('.ai-generate-btn').prop('disabled', true);

    // 工具名输入框的实时验证
    $('#toolName').on('input', function() {
        const toolType = $('#toolType').val();
        const toolName = $(this).val().trim();

        // 对HTTP、THRIFT和PIGEON类型的工具名进行中文字符检查
        if ((toolType === 'HTTP' || toolType === 'THRIFT' || toolType === 'PIGEON') && containsChinese(toolName)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after(`<div class="invalid-feedback">${toolType}类型的工具名不能包含中文字符，请使用英文字母、数字和下划线</div>`);
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // 更新工具名输入框的实时验证
    $('#updateToolName').on('input', function() {
        const toolType = $('#updateToolType').val();
        const toolName = $(this).val().trim();

        // 对HTTP、THRIFT和PIGEON类型的工具名进行中文字符检查
        if ((toolType === 'HTTP' || toolType === 'THRIFT' || toolType === 'PIGEON') && containsChinese(toolName)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after(`<div class="invalid-feedback">${toolType}类型的工具名不能包含中文字符，请使用英文字母、数字和下划线</div>`);
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // 工具类型切换事件，需要重新验证工具名
    $('#toolType').on('change', function() {
        $('#toolName').trigger('input');
    });

    // 更新工具类型切换事件，需要重新验证工具名
    $('#updateToolType').on('change', function() {
        $('#updateToolName').trigger('input');
    });

    // 监听THRIFT和PIGEON表单中的必填字段变化，控制AI生成按钮的启用/禁用状态
    $('#thriftAppKey, #thriftInterfaceName, #thriftMethodName').on('input', function() {
        checkAIButtonStatus('THRIFT', 'create');
    });

    $('#pigeonAppKey, #pigeonInterfaceName, #pigeonMethodName').on('input', function() {
        checkAIButtonStatus('PIGEON', 'create');
    });

    $('#updateThriftAppKey, #updateThriftInterfaceName, #updateThriftMethodName').on('input', function() {
        checkAIButtonStatus('THRIFT', 'update');
    });

    $('#updatePigeonAppKey, #updatePigeonInterfaceName, #updatePigeonMethodName').on('input', function() {
        checkAIButtonStatus('PIGEON', 'update');
    });

    // 创建工具按钮点击事件，确保在打开创建工具模态框时禁用AI生成按钮
    $('#createToolBtn').on('click', function() {
        // 确保AI生成按钮是禁用状态
        $('.ai-generate-btn').prop('disabled', true);
    });

    // 确保选择默认工具按钮事件正确绑定
    $('#selectDefaultToolsBtn').off('click').on('click', function() {
        console.log("工具修复脚本中：点击选择默认工具按钮");
        // 获取当前 McpServerId
        const mcpServerId = $('#toolListTitle').attr('data-mcpserver-id');
        console.log("工具修复脚本中：当前 McpServerId:", mcpServerId);

        if (!mcpServerId) {
            console.error("工具修复脚本中：无法获取 McpServerId，toolListTitle 元素:", $('#toolListTitle'));
            showAlert('请先选择一个McpServer', 'warning');
            return;
        }
        // 确保 mcpServerId 是数字类型
        const mcpServerIdNum = parseInt(mcpServerId, 10);
        if (isNaN(mcpServerIdNum)) {
            console.error("工具修复脚本中：McpServerId 不是有效的数字:", mcpServerId);
            showAlert('McpServerId 不是有效的数字', 'danger');
            return;
        }

        // 显示选择默认工具模态框
        $('#selectDefaultToolsModal').modal('show');

        // 加载默认工具列表
        if (typeof window.loadDefaultTools === 'function') {
            window.loadDefaultTools(mcpServerIdNum);
        } else {
            console.error("loadDefaultTools 函数未定义");
            // 显示错误信息
            $('#defaultToolsList').html(`
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-circle"></i> 加载默认工具列表失败: loadDefaultTools 函数未定义
                </div>
            `);
        }
    });

    // 保存默认工具按钮点击事件
    $('#saveDefaultToolsBtn').off('click').on('click', function() {
        console.log("工具修复脚本中：点击保存默认工具按钮");
        // 获取当前 McpServerId
        const mcpServerId = $('#toolListTitle').attr('data-mcpserver-id');
        console.log("工具修复脚本中：当前 McpServerId:", mcpServerId);
        if (!mcpServerId) {
            console.error("工具修复脚本中：无法获取 McpServerId，toolListTitle 元素:", $('#toolListTitle'));
            showAlert('请先选择一个McpServer', 'warning');
            return;
        }
        // 确保 mcpServerId 是数字类型
        const mcpServerIdNum = parseInt(mcpServerId, 10);
        if (isNaN(mcpServerIdNum)) {
            console.error("工具修复脚本中：McpServerId 不是有效的数字:", mcpServerId);
            showAlert('McpServerId 不是有效的数字', 'danger');
            return;
        }

        // 直接调用全局函数
        if (typeof window.saveDefaultTools === 'function') {
            window.saveDefaultTools(mcpServerIdNum);
        } else {
            console.error("saveDefaultTools 函数未定义");
            // 显示错误信息
            showAlert('保存默认工具失败: saveDefaultTools 函数未定义', 'danger');
        }
    });
    // 重新绑定创建工具表单提交事件
    $('#createToolForm').off('submit').on('submit', function(e) {
        e.preventDefault();
        console.log("表单提交事件触发");

        // 清理之前的错误状态
        $('#toolDescription').closest('.form-group').removeClass('has-error');
        $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
        $('#toolDescriptionPlain').removeClass('is-invalid');

        // 获取工具描述和文本类型
        const textType = $('input[name="textType"]:checked').val() || '2';
        let toolDescription;

        console.log("当前选择的文本类型:", textType);

        if (textType === '1') {
            // 富文本
            toolDescription = $('#toolDescription').summernote('code');
            console.log("富文本内容:", toolDescription);

            // 富文本验证 - 移除HTML标签后检查是否为空
            const text = $('<div>').html(toolDescription).text().trim();
            if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                showAlert('请填写工具描述', 'warning');
                $('#toolDescription').closest('.form-group').addClass('has-error');
                $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                return;
            }
        } else {
            // 普通文本（默认）
            toolDescription = $('#toolDescriptionPlain').val();
            console.log("普通文本内容:", toolDescription);
            console.log("普通文本框是否存在:", $('#toolDescriptionPlain').length);
            console.log("普通文本框是否可见:", $('#toolDescriptionPlain').is(':visible'));
            console.log("普通文本框父容器是否可见:", $('#toolDescriptionPlain').parent().is(':visible'));
            console.log("普通文本框祖父容器是否可见:", $('#toolDescriptionPlain').parent().parent().is(':visible'));

            // 普通文本验证
            if (!toolDescription || toolDescription.trim() === '') {
                console.error("❌ 普通文本验证失败 - 内容为空或无效");
                showAlert('请填写工具描述', 'warning');
                $('#toolDescriptionPlain').addClass('is-invalid');
                return;
            } else {
                console.log("✅ 普通文本验证通过");
            }
        }

        console.log("最终工具描述:", toolDescription);
        console.log("文本类型:", textType);
        createTool();
    });

    // 直接绑定到提交按钮的点击事件
    $('#createToolModal .modal-footer button[type="submit"]').off('click').on('click', function(e) {
        e.preventDefault();
        console.log("提交按钮点击事件触发");

        // 清理之前的错误状态
        $('#toolDescription').closest('.form-group').removeClass('has-error');
        $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
        $('#toolDescriptionPlain').removeClass('is-invalid');

        // 获取工具描述和文本类型
        const textType = $('input[name="textType"]:checked').val() || '2';
        let toolDescription;

        console.log("提交按钮 - 当前选择的文本类型:", textType);

        if (textType === '1') {
            // 富文本
            toolDescription = $('#toolDescription').summernote('code');
            console.log("提交按钮 - 富文本内容:", toolDescription);

            // 富文本验证 - 移除HTML标签后检查是否为空
            const text = $('<div>').html(toolDescription).text().trim();
            if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                showAlert('请填写工具描述', 'warning');
                $('#toolDescription').closest('.form-group').addClass('has-error');
                $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                return;
            }
        } else {
            // 普通文本（默认）
            toolDescription = $('#toolDescriptionPlain').val();
            console.log("提交按钮 - 普通文本内容:", toolDescription);
            console.log("提交按钮 - 普通文本框是否存在:", $('#toolDescriptionPlain').length);
            console.log("提交按钮 - 普通文本框是否可见:", $('#toolDescriptionPlain').is(':visible'));
            console.log("提交按钮 - 普通文本框父容器是否可见:", $('#toolDescriptionPlain').parent().is(':visible'));

            // 普通文本验证
            if (!toolDescription || toolDescription.trim() === '') {
                console.error("❌ 提交按钮 - 普通文本验证失败 - 内容为空或无效");
                showAlert('请填写工具描述', 'warning');
                $('#toolDescriptionPlain').addClass('is-invalid');
                return;
            } else {
                console.log("✅ 提交按钮 - 普通文本验证通过");
            }
        }

        console.log("提交按钮 - 最终工具描述:", toolDescription);
        console.log("提交按钮 - 文本类型:", textType);
        createTool();
    });

    // 解决业务场景表单提交事件被重复绑定的问题
    // 先解绑所有业务场景表单提交事件
    $('#createBusinessLineForm').off('submit');
    // 然后重新绑定一次
    $('#createBusinessLineForm').on('submit', function(e) {
        e.preventDefault();
        console.log("业务场景表单提交事件触发");
        if (validateInput($('#businessLineName'), '业务场景名称')) {
            createBusinessLine();
        }
    });

    // 解决 McpServer 表单提交事件缺失的问题
    // 先解绑所有 McpServer 表单提交事件
    $('#createMcpServerForm').off('submit');
    // 然后重新绑定一次
    $('#createMcpServerForm').on('submit', function(e) {
        e.preventDefault();
        console.log("McpServer表单提交事件触发");
        if (validateInput($('#mcpServerName'), 'McpServer名称')) {
            createMcpServer();
        }
    });

    // 直接绑定到 McpServer 提交按钮的点击事件
    $('#createMcpServerModal .modal-footer button[type="submit"]').off('click').on('click', function(e) {
        e.preventDefault();
        console.log("McpServer提交按钮点击事件触发");
        if (validateInput($('#mcpServerName'), 'McpServer名称')) {
            createMcpServer();
        }
    });

    // 确保创建 McpServer 按钮的点击事件正确绑定
    $('#createMcpServerBtn').off('click').on('click', function() {
        console.log("创建 McpServer 按钮点击事件触发");
        // 设置业务线ID
        $('#businessLineId').val(sessionStorage.getItem('currentBusinessLineId'));
        // 显示创建 McpServer 模态框
        $('#createMcpServerModal').modal('show');
    });

    // 确保编辑工具按钮的点击事件正确绑定
    $(document).on('click', '.edit-tool-btn', function() {
        console.log("编辑工具按钮点击事件触发");
        const mcpServerId = $(this).data('mcpserver-id');
        const toolName = $(this).data('name');
        console.log("编辑工具 - mcpServerId:", mcpServerId, "toolName:", toolName);
        // 调用编辑工具函数
        editTool(mcpServerId, toolName);
    });

    // 确保更新工具表单提交事件正确绑定
    $('#updateToolForm').off('submit').on('submit', function(e) {
        e.preventDefault();
        console.log("更新工具表单提交事件触发");

        // 清理之前的错误状态
        $('#updateToolDescription').closest('.form-group').removeClass('has-error');
        $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
        $('#updateToolDescriptionPlain').removeClass('is-invalid');

        // 获取工具描述和文本类型
        const textType = $('input[name="updateTextType"]:checked').val() || '2';
        let toolDescription;

        console.log("更新表单 - 当前选择的文本类型:", textType);

        if (textType === '1') {
            // 富文本
            toolDescription = $('#updateToolDescription').summernote('code');
            console.log("更新表单 - 富文本内容:", toolDescription);

            // 富文本验证 - 移除HTML标签后检查是否为空
            const text = $('<div>').html(toolDescription).text().trim();
            if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                showAlert('请填写工具描述', 'warning');
                $('#updateToolDescription').closest('.form-group').addClass('has-error');
                $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                return;
            }
        } else {
            // 普通文本（默认）
            toolDescription = $('#updateToolDescriptionPlain').val();
            console.log("更新表单 - 普通文本内容:", toolDescription);

            // 普通文本验证
            if (!toolDescription || toolDescription.trim() === '') {
                showAlert('请填写工具描述', 'warning');
                $('#updateToolDescriptionPlain').addClass('is-invalid');
                return;
            }
        }

        console.log("更新表单 - 最终工具描述:", toolDescription);
        console.log("更新表单 - 文本类型:", textType);
        updateTool();
    });

    // 直接绑定到更新工具提交按钮的点击事件
    $('#updateToolModal .modal-footer button[type="submit"]').off('click').on('click', function(e) {
        e.preventDefault();
        console.log("🔥 tool-fix.js - 更新工具提交按钮点击事件触发");

        // 清理之前的错误状态
        $('#updateToolDescription').closest('.form-group').removeClass('has-error');
        $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
        $('#updateToolDescriptionPlain').removeClass('is-invalid');

        // 获取工具描述和文本类型
        const textType = $('input[name="updateTextType"]:checked').val() || '2';
        let toolDescription;

        console.log("更新按钮 - 当前选择的文本类型:", textType);

        if (textType === '1') {
            // 富文本
            toolDescription = $('#updateToolDescription').summernote('code');
            console.log("更新按钮 - 富文本内容:", toolDescription);

            // 富文本验证 - 移除HTML标签后检查是否为空
            const text = $('<div>').html(toolDescription).text().trim();
            if (!text || toolDescription === '<p><br></p>' || toolDescription === '<p></p>' || toolDescription.trim() === '') {
                showAlert('请填写工具描述', 'warning');
                $('#updateToolDescription').closest('.form-group').addClass('has-error');
                $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', 'red');
                return;
            }
        } else {
            // 普通文本（默认）
            toolDescription = $('#updateToolDescriptionPlain').val();
            console.log("更新按钮 - 普通文本内容:", toolDescription);

            // 普通文本验证
            if (!toolDescription || toolDescription.trim() === '') {
                showAlert('请填写工具描述', 'warning');
                $('#updateToolDescriptionPlain').addClass('is-invalid');
                return;
            }
        }

        console.log("更新按钮 - 最终工具描述:", toolDescription);
        console.log("更新按钮 - 文本类型:", textType);
        updateTool();
    });

// 使用事件委托，确保动态添加的按钮也能绑定事件
$(document).on('click','.ai-generate-btn',function(){
    const toolType = $(this).data('tool-type');
    const mode = $(this).data('mode');

    // 获取接口全路径和方法名称
    let appKey, interfaceName, methodName;
    let toolParams = [];

    if (mode === 'create') {
        if (toolType === 'THRIFT') {
            appKey = $('#thriftAppKey').val().trim();
            interfaceName = $('#thriftInterfaceName').val().trim();
            methodName = $('#thriftMethodName').val().trim();

            console.log("THRIFT参数列表容器:", $('#THRIFTParamList'));
            console.log("THRIFT参数项数量:", $('#THRIFTParamList .param-item').length);
            // 收集参数列表
            $('#THRIFTParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();

                console.log("收集到的参数:", {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType
                });

                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                // 如果是自定义对象类型，添加自定义类型路径
                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    toolParams.push(paramData);
                }
            });
        } else if (toolType === 'PIGEON') {
            appKey = $('#pigeonAppKey').val().trim();
            interfaceName = $('#pigeonInterfaceName').val().trim();
            methodName = $('#pigeonMethodName').val().trim();

            console.log("PIGEON参数列表容器:", $('#PIGEONParamList'));
            console.log("PIGEON参数项数量:", $('#PIGEONParamList .param-item').length);
            // 收集参数列表
            $('#PIGEONParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();

                console.log("收集到的参数:", {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType
                });

                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                // 如果是自定义对象类型，添加自定义类型路径
                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    toolParams.push(paramData);
                }
            });
        }
    } else if (mode === 'update') {
        if (toolType === 'THRIFT') {
            appKey = $('#updateThriftAppKey').val().trim();
            interfaceName = $('#updateThriftInterfaceName').val().trim();
            methodName = $('#updateThriftMethodName').val().trim();

            console.log("更新THRIFT参数列表容器:", $('#updateTHRIFTParamList'));
            console.log("更新THRIFT参数项数量:", $('#updateTHRIFTParamList .param-item').length);
            // 收集参数列表
            $('#updateTHRIFTParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();

                console.log("收集到的更新参数:", {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType
                });

                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                // 如果是自定义对象类型，添加自定义类型路径
                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    toolParams.push(paramData);
                }
            });
        } else if (toolType === 'PIGEON') {
            appKey = $('#updatePigeonAppKey').val().trim();
            interfaceName = $('#updatePigeonInterfaceName').val().trim();
            methodName = $('#updatePigeonMethodName').val().trim();

            console.log("更新PIGEON参数列表容器:", $('#updatePIGEONParamList'));
            console.log("更新PIGEON参数项数量:", $('#updatePIGEONParamList .param-item').length);
            // 收集参数列表
            $('#updatePIGEONParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();

                console.log("收集到的更新参数:", {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType
                });

                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                // 如果是自定义对象类型，添加自定义类型路径
                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    toolParams.push(paramData);
                }
            });
        }
    }

    console.log("最终收集到的参数列表:", toolParams);
    console.log("请求数据:", {
        toolType: toolType,
        interfaceName: interfaceName,
        methodName: methodName,
        appKey: appKey,
        toolParams: toolParams
    });

    // 验证必填字段
    if (!appKey || !interfaceName || !methodName) {
        showAlert('请先填写AppKey、接口全路径和方法名称', 'warning');
        return;
    }

    // 显示确认对话框
    if (!confirm('本操作将对工具描述进行更新，是否继续？')) {
        return;
    }

    // 显示加载状态
    const $btn = $(this);
    const originalText = $btn.html();
    $btn.html('<i class="fa fa-spinner fa-spin"></i> 生成中...');
    $btn.prop('disabled', true);

    // 调用后端接口
    $.ajax({
        url: '/mcp/api/tools/llmRequest',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            toolType: toolType,
            interfaceName: interfaceName,
            methodName: methodName,
            appKey: appKey,
            toolParams: (function() {
                // 尝试从表单中获取参数
                const params = [];
                // 1. 尝试从表单中获取已有的参数输入框
                $('input.param-name').each(function(index) {
                    const $row = $(this).closest('.form-row');
                    const name = $(this).val().trim();
                    const description = $row.find('input.param-description').val() || "查询参数";
                    const required = $row.find('input.param-required').prop('checked') || true;
                    const type = $row.find('select.param-type').val() || "String";
                    if (name) {
                        params.push({
                            name: name,
                            description: description,
                            required: required,
                            type: type
                        });
                    }
                });
                // 2. 如果没有找到参数，尝试直接从页面上获取
                if (params.length === 0) {
                    // 查找所有可能包含参数名称的输入框
                    $('input[type="text"]').each(function() {
                        const id = $(this).attr('id');
                        const name = $(this).val().trim();
                        // 如果输入框ID或值包含"query"，可能是参数
                        if ((id && id.toLowerCase().includes('query')) ||
                            (name && name.toLowerCase().includes('query'))) {
                            params.push({
                                name: name || id || "query",
                                description: "查询参数",
                                required: true,
                                type: "String"
                            });
                        }
                    });
                }
                // 3. 如果仍然没有找到参数，使用硬编码的参数
                if (params.length === 0) {
                    params.push({ name: "queryId", description: "查询ID", type: "String", required: true });
                    params.push({ name: "queryParam", description: "查询参数", type: "String", required: true });
                }
                console.log("动态收集到的参数:", params);
                return params;
            })()
        }),
        success: function(response) {
            // 恢复按钮状态
            $btn.html(originalText);
            $btn.prop('disabled', false);
            if (response.code === 0) {
                try {
                    // 根据当前选择的文本类型填充对应的编辑器
                    if (mode === 'create') {
                        const textType = $('input[name="textType"]:checked').val() || '2';
                        if (textType === '1') {
                            // 富文本
                            $('#toolDescription').summernote('code', response.data || '');
                        } else {
                            // 普通文本
                            $('#toolDescriptionPlain').val(response.data || '');
                        }
                    } else if (mode === 'update') {
                        const textType = $('input[name="updateTextType"]:checked').val() || '2';
                        if (textType === '1') {
                            // 富文本
                            $('#updateToolDescription').summernote('code', response.data || '');
                        } else {
                            // 普通文本
                            $('#updateToolDescriptionPlain').val(response.data || '');
                        }
                    }

                    // 初始化工具提示
                    if (typeof initTooltips === 'function') {
                        initTooltips();
                    }
                    showAlert('AI生成工具描述成功', 'success');
                } catch (e) {
                    console.error("解析工具描述失败", e);
                    showAlert('解析工具描述失败', 'danger');
                }
            } else {
                showAlert('生成工具描述失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            // 恢复按钮状态
            $btn.html(originalText);
            $btn.prop('disabled', false);
            showAlert('生成工具描述失败: ' + error, 'danger');
        }
    });
});
});

/**
 * 检查AI生成按钮状态
 * @param {string} toolType
 * @param {string} mode
 */
function checkAIButtonStatus(toolType, mode) {
    let appKey, interfaceName, methodName;

    if (mode === 'create') {
        if (toolType === 'THRIFT') {
            appKey = $('#thriftAppKey').val().trim();
            interfaceName = $('#thriftInterfaceName').val().trim();
            methodName = $('#thriftMethodName').val().trim();
        } else if (toolType === 'PIGEON') {
            appKey = $('#pigeonAppKey').val().trim();
            interfaceName = $('#pigeonInterfaceName').val().trim();
            methodName = $('#pigeonMethodName').val().trim();
        }
    } else if (mode === 'update') {
        if (toolType === 'THRIFT') {
            appKey = $('#updateThriftAppKey').val().trim();
            interfaceName = $('#updateThriftInterfaceName').val().trim();
            methodName = $('#updateThriftMethodName').val().trim();
        } else if (toolType === 'PIGEON') {
            appKey = $('#updatePigeonAppKey').val().trim();
            interfaceName = $('#updatePigeonInterfaceName').val().trim();
            methodName = $('#updatePigeonMethodName').val().trim();
        }
    }

    // 获取对应的按钮
    const $btn = $(`.ai-generate-btn[data-tool-type="${toolType}"][data-mode="${mode}"]`);

    // 如果所有必填字段都已填写，启用按钮，否则禁用
    if (appKey && interfaceName && methodName) {
        $btn.prop('disabled', false);
    } else {
        $btn.prop('disabled', true);
    }
}

/**
 * 检查字符串是否包含中文字符
 * @param {string} str
 * @returns {boolean}
 */
function containsChinese(str) {
    return /[\u4e00-\u9fa5]/.test(str);
}

// 创建工具
function createTool() {
    console.log("createTool 函数被调用");
    // 获取通用字段
    const mcpServerId = $('#mcpServerId').val();
    const toolType = $('#toolType').val();
    const toolName = $('#toolName').val().trim();
    const textType = $('input[name="textType"]:checked').val() || '2'; // 默认为普通文本

    // 早期调试 - 检查textType获取
    console.log("=== 早期textType调试 ===");
    console.log("所有textType radio按钮:", $('input[name="textType"]'));
    console.log("选中的textType radio按钮:", $('input[name="textType"]:checked'));
    console.log("选中的textType radio按钮数量:", $('input[name="textType"]:checked').length);
    console.log("选中的textType radio按钮值:", $('input[name="textType"]:checked').val());
    console.log("最终textType值:", textType);
    console.log("textType类型:", typeof textType);
    console.log("========================");

    let toolDescription;
    if (textType === '1') {
        // 富文本
        toolDescription = $('#toolDescription').summernote('code');
    } else {
        // 普通文本（默认）
        toolDescription = $('#toolDescriptionPlain').val();
    }

    // 额外验证
    console.log("=== 创建工具时的文本类型检查 ===");
    console.log("选中的radio按钮:", $('input[name="textType"]:checked'));
    console.log("textType值:", textType);
    console.log("普通文本框内容:", $('#toolDescriptionPlain').val());
    console.log("富文本框内容:", $('#toolDescription').summernote('code'));
    console.log("最终使用的描述:", toolDescription);
    console.log("=====================================");

    const toolOwner = $('#toolOwner').val().trim();
    const toolTimeout = $('#toolTimeout').val().trim() || '6000';

    // 调试信息
    console.log("文本类型:", textType);
    console.log("工具描述内容:", toolDescription);
    console.log("工具描述长度:", toolDescription ? toolDescription.length : 0);
    console.log("工具描述trim后:", toolDescription ? toolDescription.trim() : '');
    console.log("工具描述trim后长度:", toolDescription ? toolDescription.trim().length : 0);

    // 验证必填字段
    if (!toolName) {
        showAlert('请填写工具名称', 'warning');
        return;
    }

    // 工具描述验证已在前面的表单验证中完成，这里不需要重复验证

    // 对HTTP、THRIFT和PIGEON类型的工具名进行中文字符检查
    if ((toolType === 'HTTP' || toolType === 'THRIFT' || toolType === 'PIGEON') && containsChinese(toolName)) {
        showAlert(`${toolType}类型的工具名不能包含中文字符，请使用英文字母、数字和下划线`, 'warning');
        return;
    }

    if (!toolOwner) {
        showAlert('请填写负责人', 'warning');
        return;
    }

    // 准备工具数据
    let toolData = {
        mcpServerId: Number(mcpServerId), // 确保 mcpServerId 是数字类型
        name: toolName,
        type: toolType,
        description: toolDescription,
        owner: toolOwner,
        timeOut: toolTimeout,
        textType: Number(textType) // 添加文本类型字段
    };

    // 立即检查toolData构建结果
    console.log("=== toolData构建后立即检查 ===");
    console.log("原始textType变量:", textType);
    console.log("Number(textType):", Number(textType));
    console.log("toolData.textType:", toolData.textType);
    console.log("toolData中是否包含textType:", 'textType' in toolData);
    console.log("toolData完整内容:", JSON.stringify(toolData, null, 2));
    console.log("===============================");

    // 调试工具数据
    console.log("准备提交的工具数据:", JSON.stringify(toolData, null, 2));

    // 验证关键字段
    console.log("=== 关键字段验证 ===");
    console.log("textType 类型:", typeof toolData.textType, "值:", toolData.textType);
    console.log("description 类型:", typeof toolData.description, "值:", toolData.description);
    console.log("description 长度:", toolData.description ? toolData.description.length : 0);

    // 最终验证
    if (!toolData.description || toolData.description.trim() === '') {
        console.error("错误：工具描述为空！");
        showAlert('工具描述不能为空', 'warning');
        return;
    }

    console.log("✓ 所有验证通过，准备提交");
    console.log("========================");

    // 根据工具类型获取特定字段
    if (toolType === 'HTTP') {
        const httpUrl = $('#httpUrl').val().trim();
        const httpMethod = $('#httpMethod').val();

        if (!httpUrl) {
            showAlert('请填写URL', 'warning');
            return;
        }

        // 修改参数名称以匹配后端预期
        toolData = {
            ...toolData,
            url: httpUrl,
            methodName: httpMethod,  // 确保使用 methodName 而不是 method
            headers: {}  // 初始化 headers 为对象而不是数组
        };

        // 获取请求头
        $('#httpHeaderList .header-item').each(function() {
            const headerName = $(this).find('.header-name').val().trim();
            const headerValue = $(this).find('.header-value').val().trim();
            if (headerName && headerValue) {
                toolData.headers[headerName] = headerValue;  // 使用对象格式存储 headers
            }
        });

        // 获取参数
        const params = [];
        $('#HTTPParamList .param-item').each(function() {
            const paramName = $(this).find('.param-name').val().trim();
            const paramDescription = $(this).find('.param-description').val().trim();
            const paramRequired = $(this).find('.param-required').prop('checked');
            const paramType = $(this).find('.param-type').val();
            let paramData = {
                name: paramName,
                description: paramDescription,
                required: paramRequired,
                type: paramType || 'String'
            };

            // 如果是自定义对象类型，添加自定义类型路径
            if (paramType === 'CustomObject') {
                paramData.customType = $(this).find('.param-custom-type').val().trim();
            }

            if (paramName && paramDescription) {
                params.push(paramData);
            }
        });
        toolData.toolParams = params;
    } else if (toolType === 'THRIFT') {
        const thriftAppKey = $('#thriftAppKey').val().trim();
        const thriftInterfaceName = $('#thriftInterfaceName').val().trim();
        const thriftMethodName = $('#thriftMethodName').val().trim();
        const thriftCell = $('#thriftCell').val().trim();
        const thriftIp = $('#thriftIp').val().trim();
        const thriftPort = $('#thriftPort').val().trim();

        // 验证THRIFT特定字段
        if (!thriftAppKey || !thriftInterfaceName || !thriftMethodName) {
            showAlert('请填写AppKey、接口全路径和方法名称', 'warning');
            return;
        }

        // 添加THRIFT特定字段 - 参数名称正确
        toolData.appKey = thriftAppKey;
        toolData.interfaceName = thriftInterfaceName;
        toolData.methodName = thriftMethodName;  // 正确使用 methodName
        toolData.cell = thriftCell;
        toolData.ip = thriftIp;
        toolData.port = thriftPort;
        // 添加是否是长连接任务字段
        toolData.isLongTermTask = $('#isLongTermTask').prop('checked') ? 1 : 0;

        // 获取Avitor脚本内容
        const thriftAvitorScript = getEditorContent('thriftAvitorScript');
        toolData.avitorScriptCode = thriftAvitorScript;

        // 获取参数
        const params = [];
        $('#THRIFTParamList .param-item').each(function() {
            const paramName = $(this).find('.param-name').val().trim();
            const paramDescription = $(this).find('.param-description').val().trim();
            const paramRequired = $(this).find('.param-required').prop('checked');
            const paramType = $(this).find('.param-type').val();
            let paramData = {
                name: paramName,
                description: paramDescription,
                required: paramRequired,
                type: paramType || 'String'
            };

            // 如果是自定义对象类型，添加自定义类型路径
            if (paramType === 'CustomObject') {
                paramData.customType = $(this).find('.param-custom-type').val().trim();
            }

            if (paramName && paramDescription) {
                params.push(paramData);
            }
        });
        toolData.toolParams = params;
    } else if (toolType === 'PIGEON') {
        const pigeonAppKey = $('#pigeonAppKey').val().trim();
        const pigeonInterfaceName = $('#pigeonInterfaceName').val().trim();
        const pigeonMethodName = $('#pigeonMethodName').val().trim();
        const pigeonCell = $('#pigeonCell').val().trim();

        // 验证PIGEON特定字段
        if (!pigeonAppKey || !pigeonInterfaceName || !pigeonMethodName) {
            showAlert('请填写AppKey、接口全路径和方法名称', 'warning');
            return;
        }

        // 添加PIGEON特定字段 - 参数名称正确
        toolData.appKey = pigeonAppKey;
        toolData.interfaceName = pigeonInterfaceName;
        toolData.methodName = pigeonMethodName;  // 正确使用 methodName
        toolData.cell = pigeonCell;

        // 获取Avitor脚本内容
        const pigeonAvitorScript = getEditorContent('pigeonAvitorScript');
        toolData.avitorScriptCode = pigeonAvitorScript;

        // 获取参数
        const params = [];
        $('#PIGEONParamList .param-item').each(function() {
            const paramName = $(this).find('.param-name').val().trim();
            const paramDescription = $(this).find('.param-description').val().trim();
            const paramRequired = $(this).find('.param-required').prop('checked');
            const paramType = $(this).find('.param-type').val();
            let paramData = {
                name: paramName,
                description: paramDescription,
                required: paramRequired,
                type: paramType || 'String'
            };

            // 如果是自定义对象类型，添加自定义类型路径
            if (paramType === 'CustomObject') {
                paramData.customType = $(this).find('.param-custom-type').val().trim();
            }

            if (paramName && paramDescription) {
                params.push(paramData);
            }
        });
        toolData.toolParams = params;

        // 检查THRIFT处理后的toolData
        console.log("=== THRIFT处理后toolData检查 ===");
        console.log("toolData.textType:", toolData.textType);
        console.log("toolData中是否包含textType:", 'textType' in toolData);
        console.log("================================");
    }

    console.log("准备发送的工具数据:", toolData);

    // 确保textType字段存在
    if (!toolData.hasOwnProperty('textType') || toolData.textType === undefined || toolData.textType === null) {
        console.error("❌ textType字段丢失！重新设置...");
        toolData.textType = Number(textType) || 2;
    }

    console.log("=== 最终发送前的数据检查 ===");
    console.log("textType字段存在:", toolData.hasOwnProperty('textType'));
    console.log("textType值:", toolData.textType);
    console.log("textType类型:", typeof toolData.textType);
    console.log("完整数据:", JSON.stringify(toolData, null, 2));
    console.log("===============================");

    // 强制确保textType字段存在
    toolData.textType = Number(textType) || 2;

    console.log("🔧 强制修复后的toolData:", JSON.stringify(toolData, null, 2));

    // 发送创建工具请求
    $.ajax({
        url: '/mcp/api/tools/create',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(toolData),
        success: function(response) {
            console.log("创建工具响应:", response);
            if (response.code === 0) {
                showAlert('工具创建成功', 'success');
                $('#createToolModal').modal('hide');
                // 重新加载工具列表
                if (typeof loadToolsByMcpServerId === 'function') {
                    loadToolsByMcpServerId(mcpServerId);
                }

                // 调试：检查返回的工具信息
                try {
                    const createdTool = JSON.parse(response.data);
                    console.log("创建的工具信息:", createdTool);
                    console.log("创建的工具textType:", createdTool.textType);
                    console.log("创建的工具description:", createdTool.description);
                } catch (e) {
                    console.log("解析返回数据失败:", e);
                }
            } else {
                showAlert('工具创建失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error("创建工具错误:", error);
            showAlert('工具创建失败: ' + error, 'danger');
        }
    });
}

// 更新工具
function updateTool() {
    // 获取通用字段
    const toolId = $('#updateToolId').val();
    const mcpServerId = $('#updateToolMcpServerId').val();
    const toolType = $('#updateToolType').val();
    const toolName = $('#updateToolName').val().trim();
    const textType = $('input[name="updateTextType"]:checked').val() || '2'; // 默认为普通文本

    // 早期调试 - 检查updateTextType获取
    console.log("=== 更新工具早期textType调试 ===");
    console.log("所有updateTextType radio按钮:", $('input[name="updateTextType"]'));
    console.log("选中的updateTextType radio按钮:", $('input[name="updateTextType"]:checked'));
    console.log("选中的updateTextType radio按钮数量:", $('input[name="updateTextType"]:checked').length);
    console.log("选中的updateTextType radio按钮值:", $('input[name="updateTextType"]:checked').val());
    console.log("最终textType值:", textType);
    console.log("textType类型:", typeof textType);
    console.log("================================");

    let toolDescription;
    if (textType === '1') {
        // 富文本
        toolDescription = $('#updateToolDescription').summernote('code');
    } else {
        // 普通文本（默认）
        toolDescription = $('#updateToolDescriptionPlain').val();
    }

    // 额外验证
    console.log("=== 更新工具时的文本类型检查 ===");
    console.log("选中的radio按钮:", $('input[name="updateTextType"]:checked'));
    console.log("textType值:", textType);
    console.log("普通文本框内容:", $('#updateToolDescriptionPlain').val());
    console.log("富文本框内容:", $('#updateToolDescription').summernote('code'));
    console.log("最终使用的描述:", toolDescription);
    console.log("=====================================");

    const toolOwner = $('#updateToolOwner').val().trim();
    const toolTimeout = $('#updateToolTimeout').val().trim() || '6000';

    // 调试信息
    console.log("更新文本类型:", textType);
    console.log("更新工具描述内容:", toolDescription);
    console.log("更新工具描述长度:", toolDescription ? toolDescription.length : 0);
    console.log("更新工具描述trim后:", toolDescription ? toolDescription.trim() : '');
    console.log("更新工具描述trim后长度:", toolDescription ? toolDescription.trim().length : 0);

    // 验证必填字段
    if (!toolName) {
        showAlert('请填写工具名称', 'warning');
        return;
    }

    // 添加工具描述验证
    if (textType === '1') {
        // 富文本验证
        if (!toolDescription ||
            toolDescription === '<p><br></p>' ||
            toolDescription === '<p></p>' ||
            toolDescription.trim() === '' ||
            toolDescription.replace(/<[^>]*>/g, '').trim() === '') {
            showAlert('请填写工具描述', 'warning');
            return;
        }
    } else {
        // 普通文本验证
        if (!toolDescription || toolDescription.trim() === '') {
            showAlert('请填写工具描述', 'warning');
            return;
        }
    }

    // 对HTTP、THRIFT和PIGEON类型的工具名进行中文字符检查
    if ((toolType === 'HTTP' || toolType === 'THRIFT' || toolType === 'PIGEON') && containsChinese(toolName)) {
        showAlert(`${toolType}类型的工具名不能包含中文字符，请使用英文字母、数字和下划线`, 'warning');
        return;
    }

    if (!toolOwner) {
        showAlert('请填写负责人', 'warning');
        return;
    }

    // 准备工具数据
    let toolData = {
        id: toolId,
        mcpServerId: Number(mcpServerId), // 确保 mcpServerId 是数字类型
        name: toolName,
        type: toolType,
        description: toolDescription,
        owner: toolOwner,
        timeOut: toolTimeout,
        textType: Number(textType) // 添加文本类型字段
    };

    // 立即检查更新工具toolData构建结果
    console.log("=== 更新工具toolData构建后立即检查 ===");
    console.log("原始textType变量:", textType);
    console.log("Number(textType):", Number(textType));
    console.log("toolData.textType:", toolData.textType);
    console.log("toolData中是否包含textType:", 'textType' in toolData);
    console.log("toolData完整内容:", JSON.stringify(toolData, null, 2));
    console.log("=======================================");

    // 调试更新工具数据
    console.log("准备提交的更新工具数据:", JSON.stringify(toolData, null, 2));

    // 最终验证
    if (!toolData.description || toolData.description.trim() === '') {
        console.error("错误：更新工具描述为空！");
        showAlert('工具描述不能为空', 'warning');
        return;
    }

    console.log("✓ 更新工具所有验证通过，准备提交");

    // 根据工具类型获取特定字段
    if (toolType === 'HTTP') {
        const httpUrl = $('#updateHttpUrl').val().trim();
        const httpMethod = $('#updateHttpMethod').val();

        if (!httpUrl) {
            showAlert('请填写URL', 'warning');
            return;
        }

        // 修改参数名称以匹配后端预期
        toolData = {
            ...toolData,
            url: httpUrl,
            methodName: httpMethod,  // 确保使用 methodName 而不是 method
            headers: {}  // 初始化 headers 为对象而不是数组
        };

        // 获取请求头
        $('#updateHttpHeaderList .header-item').each(function() {
            const headerName = $(this).find('.header-name').val().trim();
            const headerValue = $(this).find('.header-value').val().trim();
            if (headerName && headerValue) {
                toolData.headers[headerName] = headerValue;  // 使用对象格式存储 headers
            }
        });

        // 获取参数
        const params = [];
        $('#updateHTTPParamList .param-item').each(function() {
            const paramName = $(this).find('.param-name').val().trim();
            const paramDescription = $(this).find('.param-description').val().trim();
            const paramRequired = $(this).find('.param-required').prop('checked');
            const paramType = $(this).find('.param-type').val();
            if (paramName && paramDescription) {
                params.push({
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                });
            }
        });
        toolData.toolParams = params;
    } else if (toolType === 'THRIFT') {
        const thriftAppKey = $('#updateThriftAppKey').val().trim();
        const thriftInterfaceName = $('#updateThriftInterfaceName').val().trim();
        const thriftMethodName = $('#updateThriftMethodName').val().trim();
        const thriftCell = $('#updateThriftCell').val().trim();
        const thriftIp = $('#updateThriftIp').val().trim();
        const thriftPort = $('#updateThriftPort').val().trim();

        // 验证THRIFT特定字段
        if (!thriftAppKey || !thriftInterfaceName || !thriftMethodName) {
            showAlert('请填写AppKey、接口全路径和方法名称', 'warning');
            return;
        }

        // 添加THRIFT特定字段 - 参数名称正确
        toolData.appKey = thriftAppKey;
        toolData.interfaceName = thriftInterfaceName;
        toolData.methodName = thriftMethodName;  // 正确使用 methodName
        toolData.cell = thriftCell;
        toolData.ip = thriftIp;
        toolData.port = thriftPort;
        // 添加是否是长连接任务字段
        toolData.isLongTermTask = $('#updateIsLongTermTask').prop('checked') ? 1 : 0;

        // 获取Avitor脚本内容
        const thriftAvitorScript = getEditorContent('updateThriftAvitorScript');
        toolData.avitorScriptCode = thriftAvitorScript;

        console.log("THRIFT Avitor脚本内容:", thriftAvitorScript);

        // 获取参数
        const params = [];
        $('#updateTHRIFTParamList .param-item').each(function() {
            const paramName = $(this).find('.param-name').val().trim();
            const paramDescription = $(this).find('.param-description').val().trim();
            const paramRequired = $(this).find('.param-required').prop('checked');
            const paramType = $(this).find('.param-type').val();
            let paramData = {
                name: paramName,
                description: paramDescription,
                required: paramRequired,
                type: paramType || 'String'
            };

            // 如果是自定义对象类型，添加自定义类型路径
            if (paramType === 'CustomObject') {
                paramData.customType = $(this).find('.param-custom-type').val().trim();
            }

            if (paramName && paramDescription) {
                params.push(paramData);
            }
        });
        toolData.toolParams = params;
    } else if (toolType === 'PIGEON') {
        const pigeonAppKey = $('#updatePigeonAppKey').val().trim();
        const pigeonInterfaceName = $('#updatePigeonInterfaceName').val().trim();
        const pigeonMethodName = $('#updatePigeonMethodName').val().trim();
        const pigeonCell = $('#updatePigeonCell').val().trim();

        // 验证PIGEON特定字段
        if (!pigeonAppKey || !pigeonInterfaceName || !pigeonMethodName) {
            showAlert('请填写AppKey、接口全路径和方法名称', 'warning');
            return;
        }

        // 添加PIGEON特定字段 - 参数名称正确
        toolData.appKey = pigeonAppKey;
        toolData.interfaceName = pigeonInterfaceName;
        toolData.methodName = pigeonMethodName;  // 正确使用 methodName
        toolData.cell = pigeonCell;

        // 获取Avitor脚本内容
        const pigeonAvitorScript = getEditorContent('updatePigeonAvitorScript');
        toolData.avitorScriptCode = pigeonAvitorScript;

        console.log("PIGEON Avitor脚本内容:", pigeonAvitorScript);

        // 获取参数
        const params = [];
        $('#updatePIGEONParamList .param-item').each(function() {
            const paramName = $(this).find('.param-name').val().trim();
            const paramDescription = $(this).find('.param-description').val().trim();
            const paramRequired = $(this).find('.param-required').prop('checked');
            const paramType = $(this).find('.param-type').val();
            let paramData = {
                name: paramName,
                description: paramDescription,
                required: paramRequired,
                type: paramType || 'String'
            };

            // 如果是自定义对象类型，添加自定义类型路径
            if (paramType === 'CustomObject') {
                paramData.customType = $(this).find('.param-custom-type').val().trim();
            }

            if (paramName && paramDescription) {
                params.push(paramData);
            }
        });
        toolData.toolParams = params;

        // 检查更新工具THRIFT处理后的toolData
        console.log("=== 更新工具THRIFT处理后toolData检查 ===");
        console.log("toolData.textType:", toolData.textType);
        console.log("toolData中是否包含textType:", 'textType' in toolData);
        console.log("========================================");
    }

    console.log("准备发送的更新工具数据:", toolData);

    // 确保textType字段存在
    if (!toolData.hasOwnProperty('textType') || toolData.textType === undefined || toolData.textType === null) {
        console.error("❌ 更新工具 - textType字段丢失！重新设置...");
        toolData.textType = Number(textType) || 2;
    }

    console.log("=== 更新工具最终发送前的数据检查 ===");
    console.log("textType字段存在:", toolData.hasOwnProperty('textType'));
    console.log("textType值:", toolData.textType);
    console.log("textType类型:", typeof toolData.textType);
    console.log("完整数据:", JSON.stringify(toolData, null, 2));
    console.log("请求URL:", '/mcp/api/tools/updateTool?mcpServerId=' + mcpServerId);
    console.log("===============================");

    // 强制确保textType字段存在
    toolData.textType = Number(textType) || 2;

    console.log("🔧 强制修复后的更新toolData:", JSON.stringify(toolData, null, 2));

    // 发送更新工具请求
    $.ajax({
        url: '/mcp/api/tools/updateTool?mcpServerId=' + mcpServerId,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(toolData),
        success: function(response) {
            console.log("更新工具响应:", response);
            if (response.code === 0) {
                showAlert('工具更新成功', 'success');
                $('#updateToolModal').modal('hide');
                // 重新加载工具列表
                if (typeof loadToolsByMcpServerId === 'function') {
                    loadToolsByMcpServerId(mcpServerId);
                }
            } else {
                console.error("更新工具失败:", response);
                showAlert('工具更新失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error("更新工具错误:", {
                status: status,
                error: error,
                responseText: xhr.responseText
            });
            showAlert('工具更新失败: ' + error, 'danger');
        }
    });
}
