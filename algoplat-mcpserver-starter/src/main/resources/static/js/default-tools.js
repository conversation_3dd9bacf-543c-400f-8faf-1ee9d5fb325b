/**
 * 默认工具管理JavaScript文件
 */

// 获取工具类型对应的CSS类
function getToolTypeClass(type) {
    switch (type) {
        case 'HTTP':
            return 'tool-type-http';
        case 'THRIFT':
            return 'tool-type-thrift';
        case 'PIGEON':
            return 'tool-type-pigeon';
        case 'SystemDefault':
            return 'tool-type-systemdefault';
        case 'ToolAnnotation':
            return 'tool-type-toolannotation';
        default:
            return 'tool-type-system';
    }
}

/**
 * 加载所有默认工具
 * 直接调用getUsedSystemDefaultToolInMcpServer获取所有默认工具，包括已使用和未使用的
 */
function loadDefaultTools(mcpServerId) {
    console.log("加载默认工具列表，McpServerId:", mcpServerId, "类型:", typeof mcpServerId);

    if (!mcpServerId) {
        // 尝试从工具列表标题获取
        mcpServerId = $('#toolListTitle').attr('data-mcpserver-id');
        console.log("从工具列表标题获取 McpServerId:", mcpServerId, "类型:", typeof mcpServerId);

        if (!mcpServerId) {
            showAlert('McpServerId 为空，无法加载默认工具', 'danger');
            return;
        }
    }

    // 确保 mcpServerId 是数字类型
    mcpServerId = parseInt(mcpServerId, 10);
    if (isNaN(mcpServerId)) {
        console.error("McpServerId 不是有效的数字:", mcpServerId);
        showAlert('McpServerId 不是有效的数字', 'danger');
        return;
    }

    console.log("转换后的 McpServerId:", mcpServerId, "类型:", typeof mcpServerId);

    $('#defaultToolsList').html(`
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">加载中...</span>
            </div>
            <p>加载默认工具列表...</p>
        </div>
    `);

    // 添加时间戳，避免缓存
    const timestamp = new Date().getTime();

    // 使用getUsedSystemDefaultToolInMcpServer接口获取所有默认工具
    $.ajax({
        url: '/mcp/api/tools/getUsedSystemDefaultToolInMcpServer',
        type: 'GET',
        data: {
            mcpServerId: mcpServerId,
            _t: timestamp
        },
        success: function(response) {
            console.log("加载默认工具列表响应:", response);
            if (response.code === 0) {
                let defaultTools = [];
                try {
                    if (typeof response.data === 'string') {
                        defaultTools = JSON.parse(response.data);
                    } else if (Array.isArray(response.data)) {
                        defaultTools = response.data;
                    } else if (response.data) {
                        defaultTools = [response.data];
                    }

                    console.log("解析后的默认工具数据:", defaultTools);
                    if (!defaultTools) {
                        defaultTools = [];
                    }

                    // 确保每个工具都有usedInDefault字段，并且是布尔值
                    defaultTools.forEach(tool => {
                        // 强制转换为布尔值
                        tool.usedInDefault = tool.usedInDefault === true;
                        console.log(`工具 ${tool.name} 是否已被使用: ${tool.usedInDefault}, 类型: ${tool.type}`);
                    });
                } catch (e) {
                    console.error("解析默认工具数据失败", e);
                    defaultTools = [];
                }
                renderDefaultTools(defaultTools);
            } else {
                console.error("加载默认工具列表失败:", response.message);
                showAlert('加载默认工具列表失败: ' + response.message, 'danger');
                $('#defaultToolsList').html(`
                    <div class="alert alert-danger">
                        <i class="fa fa-exclamation-circle"></i> 加载默认工具列表失败: ${response.message}
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            console.error("加载默认工具列表请求错误:", {
                status: status,
                error: error,
                responseText: xhr.responseText
            });
            showAlert('加载默认工具列表失败: ' + error, 'danger');
            $('#defaultToolsList').html(`
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-circle"></i> 加载默认工具列表失败: ${error}
                </div>
            `);
        }
    });
}

/**
 * 渲染默认工具列表
 * 规则：
 * - 已选择的工具（usedInDefault为true），显示为已勾选且禁用
 * - 其他工具可以正常勾选
 */
function renderDefaultTools(tools) {
    const container = $('#defaultToolsList');
    container.empty();

    if (!tools || tools.length === 0) {
        container.html('<div class="alert alert-warning">暂无可用的默认工具</div>');
        return;
    }

    let html = '<table class="table table-hover"><tbody>';
    tools.forEach(tool => {
        const isUsed = tool.usedInDefault === true;
        const statusClass = isUsed ? 'bg-light' : '';
        const statusText = isUsed ? '<span class="text-success">(已添加)</span>' : '';

        html += `<tr class="${statusClass}">
            <td width="40">
                <input type="checkbox" class="default-tool-checkbox" 
                    value="${tool.name}" 
                    data-used-in-default="${isUsed}" 
                    ${isUsed ? 'checked disabled' : ''}>
            </td>
            <td>${tool.name}</td>
            <td>${tool.description || ''} ${statusText}</td>
        </tr>`;
    });
    html += '</tbody></table>';
    container.html(html);

    // 绑定复选框变更事件，以便实时更新保存按钮状态
    $('.default-tool-checkbox').on('change', function() {
        updateSaveButtonStatus();
    });

    // 初始更新保存按钮状态
    updateSaveButtonStatus();
}

/**
 * 保存选择的默认工具
 * 允许未选择任何工具时也能保存（即允许清空默认工具）
 */
function saveDefaultTools(mcpServerId) {
    console.log("开始保存默认工具，McpServerId:", mcpServerId, "类型:", typeof mcpServerId);
    if (!mcpServerId) {
        // 尝试从工具列表标题获取
        mcpServerId = $('#toolListTitle').attr('data-mcpserver-id');
        console.log("从工具列表标题获取 McpServerId:", mcpServerId, "类型:", typeof mcpServerId);

        if (!mcpServerId) {
            showAlert('McpServerId 为空，无法保存默认工具', 'danger');
            return;
        }
    }

    // 确保 mcpServerId 是数字类型
    mcpServerId = parseInt(mcpServerId, 10);
    if (isNaN(mcpServerId)) {
        console.error("McpServerId 不是有效的数字:", mcpServerId);
        showAlert('McpServerId 不是有效的数字', 'danger');
        return;
    }

    console.log("转换后的 McpServerId:", mcpServerId, "类型:", typeof mcpServerId);

    // 获取所有已选择的默认工具（包括已禁用的和新选择的）
    const allSelectedTools = [];

    // 获取所有已禁用但已勾选的复选框（即已存在的工具）
    $('.default-tool-checkbox:checked:disabled').each(function() {
        allSelectedTools.push($(this).val());
    });
    // 获取所有勾选的但未禁用的复选框（即用户新选择的工具）
    $('.default-tool-checkbox:checked:not(:disabled)').each(function() {
        allSelectedTools.push($(this).val());
    });

    console.log("所有选择的默认工具（包括已存在和新选择）:", allSelectedTools);

    // 获取新选择的工具数量
    const newSelectedToolsCount = $('.default-tool-checkbox:checked:not(:disabled)').length;
    // 获取已选择的工具数量
    const existingSelectedToolsCount = $('.default-tool-checkbox:checked:disabled').length;
    // 获取总工具数量
    const totalToolsCount = $('.default-tool-checkbox').length;

    // 显示加载状态
    const originalButtonText = $('#saveDefaultToolsBtn').html();
    $('#saveDefaultToolsBtn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 保存中...');

    // 创建表单数据
    const formData = new FormData();
    formData.append('mcpServerId', mcpServerId);

    // 添加多个同名参数
    allSelectedTools.forEach(tool => {
        formData.append('useDefaultToolNameList', tool);
    });

    // 使用fetch API发送请求
    fetch('/mcp/api/tools/saveDefaultToolForMcpServer', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(response => {
        console.log("保存默认工具响应:", response);
        // 恢复按钮状态
        $('#saveDefaultToolsBtn').prop('disabled', false).html(originalButtonText);

        if (response.code === 0) {
            // 根据保存结果显示不同的成功消息
            let successMessage = '默认工具保存成功';
            if (allSelectedTools.length === 0) {
                successMessage = '已清空所有默认工具';
            } else if (newSelectedToolsCount > 0) {
                successMessage = `成功添加 ${newSelectedToolsCount} 个新默认工具`;
            }

            showAlert(successMessage, 'success');
            $('#selectDefaultToolsModal').modal('hide');

            // 重新加载工具列表
            if (typeof loadToolsByMcpServerId === 'function') {
                loadToolsByMcpServerId(mcpServerId);
            } else {
                console.error("loadToolsByMcpServerId 函数未定义");
                // 尝试刷新页面
                location.reload();
            }
        } else {
            console.error("保存默认工具失败:", response.message);
            showAlert('默认工具保存失败: ' + response.message, 'danger');
        }
    })
    .catch(error => {
        console.error("保存默认工具请求错误:", error);
        // 恢复按钮状态
        $('#saveDefaultToolsBtn').prop('disabled', false).html(originalButtonText);
        showAlert('默认工具保存失败: ' + error, 'danger');
    });
}


// 文档加载完成后执行
$(function() {
    console.log("default-tools.js 文档加载完成");

    // 解绑之前可能存在的事件处理程序，避免重复绑定
    $('#selectDefaultToolsBtn').off('click');
    $('#saveDefaultToolsBtn').off('click');
    $('#selectDefaultToolsModal').off('shown.bs.modal');
    $('#selectDefaultToolsModal').off('hidden.bs.modal');

    // 选择默认工具按钮点击事件
    $('#selectDefaultToolsBtn').on('click', function() {
        console.log("点击选择默认工具按钮");

        // 获取当前 McpServerId
        const mcpServerId = $('#toolListTitle').attr('data-mcpserver-id');
        console.log("点击选择默认工具按钮，当前 McpServerId:", mcpServerId);

        if (!mcpServerId) {
            console.error("无法获取 McpServerId，toolListTitle 元素:", $('#toolListTitle'));
            showAlert('请先选择一个 McpServer', 'warning');
            return;
        }

        // 加载默认工具列表
        loadDefaultTools(mcpServerId);

        // 显示选择默认工具模态框
        $('#selectDefaultToolsModal').modal('show');
    });

    // 保存默认工具按钮点击事件
    $('#saveDefaultToolsBtn').on('click', function() {
        console.log("点击保存默认工具按钮");

        // 确保使用最新的 McpServerId
        const mcpServerId = $('#toolListTitle').attr('data-mcpserver-id');
        console.log("点击保存默认工具按钮，当前 McpServerId:", mcpServerId);

        if (!mcpServerId) {
            console.error("无法获取 McpServerId，toolListTitle 元素:", $('#toolListTitle'));
            showAlert('请先选择一个 McpServer', 'warning');
            return;
        }

        saveDefaultTools(mcpServerId);
    });

    // 打印当前页面上的 toolListTitle 元素
    console.log("当前页面上的 toolListTitle 元素:", $('#toolListTitle'));
    if ($('#toolListTitle').length) {
        console.log("toolListTitle 属性:", {
            text: $('#toolListTitle').text(),
            'data-mcpserver-id': $('#toolListTitle').attr('data-mcpserver-id')
        });
    }

    // 添加模态框显示后的事件处理
    $('#selectDefaultToolsModal').on('shown.bs.modal', function() {
        console.log("默认工具选择模态框已显示");
        // 调试工具状态
        if (typeof debugToolsStatus === 'function') {
            debugToolsStatus();
        }
    });

    // 添加模态框隐藏后的事件处理
    $('#selectDefaultToolsModal').on('hidden.bs.modal', function() {
        console.log("默认工具选择模态框已隐藏");
        // 清空默认工具列表
        $('#defaultToolsList').empty();
    });
});

// 检查复选框状态的辅助函数
function checkCheckboxStatus() {
    console.log("检查复选框状态:");
    $('.default-tool-checkbox').each(function() {
        const $checkbox = $(this);
        console.log(`复选框 ${$checkbox.val()}: 选中=${$checkbox.prop('checked')}, 禁用=${$checkbox.prop('disabled')}`);
    });
}

/**
 * 强制修复复选框状态的函数
 * 只对已经被用户选择过的工具（usedInDefault为true）进行强制选中和禁用
 * 并且修复Safari下tr的disabled属性导致的样式问题
 */
function forceFixCheckboxStatus() {
    $('.default-tool-checkbox').each(function() {
        const $checkbox = $(this);
        const $row = $checkbox.closest('tr');
        const isUsed = $checkbox.data('used-in-default') === true;

        if (isUsed) {
            // 确保已使用的工具复选框被选中且禁用
            $checkbox.prop('checked', true);
            $checkbox.prop('disabled', true);

            // 添加样式
            $row.addClass('bg-light');
            if (!$row.find('.text-success').length) {
                $row.find('td:last').append(' <span class="text-success">(已添加)</span>');
            }
        } else {
            // 确保未使用的工具复选框可以正常勾选
            $checkbox.prop('disabled', false);
        }
    });

    // 更新保存按钮状态
    updateSaveButtonStatus();
}


/**
 * 检查是否有新选择的工具，并更新保存按钮状态
 * 允许未选择任何工具时也能保存（即允许清空默认工具）
 */
function updateSaveButtonStatus() {
    // 获取用户新选择的默认工具数量（不包括已禁用的工具）
    const newSelectedToolsCount = $('.default-tool-checkbox:checked:not(:disabled)').length;
    // 获取已选择的默认工具数量（已禁用的工具）
    const existingSelectedToolsCount = $('.default-tool-checkbox:checked:disabled').length;
    // 获取所有工具数量
    const totalToolsCount = $('.default-tool-checkbox').length;

    console.log("用户新选择的默认工具数量:", newSelectedToolsCount);
    console.log("已存在的默认工具数量:", existingSelectedToolsCount);
    console.log("所有工具数量:", totalToolsCount);

    // 总是启用保存按钮，允许用户提交当前选择（包括清空所有选择）
    $('#saveDefaultToolsBtn').prop('disabled', false);

    // 更新保存按钮文本，提示用户当前选择状态
    let buttonText = '保存选择';
    if (newSelectedToolsCount > 0) {
        buttonText = `保存选择 (${newSelectedToolsCount} 个新工具)`;
    } else if (newSelectedToolsCount === 0 && existingSelectedToolsCount === 0) {
        buttonText = '清空所有默认工具';
    }
    $('#saveDefaultToolsBtn').html(buttonText);

    if (newSelectedToolsCount === 0 && existingSelectedToolsCount === 0) {
        console.log("保存按钮启用 - 未选择任何工具（清空所有默认工具）");
    } else if (newSelectedToolsCount === 0) {
        console.log("保存按钮启用 - 只有已存在的工具，没有新选择的工具");
    } else {
        console.log("保存按钮启用 - 有新选择的工具");
    }
}

/**
 * 调试工具状态
 */
function debugToolsStatus() {
    console.log("======= 调试工具状态 =======");

    // 获取所有工具
    const allTools = [];
    $('.default-tool-checkbox').each(function() {
        const $checkbox = $(this);
        allTools.push({
            name: $checkbox.val(),
            checked: $checkbox.prop('checked'),
            disabled: $checkbox.prop('disabled'),
            usedInDefault: $checkbox.data('used-in-default')
        });
    });

    console.log("所有工具:", allTools);

    // 获取已选择的工具（已禁用的工具）
    const selectedTools = allTools.filter(tool => tool.checked && tool.disabled);
    console.log("已选择的工具:", selectedTools);

    // 获取新选择的工具（用户新勾选的工具）
    const newSelectedTools = allTools.filter(tool => tool.checked && !tool.disabled);
    console.log("新选择的工具:", newSelectedTools);

    // 获取未选择的工具
    const unselectedTools = allTools.filter(tool => !tool.checked);
    console.log("未选择的工具:", unselectedTools);

    // 获取保存按钮状态
    console.log("保存按钮状态:", {
        disabled: $('#saveDefaultToolsBtn').prop('disabled'),
        text: $('#saveDefaultToolsBtn').html()
    });

    console.log("======= 调试工具状态结束 =======");
}

// 将函数暴露到全局作用域
window.loadDefaultTools = loadDefaultTools;
window.saveDefaultTools = saveDefaultTools;
window.checkCheckboxStatus = checkCheckboxStatus;
window.forceFixCheckboxStatus = forceFixCheckboxStatus;
window.updateSaveButtonStatus = updateSaveButtonStatus;
window.debugToolsStatus = debugToolsStatus;
