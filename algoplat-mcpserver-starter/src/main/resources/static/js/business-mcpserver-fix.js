/**
 * 业务场景和McpServer管理修复脚本
 * 解决重复提交和事件绑定问题
 */

$(document).ready(function() {
    // 防重复提交的状态管理
    let isCreatingBusinessLine = false;
    let isUpdatingBusinessLine = false;
    let isDeletingBusinessLine = false;
    let isCreatingMcpServer = false;
    let isUpdatingMcpServer = false;
    let isDeletingMcpServer = false;

    // 将状态变量暴露到全局作用域，用于测试
    window.businessMcpServerFix = {
        isCreatingBusinessLine: function() { return isCreatingBusinessLine; },
        isUpdatingBusinessLine: function() { return isUpdatingBusinessLine; },
        isDeletingBusinessLine: function() { return isDeletingBusinessLine; },
        isCreatingMcpServer: function() { return isCreatingMcpServer; },
        isUpdatingMcpServer: function() { return isUpdatingMcpServer; },
        isDeletingMcpServer: function() { return isDeletingMcpServer; },
        setCreatingBusinessLine: function(value) { isCreatingBusinessLine = value; },
        setUpdatingBusinessLine: function(value) { isUpdatingBusinessLine = value; },
        setDeletingBusinessLine: function(value) { isDeletingBusinessLine = value; },
        setCreatingMcpServer: function(value) { isCreatingMcpServer = value; },
        setUpdatingMcpServer: function(value) { isUpdatingMcpServer = value; },
        setDeletingMcpServer: function(value) { isDeletingMcpServer = value; }
    };

    // 解绑所有可能重复的事件绑定
    function unbindDuplicateEvents() {
        // 解绑业务场景相关事件
        $('#createBusinessLineForm').off('submit');
        $('#updateBusinessLineForm').off('submit');
        $('.confirm-delete-business-btn').off('click');
        $(document).off('click', '.confirm-delete-business-btn');

        // 解绑McpServer相关事件
        $('#createMcpServerForm').off('submit');
        $('#updateMcpServerForm').off('submit');
        $('.confirm-delete-mcpserver-btn').off('click');
        $(document).off('click', '.confirm-delete-mcpserver-btn');

        // 解绑模态框按钮事件
        $('#createBusinessLineModal .modal-footer button[type="submit"]').off('click');
        $('#updateBusinessLineModal .modal-footer button[type="submit"]').off('click');
        $('#createMcpServerModal .modal-footer button[type="submit"]').off('click');
        $('#updateMcpServerModal .modal-footer button[type="submit"]').off('click');
    }

    // 重新绑定事件（只绑定一次）
    function bindBusinessMcpServerEvents() {
        // === 业务场景相关事件 ===

        // 创建业务场景表单提交事件
        $('#createBusinessLineForm').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isCreatingBusinessLine) {
                console.log("正在创建业务场景中，忽略重复提交");
                return false;
            }

            console.log("创建业务场景表单提交事件触发");

            // 验证输入
            if (!validateInput($('#businessLineName'), '业务场景名称')) {
                return false;
            }

            // 设置创建状态
            isCreatingBusinessLine = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $('#createBusinessLineModal .modal-footer button[type="submit"]');
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 创建中...');

            // 调用创建函数
            createBusinessLineWithCallback(function(success) {
                // 恢复按钮状态
                isCreatingBusinessLine = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#createBusinessLineModal').modal('hide');
                    $('#createBusinessLineForm')[0].reset();
                }
            });

            return false;
        });

        // 更新业务场景表单提交事件
        $('#updateBusinessLineForm').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isUpdatingBusinessLine) {
                console.log("正在更新业务场景中，忽略重复提交");
                return false;
            }

            console.log("更新业务场景表单提交事件触发");

            // 设置更新状态
            isUpdatingBusinessLine = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $('#updateBusinessLineModal .modal-footer button[type="submit"]');
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 更新中...');

            // 调用更新函数
            updateBusinessLineWithCallback(function(success) {
                // 恢复按钮状态
                isUpdatingBusinessLine = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#updateBusinessLineModal').modal('hide');
                }
            });

            return false;
        });

        // 删除业务场景确认按钮事件
        $(document).on('click', '.confirm-delete-business-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isDeletingBusinessLine) {
                console.log("正在删除业务场景中，忽略重复提交");
                return false;
            }

            const businessLineId = $(this).data('id');

            // 设置删除状态
            isDeletingBusinessLine = true;

            // 禁用删除按钮并显示loading状态
            const $deleteBtn = $(this);
            const originalText = $deleteBtn.text();
            $deleteBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 删除中...');

            // 调用删除函数
            deleteBusinessLineWithCallback(businessLineId, function(success) {
                // 恢复按钮状态
                isDeletingBusinessLine = false;
                $deleteBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#confirmDeleteBusinessModal').modal('hide');
                }
            });

            return false;
        });

        // === McpServer相关事件 ===

        // 创建McpServer表单提交事件
        $('#createMcpServerForm').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isCreatingMcpServer) {
                console.log("正在创建McpServer中，忽略重复提交");
                return false;
            }

            console.log("创建McpServer表单提交事件触发");

            // 验证输入
            if (!validateInput($('#mcpServerName'), 'McpServer名称')) {
                return false;
            }

            // 设置创建状态
            isCreatingMcpServer = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $('#createMcpServerModal .modal-footer button[type="submit"]');
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 创建中...');

            // 调用创建函数
            createMcpServerWithCallback(function(success) {
                // 恢复按钮状态
                isCreatingMcpServer = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#createMcpServerModal').modal('hide');
                    $('#createMcpServerForm')[0].reset();
                }
            });

            return false;
        });

        // 更新McpServer表单提交事件
        $('#updateMcpServerForm').on('submit', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isUpdatingMcpServer) {
                console.log("正在更新McpServer中，忽略重复提交");
                return false;
            }

            console.log("更新McpServer表单提交事件触发");

            // 验证输入（如果名称字段可编辑的话）
            const nameInput = $('#updateMcpServerName');
            if (nameInput.length && !validateInput(nameInput, 'McpServer名称')) {
                return false;
            }

            // 设置更新状态
            isUpdatingMcpServer = true;

            // 禁用提交按钮并显示loading状态
            const $submitBtn = $('#updateMcpServerModal .modal-footer button[type="submit"]');
            const originalText = $submitBtn.text();
            $submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 更新中...');

            // 调用更新函数
            updateMcpServerWithCallback(function(success) {
                // 恢复按钮状态
                isUpdatingMcpServer = false;
                $submitBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#updateMcpServerModal').modal('hide');
                }
            });

            return false;
        });

        // 删除McpServer确认按钮事件
        $(document).on('click', '.confirm-delete-mcpserver-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();

            if (isDeletingMcpServer) {
                console.log("正在删除McpServer中，忽略重复提交");
                return false;
            }

            const mcpServerId = $(this).data('id');
            const businessLineId = $(this).data('business-line-id');

            // 设置删除状态
            isDeletingMcpServer = true;

            // 禁用删除按钮并显示loading状态
            const $deleteBtn = $(this);
            const originalText = $deleteBtn.text();
            $deleteBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 删除中...');

            // 调用删除函数
            deleteMcpServerWithCallback(mcpServerId, businessLineId, function(success) {
                // 恢复按钮状态
                isDeletingMcpServer = false;
                $deleteBtn.prop('disabled', false).text(originalText);

                if (success) {
                    $('#confirmDeleteMcpServerModal').modal('hide');
                }
            });

            return false;
        });
    }

    // === 带回调的业务场景操作函数 ===

    function createBusinessLineWithCallback(callback) {
        const businessLine = {
            businessLine: $('#businessLineName').val().trim(),
            description: $('#businessLineDescription').val().trim()
        };

        $.ajax({
            url: '/business/create',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(businessLine),
            timeout: 30000,
            success: function(response) {
                console.log("创建业务场景响应:", response);
                if (response.code === 0) {
                    showAlert('业务场景创建成功', 'success');
                    if (typeof loadBusinessLines === 'function') {
                        loadBusinessLines();
                    }
                    callback(true);
                } else {
                    showAlert('业务场景创建失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("创建业务场景错误:", error);
                showAlert('业务场景创建失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    function updateBusinessLineWithCallback(callback) {
        const businessLine = {
            id: $('#updateBusinessLineId').val(),
            description: $('#updateBusinessLineDescription').val().trim()
        };

        $.ajax({
            url: '/business/update',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(businessLine),
            timeout: 30000,
            success: function(response) {
                console.log("更新业务场景响应:", response);
                if (response.code === 0) {
                    showAlert('业务场景更新成功', 'success');
                    if (typeof loadBusinessLines === 'function') {
                        loadBusinessLines();
                    }
                    callback(true);
                } else {
                    showAlert('业务场景更新失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("更新业务场景错误:", error);
                showAlert('业务场景更新失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    function deleteBusinessLineWithCallback(businessLineId, callback) {
        $.ajax({
            url: '/business/delete/' + businessLineId,
            type: 'GET',
            timeout: 30000,
            success: function(response) {
                console.log("删除业务场景响应:", response);
                if (response.code === 0) {
                    showAlert('业务场景删除成功', 'success');
                    if (typeof loadBusinessLines === 'function') {
                        loadBusinessLines();
                    }
                    // 重置页面状态
                    $('#currentBusinessLine').text('选择业务场景');
                    $('#welcomePage').show();
                    $('#mcpServerListArea').hide();
                    $('#toolListArea').hide();
                    callback(true);
                } else {
                    showAlert('业务场景删除失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("删除业务场景错误:", error);
                showAlert('业务场景删除失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    // === 带回调的McpServer操作函数 ===

    function createMcpServerWithCallback(callback) {
        const mcpServer = {
            businessLineId: $('#businessLineId').val(),
            mcpServerName: $('#mcpServerName').val().trim(),
            owner: $('#mcpServerOwner').val().trim(),
            description: $('#mcpServerDescription').val().trim(),
            status: 'ACTIVE'
        };

        $.ajax({
            url: '/server/create',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(mcpServer),
            timeout: 30000,
            success: function(response) {
                console.log("创建McpServer响应:", response);
                if (response.code === 0) {
                    showAlert('McpServer创建成功', 'success');
                    if (typeof loadMcpServersByBusinessLineId === 'function') {
                        loadMcpServersByBusinessLineId(mcpServer.businessLineId);
                    }
                    callback(true);
                } else {
                    showAlert('McpServer创建失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("创建McpServer错误:", error);
                showAlert('McpServer创建失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    function updateMcpServerWithCallback(callback) {
        const mcpServer = {
            id: $('#updateMcpServerId').val(),
            description: $('#updateMcpServerDescription').val().trim(),
            owner: $('#updateMcpServerOwner').val().trim(),
            status: 'ACTIVE'
        };

        $.ajax({
            url: '/server/update',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(mcpServer),
            timeout: 30000,
            success: function(response) {
                console.log("更新McpServer响应:", response);
                if (response.code === 0) {
                    showAlert('McpServer更新成功', 'success');
                    // 获取当前业务场景ID
                    const businessLineId = $('#createMcpServerBtn').data('business-line-id');
                    if (typeof loadMcpServersByBusinessLineId === 'function') {
                        loadMcpServersByBusinessLineId(businessLineId);
                    }
                    callback(true);
                } else {
                    showAlert('McpServer更新失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("更新McpServer错误:", error);
                showAlert('McpServer更新失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    function deleteMcpServerWithCallback(mcpServerId, businessLineId, callback) {
        $.ajax({
            url: '/server/delete/' + mcpServerId,
            type: 'POST',
            timeout: 30000,
            success: function(response) {
                console.log("删除McpServer响应:", response);
                if (response.code === 0) {
                    showAlert('McpServer删除成功', 'success');
                    if (typeof loadMcpServersByBusinessLineId === 'function') {
                        loadMcpServersByBusinessLineId(businessLineId);
                    }
                    callback(true);
                } else {
                    showAlert('McpServer删除失败: ' + response.message, 'danger');
                    callback(false);
                }
            },
            error: function(xhr, status, error) {
                console.error("删除McpServer错误:", error);
                showAlert('McpServer删除失败: ' + error, 'danger');
                callback(false);
            }
        });
    }

    // 初始化修复
    function initFix() {
        console.log("初始化业务场景和McpServer修复脚本");

        // 解绑重复事件
        unbindDuplicateEvents();

        // 重新绑定事件
        bindBusinessMcpServerEvents();

        console.log("业务场景和McpServer修复脚本初始化完成");
    }

    // 页面加载完成后延迟执行修复
    setTimeout(initFix, 1500);

    // 监听业务场景和McpServer列表重新加载事件，重新应用修复
    $(document).on('businessLinesLoaded mcpServersLoaded', function() {
        console.log("检测到业务场景或McpServer列表重新加载，重新应用修复");
        setTimeout(function() {
            unbindDuplicateEvents();
            bindBusinessMcpServerEvents();
        }, 500);
    });
});

