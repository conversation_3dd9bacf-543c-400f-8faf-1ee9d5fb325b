/**
 * McpServer管理JavaScript文件
 * 修改于 2023-06-01，添加描述信息截断功能
 */

// 加载业务场景下的McpServer列表
function loadMcpServersByBusinessLineId(businessLineId) {
    $.ajax({
        url: '/server/by-business/' + businessLineId,
        type: 'GET',
        success: function(response) {
            if (response.code === 0) {
                let mcpServers = [];
                try {
                    mcpServers = JSON.parse(response.data);
                    if (!mcpServers) {
                        mcpServers = [];
                    }
                    // 确保mcpServers是数组
                    if (!Array.isArray(mcpServers)) {
                        mcpServers = [mcpServers];
                    }
                } catch (e) {
                    console.error("解析McpServer数据失败", e);
                    mcpServers = [];
                }
                renderMcpServers(mcpServers, businessLineId);
            } else {
                showAlert('加载McpServer列表失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('加载McpServer列表失败: ' + error, 'danger');
            // 添加示例数据（仅用于演示）
            const mcpServers = [
                {
                    id: 1,
                    mcpServerName: "SearchServer",
                    businessLineId: businessLineId,
                    status: "ACTIVE",
                    owner: "张三",
                    description: "搜索服务器，提供搜索相关功能",
                    toolInfoList: [
                        {
                            id: 1,
                            name: "SearchTool",
                            type: "HTTP",
                            owner: "张三",
                            description: "搜索工具"
                        },
                        {
                            id: 2,
                            name: "IndexTool",
                            type: "THRIFT",
                            owner: "李四",
                            description: "索引工具"
                        }
                    ]
                },
                {
                    id: 2,
                    mcpServerName: "RecommendServer",
                    businessLineId: businessLineId,
                    status: "ACTIVE",
                    owner: "李四",
                    description: "推荐服务器，提供推荐相关功能",
                    toolInfoList: [
                        {
                            id: 3,
                            name: "RecommendTool",
                            type: "HTTP",
                            owner: "李四",
                            description: "推荐工具"
                        }
                    ]
                }
            ];
            renderMcpServers(mcpServers, businessLineId);
        }
    });
}

// 渲染McpServer列表
function renderMcpServers(mcpServers, businessLineId) {
    const container = $('#mcpServerCards');
    container.empty();
    // 设置创建McpServer按钮的业务场景ID
    $('#businessLineId').val(businessLineId);
    $('#createMcpServerBtn').data('business-line-id', businessLineId);
    if (mcpServers.length === 0) {
        container.append('<div class="col-12"><div class="alert alert-info">暂无McpServer，请添加新的McpServer</div></div>');
        return;
    }

    // 获取业务场景名称
    let businessLineName = $('#currentBusinessLine').text();

    // 如果当前显示的是默认文本或undefined，尝试从sessionStorage获取
    if (businessLineName === '选择业务场景' || businessLineName === 'undefined') {
        // 首先尝试从sessionStorage获取
        const storedName = sessionStorage.getItem('currentBusinessLineName');
        if (storedName && storedName !== 'undefined') {
            businessLineName = storedName;
            // 更新页面显示
            $('#currentBusinessLine').text(businessLineName);
        } else {
            // 从侧边栏获取业务场景名称
            $('.sidebar-item').each(function() {
                if ($(this).data('id') == businessLineId) {
                    businessLineName = $(this).data('name');
                    if (businessLineName && businessLineName !== 'undefined') {
                        // 存储到sessionStorage
                        sessionStorage.setItem('currentBusinessLineName', businessLineName);
                        // 更新页面显示
                        $('#currentBusinessLine').text(businessLineName);
                    } else {
                        businessLineName = '未命名业务场景';
                        sessionStorage.setItem('currentBusinessLineName', businessLineName);
                        $('#currentBusinessLine').text(businessLineName);
                    }
                    return false; // 跳出循环
                }
            });
        }
    }

    mcpServers.forEach(function(mcpServer) {
        // 处理状态值，后端可能返回1表示启用中，0表示维护中，或者字符串'ACTIVE'/'INACTIVE'
        let status = 'ACTIVE'; // 默认为启用中

        if (mcpServer.status !== undefined) {
            if (mcpServer.status === 1 || mcpServer.status === '1' || mcpServer.status === 'ACTIVE') {
                status = 'ACTIVE';
            } else if (mcpServer.status === 0 || mcpServer.status === '0' || mcpServer.status === 'INACTIVE') {
                status = 'INACTIVE';
            }
        }
        const statusClass = status === 'ACTIVE' ? 'mcpserver-status-active' : 'mcpserver-status-inactive';
        const statusText = status === 'ACTIVE' ? '启用中' : '维护中';

        // 构建接入方式 - 使用当前页面的域名和协议
        const currentOrigin = window.location.origin; // 获取当前页面的协议+域名+端口
        const accessUrl = `${currentOrigin}/mcp/${businessLineName}/${mcpServer.mcpServerName}/sse`;

        // 构建工具预览
        let toolPreview = '';
        if (mcpServer.toolInfoList && mcpServer.toolInfoList.length > 0) {
            const previewCount = Math.min(3, mcpServer.toolInfoList.length);
            toolPreview += '<div class="tool-preview">';
            for (let i = 0; i < previewCount; i++) {
                const tool = mcpServer.toolInfoList[i];
                const toolTypeClass = getToolTypeClass(tool.type);
                toolPreview += `
                    <div class="tool-preview-item">
                        <span class="tool-type ${toolTypeClass}">${tool.type}</span>
                        ${tool.name}
                    </div>
                `;
            }
            if (mcpServer.toolInfoList.length > 3) {
                toolPreview += `<div class="text-muted">还有 ${mcpServer.toolInfoList.length - 3} 个工具...</div>`;
            }
            toolPreview += '</div>';
        } else {
            toolPreview = '<div class="text-muted">暂无工具</div>';
        }

        const card = `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="mcpserver-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            ${mcpServer.mcpServerName}
                            <span class="mcpserver-status ${statusClass}">${statusText}</span>
                        </h5>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-warning edit-mcpserver-btn" data-id="${mcpServer.id}" style="color: #fd7e14; border-color: #fd7e14;">
                                <i class="fa fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-mcpserver-btn" data-id="${mcpServer.id}" data-name="${mcpServer.mcpServerName}">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div>
                            <strong>接入方式:</strong>
                            <div class="access-info">${accessUrl}</div>
                        </div>
                        <div>
                            <strong>负责人:</strong> ${mcpServer.owner || '未设置'}
                        </div>
                        <div>
                            <strong>描述:</strong> ${mcpServer.description ? 
                                (function() {
                                    // 去除HTML标签
                                    const plainText = mcpServer.description.replace(/<[^>]*>/g, '');
                                    // 截断文本
                                    return plainText.length > 30 ? plainText.substring(0, 30) + '...' : plainText;
                                })() : 
                                '无描述'}
                        </div>
                        <div>
                            <strong>工具数量:</strong> ${mcpServer.toolInfoList ? mcpServer.toolInfoList.length : 0}
                            ${toolPreview}
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="button" class="btn btn-primary btn-block view-tools-btn" data-id="${mcpServer.id}" data-name="${mcpServer.mcpServerName}">
                            <i class="fa fa-wrench"></i> 查看工具
                        </button>
                    </div>
                </div>
            </div>
        `;
        container.append(card);
    });

    // 绑定查看工具按钮点击事件
    $('.view-tools-btn').on('click', function() {
        const mcpServerId = $(this).data('id');
        const mcpServerName = $(this).data('name');

        console.log("点击查看工具按钮, mcpServerId:", mcpServerId, "mcpServerName:", mcpServerName);

        // 更新工具列表标题
        $('#toolListTitle').text(mcpServerName + ' - 工具列表').attr('data-mcpserver-id', mcpServerId);

        // 加载该McpServer下的工具列表
        loadToolsByMcpServerId(mcpServerId);

        // 检查工具列表区域是否存在
        console.log("工具列表区域:", $('#toolListArea'));
        console.log("McpServer列表区域:", $('#mcpServerListArea'));

        // 隐藏McpServer列表区域，显示工具列表区域
        $('#mcpServerListArea').hide();
        $('#toolListArea').show();

        console.log("工具列表区域显示状态:", $('#toolListArea').is(':visible'));
    });

    // 绑定编辑McpServer按钮点击事件
    $('.edit-mcpserver-btn').on('click', function() {
        const mcpServerId = $(this).data('id');
        loadMcpServerForEdit(mcpServerId);
    });

    // 绑定删除McpServer按钮点击事件
    $('.delete-mcpserver-btn').on('click', function() {
        const mcpServerId = $(this).data('id');
        const mcpServerName = $(this).data('name');

        $('#confirmDeleteMcpServerModal .mcpserver-name').text(mcpServerName);
        $('#confirmDeleteMcpServerModal .confirm-delete-mcpserver-btn').data('id', mcpServerId);
        $('#confirmDeleteMcpServerModal .confirm-delete-mcpserver-btn').data('business-line-id', businessLineId);

        $('#confirmDeleteMcpServerModal').modal('show');
    });

    // 绑定创建McpServer按钮点击事件
    $('#createMcpServerBtn').on('click', function() {
        $('#createMcpServerModal').modal('show');
    });

    // 绑定返回按钮点击事件
    $('#backToMcpServerBtn').on('click', function() {
        $('#mcpServerListArea').show();
        $('#toolListArea').hide();
    });
}

// 创建McpServer
function createMcpServer() {
    // 验证名称
    if (!validateInput($('#mcpServerName'), 'McpServer名称')) {
        return;
    }

    const mcpServer = {
        businessLineId: $('#businessLineId').val(),
        mcpServerName: $('#mcpServerName').val().trim(),
        owner: $('#mcpServerOwner').val().trim(),
        description: $('#mcpServerDescription').val().trim(),
        status: 'ACTIVE' // 默认为活跃状态
    };
    $.ajax({
        url: '/server/create',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(mcpServer),
        success: function(response) {
            if (response.code === 0) {
                showAlert('McpServer创建成功', 'success');
                $('#createMcpServerModal').modal('hide');
                $('#createMcpServerForm')[0].reset();
                loadMcpServersByBusinessLineId(mcpServer.businessLineId);
            } else {
                showAlert('McpServer创建失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('McpServer创建失败: ' + error, 'danger');
        }
    });
}

// 更新McpServer
function updateMcpServer() {
    // 验证名称（如果名称字段可编辑的话）
    const nameInput = $('#updateMcpServerName');
    if (nameInput.length && !validateInput(nameInput, 'McpServer名称')) {
        return;
    }

    const mcpServer = {
        id: $('#updateMcpServerId').val(),
        description: $('#updateMcpServerDescription').val().trim(),
        owner: $('#updateMcpServerOwner').val().trim(),
        status: 'ACTIVE' // 默认为活跃状态
    };
    $.ajax({
        url: '/server/update',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(mcpServer),
        success: function(response) {
            if (response.code === 0) {
                showAlert('McpServer更新成功', 'success');
                $('#updateMcpServerModal').modal('hide');
                // 获取当前业务场景ID
                const businessLineId = $('#createMcpServerBtn').data('business-line-id');
                loadMcpServersByBusinessLineId(businessLineId);
            } else {
                showAlert('McpServer更新失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('McpServer更新失败: ' + error, 'danger');
        }
    });
}

// 删除McpServer
function deleteMcpServer(mcpServerId, businessLineId) {
    $.ajax({
        url: '/server/delete/' + mcpServerId,
        type: 'POST',
        success: function(response) {
            if (response.code === 0) {
                showAlert('McpServer删除成功', 'success');
                $('#confirmDeleteMcpServerModal').modal('hide');
                loadMcpServersByBusinessLineId(businessLineId);
            } else {
                showAlert('McpServer删除失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('McpServer删除失败: ' + error, 'danger');
        }
    });
}

// 加载McpServer详情用于编辑
function loadMcpServerForEdit(mcpServerId) {
    $.ajax({
        url: '/server/detail/' + mcpServerId,
        type: 'GET',
        success: function(response) {
            if (response.code === 0) {
                let mcpServer;
                try {
                    mcpServer = JSON.parse(response.data);
                    if (!mcpServer) {
                        showAlert('McpServer数据为空', 'danger');
                        return;
                    }
                } catch (e) {
                    console.error("解析McpServer数据失败", e);
                    showAlert('解析McpServer数据失败', 'danger');
                    return;
                }
                $('#updateMcpServerId').val(mcpServer.id);
                $('#updateMcpServerName').val(mcpServer.mcpServerName);
                $('#updateMcpServerDescription').val(mcpServer.description);
                $('#updateMcpServerOwner').val(mcpServer.owner);
                $('#updateMcpServerModal').modal('show');
            } else {
                showAlert('加载McpServer详情失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('加载McpServer详情失败: ' + error, 'danger');
        }
    });
}

// 获取工具类型对应的CSS类
function getToolTypeClass(type) {
    switch (type) {
        case 'HTTP':
            return 'tool-type-http';
        case 'THRIFT':
            return 'tool-type-thrift';
        case 'PIGEON':
            return 'tool-type-pigeon';
        case 'SystemDefault':
            return 'tool-type-systemdefault';
        case 'ToolAnnotation':
            return 'tool-type-toolannotation';
        default:
            return 'tool-type-system';
    }
}

// 文档加载完成后执行
$(document).ready(function() {
    console.log("页面DOM检查:");
    console.log("工具列表区域:", $('#toolListArea').length ? "存在" : "不存在");
    console.log("工具卡片容器:", $('#toolCards').length ? "存在" : "不存在");
    console.log("McpServer列表区域:", $('#mcpServerListArea').length ? "存在" : "不存在");

    // 加载业务场景列表
    loadBusinessLines();

    // McpServer搜索按钮点击事件
    $('#searchMcpServerBtn').on('click', function() {
        searchMcpServer();
    });

    // McpServer搜索框回车事件
    $('#searchMcpServerName, #searchMcpServerOwner').on('keypress', function(e) {
        if (e.which === 13) {
            searchMcpServer();
        }
    });

    // 重置McpServer搜索按钮点击事件
    $('#resetMcpServerSearchBtn').on('click', function() {
        resetMcpServerSearch();
    });
});

// 文档加载完成后执行
$(document).ready(function() {
    // 创建McpServer表单提交事件
    $('#createMcpServerForm').on('submit', function(e) {
        e.preventDefault();
        if (validateInput($('#mcpServerName'), 'McpServer名称')) {
            createMcpServer();
        }
    });

    // 更新McpServer表单提交事件
    $('#updateMcpServerForm').on('submit', function(e) {
        e.preventDefault();
        const nameInput = $('#updateMcpServerName');
        if (!nameInput.length || validateInput(nameInput, 'McpServer名称')) {
            updateMcpServer();
        }
    });

    // 删除McpServer确认按钮点击事件
    $('.confirm-delete-mcpserver-btn').on('click', function() {
        const mcpServerId = $(this).data('id');
        const businessLineId = $(this).data('business-line-id');
        deleteMcpServer(mcpServerId, businessLineId);
    });
});

// 搜索McpServer
function searchMcpServer() {
    const businessLineId = sessionStorage.getItem('currentBusinessLineId');
    const mcpServerName = $('#searchMcpServerName').val().trim();
    const owner = $('#searchMcpServerOwner').val().trim();

    // 如果两个搜索条件都为空，则加载所有McpServer
    if (!mcpServerName && !owner) {
        loadMcpServersByBusinessLineId(businessLineId);
        return;
    }

    // 如果只有名称不为空，使用名称搜索
    if (mcpServerName && !owner) {
        $.ajax({
            url: '/server/by-name/' + mcpServerName,
            type: 'GET',
            success: function(response) {
                handleSearchResponse(response, businessLineId);
            },
            error: function(xhr, status, error) {
                showAlert('搜索McpServer失败: ' + error, 'danger');
                renderMcpServers([], businessLineId);
            }
        });
        return;
    }

    // 如果只有负责人不为空，或者两个条件都不为空，使用负责人搜索
    // 注意：如果两个条件都不为空，我们会在前端对结果进行过滤
    $.ajax({
        url: '/server/getByOwner',
        type: 'GET',
        data: {
            businessLineId: businessLineId,
            owner: owner
        },
        success: function(response) {
            if (mcpServerName) {
                // 如果名称不为空，需要在前端对结果进行过滤
                if (response.code === 0) {
                    let mcpServers = [];
                    try {
                        mcpServers = JSON.parse(response.data);
                        if (!mcpServers) {
                            mcpServers = [];
                        }
                        // 按名称过滤
                        mcpServers = mcpServers.filter(server =>
                            server.mcpServerName && server.mcpServerName.toLowerCase().includes(mcpServerName.toLowerCase())
                        );
                        // 创建新的响应对象
                        const filteredResponse = {
                            code: 0,
                            message: "搜索成功",
                            data: JSON.stringify(mcpServers)
                        };
                        handleSearchResponse(filteredResponse, businessLineId);
                    } catch (e) {
                        console.error("解析McpServer数据失败", e);
                        showAlert('搜索McpServer失败: 数据解析错误', 'danger');
                        renderMcpServers([], businessLineId);
                    }
                } else {
                    showAlert('搜索McpServer失败: ' + response.message, 'danger');
                    renderMcpServers([], businessLineId);
                }
            } else {
                // 如果只有负责人不为空，直接使用响应结果
                handleSearchResponse(response, businessLineId);
            }
        },
        error: function(xhr, status, error) {
            showAlert('搜索McpServer失败: ' + error, 'danger');
            renderMcpServers([], businessLineId);
        }
    });
}

// 处理搜索响应
function handleSearchResponse(response, businessLineId) {
    if (response.code === 0) {
        let mcpServers = [];
        try {
            mcpServers = JSON.parse(response.data);
            if (!mcpServers) {
                mcpServers = [];
            }
            // 确保mcpServers是数组
            if (!Array.isArray(mcpServers)) {
                mcpServers = [mcpServers];
            }
        } catch (e) {
            console.error("解析McpServer数据失败", e);
            mcpServers = [];
        }
        renderMcpServers(mcpServers, businessLineId);

        // 如果没有搜索结果，显示提示
        if (mcpServers.length === 0) {
            $('#mcpServerCards').html('<div class="col-12"><div class="alert alert-info">未找到匹配的McpServer，请尝试其他搜索条件</div></div>');
        }
    } else {
        showAlert('搜索McpServer失败: ' + response.message, 'danger');
        renderMcpServers([], businessLineId);
    }
}

// 重置McpServer搜索
function resetMcpServerSearch() {
    $('#searchMcpServerName').val('');
    $('#searchMcpServerOwner').val('');
    const businessLineId = sessionStorage.getItem('currentBusinessLineId');
    loadMcpServersByBusinessLineId(businessLineId);
}

// 验证是否包含中文字符
function containsChinese(str) {
    return /[\u4e00-\u9fa5]/.test(str);
}

// 验证输入字段
function validateInput(input, fieldName) {
    const value = input.val().trim();
    if (containsChinese(value)) {
        showAlert(`${fieldName}不能包含中文字符，请使用英文字母、数字和下划线`, 'warning');
        input.addClass('is-invalid');
        return false;
    }
    input.removeClass('is-invalid');
    return true;
}

// 添加实时验证
$(document).ready(function() {
    // 为McpServer名称输入框添加实时验证
    $('#mcpServerName').on('input', function() {
        validateInput($(this), 'McpServer名称');
    });

    // 如果更新表单中有名称字段，也添加验证
    $('#updateMcpServerName').on('input', function() {
        validateInput($(this), 'McpServer名称');
    });
});
