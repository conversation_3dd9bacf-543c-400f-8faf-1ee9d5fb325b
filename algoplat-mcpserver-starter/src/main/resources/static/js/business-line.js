/**
 * 业务场景管理JavaScript文件
 */

// 加载业务场景列表
function loadBusinessLines() {
    $.ajax({
        url: '/business/list',
        type: 'GET',
        success: function(response) {
            if (response.code === 0) {
                let businessLines = [];
                try {
                    businessLines = JSON.parse(response.data);
                    if (!businessLines) {
                        businessLines = [];
                    }
                    // 确保businessLines是数组
                    if (!Array.isArray(businessLines)) {
                        businessLines = [businessLines];
                    }
                } catch (e) {
                    console.error("解析业务场景数据失败", e);
                    businessLines = [];
                }
                renderBusinessLinesSidebar(businessLines);
            } else {
                showAlert('加载业务场景列表失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('加载业务场景列表失败: ' + error, 'danger');
            renderBusinessLinesSidebar([]);
        }
    });
}

// 渲染业务场景侧边栏
function renderBusinessLinesSidebar(businessLines, isSearchResult) {
    const container = $('#businessLinesList');
    container.empty();
    if (businessLines.length === 0) {
        if (isSearchResult) {
            container.append('<div class="p-3 text-center">未找到匹配的业务场景，请尝试其他关键词</div>');
        } else {
            container.append('<div class="p-3 text-center">暂无业务场景，请创建新的业务场景</div>');
        }
        return;
    }
    businessLines.forEach(function(businessLine) {
        // 确保业务场景名称不为undefined
        // 尝试多种可能的字段名，优先使用businessLine，其次是businessLineName
        const businessLineName = businessLine.businessLine || businessLine.businessLineName;
        const displayName = businessLineName && businessLineName !== 'undefined'
            ? businessLineName
            : '未命名业务场景-' + businessLine.id;

        const item = `
            <div class="sidebar-item" data-id="${businessLine.id}" data-name="${displayName}">
                <span class="sidebar-item-name">${displayName}</span>
                <div class="sidebar-item-actions">
                    <button type="button" class="edit-business-line-btn" data-id="${businessLine.id}" data-name="${displayName}" data-description="${businessLine.description || ''}">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button type="button" class="delete-business-line-btn" data-id="${businessLine.id}" data-name="${displayName}">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(item);
    });

    // 绑定业务场景相关事件
    bindBusinessLineEvents();
}

// 绑定业务场景相关事件
function bindBusinessLineEvents() {
    // 绑定业务场景点击事件
    $('.sidebar-item').off('click').on('click', function(e) {
        if (!$(e.target).hasClass('edit-business-line-btn') && !$(e.target).hasClass('delete-business-line-btn') &&
            !$(e.target).hasClass('fa-edit') && !$(e.target).hasClass('fa-trash')) {
            const businessLineId = $(this).data('id');
            const businessLineName = $(this).data('name');

            // 确保业务场景名称不为undefined
            if (businessLineName && businessLineName !== 'undefined') {
                // 更新当前业务场景
                $('#currentBusinessLine').text(businessLineName);
                // 同时存储业务场景名称到sessionStorage，以便在页面刷新后仍能获取
                sessionStorage.setItem('currentBusinessLineName', businessLineName);
                sessionStorage.setItem('currentBusinessLineId', businessLineId);
            } else {
                $('#currentBusinessLine').text('未命名业务场景');
                sessionStorage.setItem('currentBusinessLineName', '未命名业务场景');
                sessionStorage.setItem('currentBusinessLineId', businessLineId);
            }
            // 加载该业务场景下的McpServer列表
            loadMcpServersByBusinessLineId(businessLineId);

            // 添加活动状态
            $('.sidebar-item').removeClass('active');
            $(this).addClass('active');

            // 隐藏欢迎页面，显示McpServer列表区域
            $('#welcomePage').hide();
            $('#mcpServerListArea').show();
            $('#toolListArea').hide();
        }
    });

    // 绑定编辑业务场景按钮点击事件
    $('.edit-business-line-btn').off('click').on('click', function(e) {
        e.stopPropagation();
        const businessLineId = $(this).data('id');
        const businessLineName = $(this).data('name');

        // 先从后端获取最新的业务场景信息
        $.ajax({
            url: '/business/getBusinessLineById',
            type: 'GET',
            data: {id: businessLineId},
            success: function(response) {
                if (response.code === 0) {
                    try {
                        // 解析业务场景数据
                        const businessLine = JSON.parse(response.data);
                        if (businessLine) {
                            // 填充表单
                            $('#updateBusinessLineId').val(businessLine.id);
                            $('#updateBusinessLineName').val(businessLine.businessLine || businessLineName);
                            $('#updateBusinessLineDescription').val(businessLine.description || '');

                            // 显示模态框
                            $('#updateBusinessLineModal').modal('show');
                        } else {
                            showAlert('获取业务场景详情失败: 数据为空', 'warning');
                        }
                    } catch (e) {
                        console.error("解析业务场景数据失败", e);
                        // 如果解析失败，使用按钮上的数据作为备选
                        $('#updateBusinessLineId').val(businessLineId);
                        $('#updateBusinessLineName').val(businessLineName);
                        $('#updateBusinessLineDescription').val($(e.currentTarget).data('description') || '');

                        $('#updateBusinessLineModal').modal('show');
                    }
                } else {
                    showAlert('获取业务场景详情失败: ' + response.message, 'warning');
                    // 如果请求失败，使用按钮上的数据作为备选
                    $('#updateBusinessLineId').val(businessLineId);
                    $('#updateBusinessLineName').val(businessLineName);
                    $('#updateBusinessLineDescription').val($(e.currentTarget).data('description') || '');

                    $('#updateBusinessLineModal').modal('show');
                }
            },
            error: function(xhr, status, error) {
                showAlert('获取业务场景详情失败: ' + error, 'warning');
                // 如果请求失败，使用按钮上的数据作为备选
                $('#updateBusinessLineId').val(businessLineId);
                $('#updateBusinessLineName').val(businessLineName);
                $('#updateBusinessLineDescription').val($(e.currentTarget).data('description') || '');

                $('#updateBusinessLineModal').modal('show');
            }
        });
    });

    // 绑定删除业务场景按钮点击事件
    $('.delete-business-line-btn').off('click').on('click', function(e) {
        e.stopPropagation();
        const businessLineId = $(this).data('id');
        const businessLineName = $(this).data('name');

        $('#confirmDeleteBusinessModal .business-name').text(businessLineName);
        $('#confirmDeleteBusinessModal .confirm-delete-business-btn').data('id', businessLineId);

        $('#confirmDeleteBusinessModal').modal('show');
    });
}

// 创建业务场景
function createBusinessLine() {
    // 验证业务场景名称
    if (!validateInput($('#businessLineName'), '业务场景名称')) {
        return;
    }

    const businessLine = {
        businessLine: $('#businessLineName').val().trim(),
        description: $('#businessLineDescription').val().trim()
    };
    $.ajax({
        url: '/business/create',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(businessLine),
        success: function(response) {
            if (response.code === 0) {
                showAlert('业务场景创建成功', 'success');
                $('#createBusinessLineModal').modal('hide');
                $('#createBusinessLineForm')[0].reset();
                loadBusinessLines();
            } else {
                showAlert('业务场景创建失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('业务场景创建失败: ' + error, 'danger');
        }
    });
}

// 更新业务场景
function updateBusinessLine() {
    const businessLine = {
        id: $('#updateBusinessLineId').val(),
        description: $('#updateBusinessLineDescription').val().trim()
    };
    $.ajax({
        url: '/business/update',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(businessLine),
        success: function(response) {
            if (response.code === 0) {
                showAlert('业务场景更新成功', 'success');
                $('#updateBusinessLineModal').modal('hide');
                loadBusinessLines();
            } else {
                showAlert('业务场景更新失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('业务场景更新失败: ' + error, 'danger');
        }
    });
}

// 删除业务场景
function deleteBusinessLine(businessLineId) {
    $.ajax({
        url: '/business/delete/' + businessLineId,
        type: 'GET',
        success: function(response) {
            if (response.code === 0) {
                showAlert('业务场景删除成功', 'success');
                $('#confirmDeleteBusinessModal').modal('hide');
                loadBusinessLines();

                // 重置页面状态
                $('#currentBusinessLine').text('选择业务场景');
                $('#welcomePage').show();
                $('#mcpServerListArea').hide();
                $('#toolListArea').hide();
            } else {
                showAlert('业务场景删除失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('业务场景删除失败: ' + error, 'danger');
        }
    });
}

// 搜索业务场景
function searchBusinessLine(name) {
    if (!name) {
        loadBusinessLines();
        return;
    }
    $.ajax({
        url: '/business/search',
        type: 'GET',
        data: {name: name},
        success: function(response) {
            if (response.code === 0) {
                let businessLines = [];
                try {
                    businessLines = JSON.parse(response.data);
                    if (!businessLines) {
                        businessLines = [];
                    }
                    // 确保businessLines是数组
                    if (!Array.isArray(businessLines)) {
                        businessLines = [businessLines];
                    }
                } catch (e) {
                    console.error("解析业务场景数据失败", e);
                    businessLines = [];
                }
                if (businessLines.length === 0) {
                    // 使用 search 图标和灰色背景
                    showAlert('搜索业务场景: 业务线不存在', 'search');
                }
                renderBusinessLinesSidebar(businessLines, true);
            } else {
                showAlert('搜索业务场景: ' + response.message, 'warning');
            }
        },
        error: function(xhr, status, error) {
            showAlert('搜索业务场景失败: ' + error, 'warning');
            renderBusinessLinesSidebar([], true);
        }
    });
}

// 文档加载完成后执行
$(document).ready(function() {
    // 加载业务场景列表
    loadBusinessLines();
    // 创建业务场景表单提交事件
    $('#createBusinessLineForm').on('submit', function(e) {
        e.preventDefault();
        if (validateInput($('#businessLineName'), '业务场景名称')) {
            createBusinessLine();
        }
    });
    // 更新业务场景表单提交事件
    $('#updateBusinessLineForm').on('submit', function(e) {
        e.preventDefault();
        updateBusinessLine();
    });
    // 删除业务场景确认按钮点击事件
    $(document).on('click', '.confirm-delete-business-btn', function() {
        const businessLineId = $(this).data('id');
        deleteBusinessLine(businessLineId);
    });
    // 业务场景搜索按钮点击事件
    $('#searchBusinessLineBtn').on('click', function() {
        const searchTerm = $('#searchBusinessLineName').val().trim();
        searchBusinessLine(searchTerm);
    });
    // 业务场景搜索框回车事件
    $('#searchBusinessLineName').on('keyup', function(e) {
        // 只有在按下回车键(keyCode 13)时才执行搜索
        if (e.keyCode === 13) {
            const searchTerm = $(this).val().trim();
            searchBusinessLine(searchTerm);
        }
    });

    // 首页链接点击事件
    $('#homeLink').on('click', function(e) {
        e.preventDefault();
        $('#currentBusinessLine').text('选择业务场景');
        $('#welcomePage').show();
        $('#mcpServerListArea').hide();
        $('#toolListArea').hide();
        $('.sidebar-item').removeClass('active');
    });

    // 为业务场景名称输入框添加实时验证
    $('#businessLineName').on('input', function() {
        validateInput($(this), '业务场景名称');
    });
});

// 添加实时验证
$(document).ready(function() {
    // 为业务场景名称输入框添加实时验证
    $('#businessLineName').on('input', function() {
        validateInput($(this), '业务场景名称');
    });

    // 创建业务场景表单提交事件
    $('#createBusinessLineForm').on('submit', function(e) {
        e.preventDefault();
        if (validateInput($('#businessLineName'), '业务场景名称')) {
            createBusinessLine();
        }
    });
});

// 验证是否包含中文字符
function containsChinese(str) {
    return /[\u4e00-\u9fa5]/.test(str);
}

// 验证输入字段
function validateInput(input, fieldName) {
    const value = input.val().trim();
    if (containsChinese(value)) {
        showAlert(`${fieldName}不能包含中文字符，请使用英文字母、数字和下划线`, 'warning');
        input.addClass('is-invalid');
        return false;
    }
    input.removeClass('is-invalid');
    return true;
}
