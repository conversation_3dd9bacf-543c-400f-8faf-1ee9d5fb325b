/**
 * 通用功能JavaScript文件
 */

// 显示顶层提示信息（保持向后兼容）
function showToast(message, type) {
    showAlert(message, type);
}

// 显示提示信息
function showAlert(message, type) {
    // 移除现有的提示框
    $('.alert-float').remove();

    // 根据类型选择图标和样式
    let iconClass, backgroundColor;
    switch(type) {
        case 'success':
            iconClass = 'fas fa-check-circle';  // ✓ 勾号
            backgroundColor = 'rgba(40, 167, 69, 0.95)';
            break;
        case 'danger':
            iconClass = 'fas fa-times-circle';  // × 叉号
            backgroundColor = 'rgba(220, 53, 69, 0.95)';
            break;
        case 'warning':
            iconClass = 'fas fa-exclamation-triangle';  // ⚠ 警告三角
            backgroundColor = 'rgba(255, 193, 7, 0.95)';
            break;
        case 'info':
            iconClass = 'fas fa-info-circle';  // ℹ 信息图标
            backgroundColor = 'rgba(23, 162, 184, 0.95)';
            break;
        case 'search':  // 搜索无结果
            iconClass = 'fas fa-search';  // 🔍 搜索图标
            backgroundColor = 'rgba(108, 117, 125, 0.95)';  // 使用灰色背景
            break;
        default:
            iconClass = 'fas fa-info-circle';
            backgroundColor = 'rgba(23, 162, 184, 0.95)';
    }

    // 创建新的提示框
    const alertDiv = $(`
        <div class="alert-float">
            <i class="${iconClass}"></i>
            <span class="alert-message">${message}</span>
        </div>
    `);

    // 添加到页面
    $('body').append(alertDiv);

    // 应用样式
    alertDiv.css({
        'position': 'fixed',
        'top': '20px',
        'left': '50%',
        'transform': 'translateX(-50%)',
        'display': 'flex',
        'align-items': 'center',
        'justify-content': 'center',
        'padding': '12px 20px',
        'min-width': '300px',
        'border-radius': '4px',
        'background-color': backgroundColor,
        'color': type === 'warning' ? '#000' : '#fff',
        'box-shadow': '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)',
        'z-index': '9999999',
        'opacity': '0',
        'text-align': 'center'
    });

    // 设置图标样式
    alertDiv.find('i').css({
        'margin-right': '8px',
        'font-size': '16px',
        'display': 'inline-block',
        'vertical-align': 'middle'
    });

    // 触发动画
    setTimeout(() => {
        alertDiv.css({
            'opacity': '1',
            'transition': 'opacity 0.3s ease-in'
        });
    }, 10);

    // 3秒后自动消失
    setTimeout(() => {
        alertDiv.css({
            'opacity': '0',
            'transition': 'opacity 0.3s ease-out'
        });
        setTimeout(() => alertDiv.remove(), 300);
    }, 3000);
}

// 初始化富文本编辑器
function initSummernote() {
    $('.summernote').summernote({
        height: 200,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'underline', 'clear']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ],
        callbacks: {
            onBlur: function() {
                // 将富文本内容转换为纯文本（保留换行）
                const content = $(this).summernote('code');
                const plainText = $('<div>').html(content).text();
                $(this).data('plain-text', plainText);
            }
        }
    });
}

// 获取工具类型样式类
function getToolTypeClass(type) {
    switch(type) {
        case 'HTTP':
            return 'tool-type-http';
        case 'THRIFT':
            return 'tool-type-thrift';
        case 'PIGEON':
            return 'tool-type-pigeon';
        case 'SystemDefault':
        case 'ToolAnnotation':
            return 'tool-type-system';
        default:
            return '';
    }
}

// 添加参数项
function addParamItem(paramList, toolType, param) {
    const id = Date.now();
    let paramItem = `
        <div class="param-item">
            <div class="form-row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>参数名称</label>
                        <input type="text" class="form-control param-name" value="${param ? param.name : ''}" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>参数描述</label>
                        <input type="text" class="form-control param-description" value="${param ? param.description : ''}" required title="参数描述">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>参数类型</label>
                        <select class="form-control param-type" required>
                            <option value="String" ${param && param.type === 'String' ? 'selected' : ''}>String</option>
                            <option value="Integer" ${param && param.type === 'Integer' ? 'selected' : ''}>Integer</option>
                            <option value="Long" ${param && param.type === 'Long' ? 'selected' : ''}>Long</option>
                            <option value="Double" ${param && param.type === 'Double' ? 'selected' : ''}>Double</option>
                            <option value="Boolean" ${param && param.type === 'Boolean' ? 'selected' : ''}>Boolean</option>
                            <option value="List" ${param && param.type === 'List' ? 'selected' : ''}>List</option>
                            <option value="Set" ${param && param.type === 'Set' ? 'selected' : ''}>Set</option>
                            <option value="Map" ${param && param.type === 'Map' ? 'selected' : ''}>Map</option>
                            <option value="CustomObject" ${param && param.type === 'CustomObject' ? 'selected' : ''}>自定义对象类型</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>必填</label>
                        <div class="custom-control custom-switch mt-2">
                            <input type="checkbox" class="custom-control-input param-required" id="paramRequired${id}" ${param ? (param.required ? 'checked' : '') : 'checked'}>
                            <label class="custom-control-label" for="paramRequired${id}"></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-row custom-object-path" style="display: ${param && param.type === 'CustomObject' ? 'block' : 'none'}">
                <div class="col-md-12">
                    <div class="form-group">
                        <label>对象类型全路径</label>
                        <input type="text" class="form-control param-custom-type" placeholder="请输入自定义对象类型的全路径，如：com.example.dto.UserDTO" value="${param && param.customType ? param.customType : ''}">
                    </div>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-danger remove-param-btn">
                <i class="fa fa-trash"></i> 删除参数
            </button>
        </div>
    `;

    paramList.append(paramItem);
    // 绑定删除按钮事件
    paramList.find('.remove-param-btn').last().on('click', function() {
        $(this).closest('.param-item').remove();
    });
    // 绑定自定义对象类型显示/隐藏逻辑
    paramList.find('.param-type').last().on('change', function() {
        const customObjectPathDiv = $(this).closest('.param-item').find('.custom-object-path');
        if ($(this).val() === 'CustomObject') {
            customObjectPathDiv.show();
        } else {
            customObjectPathDiv.hide();
        }
    });
}

// 添加请求头项
function addHeaderItem(headerList) {
    const headerItem = `
        <div class="header-item mb-2">
            <div class="form-row">
                <div class="col-md-5">
                    <input type="text" class="form-control header-name" placeholder="Header名称" required>
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control header-value" placeholder="Header值" required>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-danger remove-header-btn">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    headerList.append(headerItem);
}

// 分页功能
function renderPagination(totalItems, currentPage, pageSize, containerId, callback) {
    const totalPages = Math.ceil(totalItems / pageSize);
    const container = $('#' + containerId);
    container.empty();
    if (totalPages <= 1) {
        return;
    }
    let html = '<ul class="pagination">';
    // 上一页按钮
    if (currentPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a></li>`;
    } else {
        html += '<li class="page-item disabled"><a class="page-link" href="#">上一页</a></li>';
    }
    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);
    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            html += `<li class="page-item active"><a class="page-link" href="#">${i}</a></li>`;
        } else {
            html += `<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
        }
    }
    // 下一页按钮
    if (currentPage < totalPages) {
        html += `<li class="page-item"><a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a></li>`;
    } else {
        html += '<li class="page-item disabled"><a class="page-link" href="#">下一页</a></li>';
    }
    html += '</ul>';
    container.html(html);
    // 绑定页码点击事件
    container.find('.page-link').on('click', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page && typeof callback === 'function') {
            callback(page);
        }
    });
}

// 验证英文字符（不允许中文）
function validateEnglishOnly(value) {
    const pattern = /^[^\u4e00-\u9fa5]+$/;
    return pattern.test(value);
}

// 验证输入字段
function validateInput(input, fieldName) {
    const value = input.val().trim();
    if (containsChinese(value)) {
        showAlert(`${fieldName}不能包含中文字符，请使用英文字母、数字和下划线`, 'warning');
        input.addClass('is-invalid');
        return false;
    }
    input.removeClass('is-invalid');
    return true;
}

// 验证是否包含中文字符
function containsChinese(str) {
    return /[\u4e00-\u9fa5]/.test(str);
}

// 文档加载完成后执行
$(document).ready(function() {
    // 初始化工具提示
    $('[data-toggle="tooltip"]').tooltip();

    // 初始化富文本编辑器
    initSummernote();
});