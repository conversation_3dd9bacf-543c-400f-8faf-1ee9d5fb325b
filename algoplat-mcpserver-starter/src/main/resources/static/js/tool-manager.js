/**
 * 工具管理JavaScript文件
 */

// 全局变量
let currentMcpServerId;
let currentPage = 1;
let filteredTools = [];
let totalTools = 0;
const pageSize = 9; // 每页显示的工具数量

/**
 * 检查字符串是否包含中文字符
 * @param {string} str
 * @returns {boolean}
 */
function containsChinese(str) {
    return /[\u4e00-\u9fa5]/.test(str);
}

/**
 * 根据McpServerId加载工具列表
 * @param {number} mcpServerId McpServer ID
 * @param {number} page 页码，默认为1
 */
function loadToolsByMcpServerId(mcpServerId, page = 1) {
    currentMcpServerId = mcpServerId;
    currentPage = page;

    // 将 mcpServerId 存储在页面元素中，以便其他 JS 文件可以获取到
    $('#toolListTitle').attr('data-mcpserver-id', mcpServerId);
    $.ajax({
        url: '/mcp/api/tools/toolList',
        type: 'GET',
        data: {mcpServerId: mcpServerId},
        success: function(response) {
            console.log("工具列表API响应:", response);
            if (response.code === 0) {
                let tools = [];
                try {
                    console.log("原始工具数据:", response.data);
                    tools = JSON.parse(response.data);
                    console.log("解析后的工具数据:", tools);
                    if (!tools) {
                        tools = [];
                    }
                } catch (e) {
                    console.error("解析工具数据失败", e);
                    tools = [];
                }
                filteredTools = tools;
                totalTools = tools.length;
                console.log("工具总数:", totalTools);

                // 保存原始工具列表
                $('#toolCards').data('original-tools', JSON.stringify(tools));

                // 渲染工具列表
                renderTools(tools, page);
                // 加载工具所有者列表用于筛选
                loadToolOwners(tools);
                // 触发工具列表加载完成事件，用于修复脚本重新绑定事件
                $(document).trigger('toolsLoaded');
            } else {
                showAlert('加载工具列表失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('加载工具列表失败: ' + error, 'danger');
            // 添加示例数据（仅用于演示）
            const tools = [
                {
                    id: 1,
                    name: "SearchTool",
                    type: "HTTP",
                    owner: "张三",
                    description: "搜索工具，提供搜索相关功能"
                },
                {
                    id: 2,
                    name: "IndexTool",
                    type: "THRIFT",
                    owner: "李四",
                    description: "索引工具，提供索引相关功能"
                },
                {
                    id: 3,
                    name: "RecommendTool",
                    type: "HTTP",
                    owner: "王五",
                    description: "推荐工具，提供推荐相关功能"
                }
            ];

            filteredTools = tools;
            totalTools = tools.length;

            // 保存原始工具列表
            $('#toolCards').data('original-tools', JSON.stringify(tools));

            // 渲染工具列表
            renderTools(tools, page);

            // 加载工具所有者列表用于筛选
            loadToolOwners(tools);
            // 触发工具列表加载完成事件，用于修复脚本重新绑定事件
            $(document).trigger('toolsLoaded');
        }
    });
}

// 加载工具所有者列表
function loadToolOwners(tools) {
    const ownerSelect = $('#ownerFilter');
    ownerSelect.empty();
    ownerSelect.append('<option value="">所有负责人</option>');
    ownerSelect.append('<option value="未设置">未设置</option>');

    const owners = new Set();
    tools.forEach(function(tool) {
        if (tool.owner) {
            owners.add(tool.owner);
        }
    });

    owners.forEach(function(owner) {
        ownerSelect.append(`<option value="${owner}">${owner}</option>`);
    });
}

// 渲染工具列表
function renderTools(tools, page = 1) {
    console.log("开始渲染工具列表，工具数量:", tools.length);
    console.log("工具列表数据:", tools);
    const container = $('#toolCards');
    console.log("工具卡片容器:", container);
    container.empty();

    if (!tools || tools.length === 0) {
        console.log("没有工具可显示");
        container.append('<div class="col-12"><div class="alert alert-info">暂无工具，请添加新的工具</div></div>');
        return;
    }
    // 计算分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, tools.length);
    const pagedTools = tools.slice(startIndex, endIndex);
    console.log("当前页工具:", pagedTools);
    pagedTools.forEach(function(tool) {
        // 确保工具对象有必要的字段
        if (!tool || !tool.name) {
            console.log("跳过无效工具:", tool);
            return;
        }

        const toolTypeClass = getToolTypeClass(tool.type);

        // 截断描述（去除HTML标签后再截断）
        function stripHtml(html) {
            return html ? html.replace(/<[^>]+>/g, '') : '';
        }
        const plainDescription = stripHtml(tool.description);
        const shortDescription = plainDescription.length > 30 ? plainDescription.substring(0, 30) + '...' : (plainDescription || '无描述');

        const card = `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card tool-card h-100">
                    <div class="card-header">
                        <h5 class="card-title">
                            <span class="tool-type ${toolTypeClass}">${tool.type || '未知类型'}</span>
                            ${tool.name}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="tool-description mb-3">
                            <strong>描述:</strong>
                            <p>${shortDescription}</p>
                            <div class="full-description">${tool.description || '无描述'}</div>
                        </div>
                        <div class="mb-3">
                            <strong>负责人:</strong>
                            <p>${tool.owner || '未设置'}</p>
                        </div>
                        <div class="mb-3">
                            <strong>长连接任务:</strong>
                            <p>${tool.isLongTermTask === 1 ? '是' : '否'}</p>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100">
                            <button type="button" class="btn btn-sm btn-outline-primary view-tool-btn" data-mcpserver-id="${currentMcpServerId}" data-name="${tool.name}">
                                <i class="fa fa-eye"></i> 查看
                            </button>
                            ${tool.type === 'ToolAnnotation' ? 
                            `<button type="button" class="btn btn-sm btn-outline-secondary edit-tool-btn" data-mcpserver-id="${currentMcpServerId}" data-name="${tool.name}" disabled>
                                <i class="fa fa-edit"></i> 编辑
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-tool-btn" data-mcpserver-id="${currentMcpServerId}" data-name="${tool.name}" data-type="${tool.type || ''}" data-owner="${tool.owner || ''}" disabled>
                                <i class="fa fa-trash"></i> 删除
                            </button>` :
                            tool.type === 'SystemDefault' ?
                            `<button type="button" class="btn btn-sm btn-outline-secondary edit-tool-btn" data-mcpserver-id="${currentMcpServerId}" data-name="${tool.name}" disabled>
                                <i class="fa fa-edit"></i> 编辑
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-tool-btn" data-mcpserver-id="${currentMcpServerId}" data-name="${tool.name}" data-type="${tool.type || ''}" data-owner="${tool.owner || ''}">
                                <i class="fa fa-trash"></i> 删除
                            </button>` :
                            `<button type="button" class="btn btn-sm btn-outline-warning edit-tool-btn" data-mcpserver-id="${currentMcpServerId}" data-name="${tool.name}" style="color: #fd7e14; border-color: #fd7e14;">
                                <i class="fa fa-edit"></i> 编辑
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-tool-btn" data-mcpserver-id="${currentMcpServerId}" data-name="${tool.name}" data-type="${tool.type || ''}" data-owner="${tool.owner || ''}">
                                <i class="fa fa-trash"></i> 删除
                            </button>`}
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.append(card);
    });

    // 绑定工具按钮事件
    bindToolButtonEvents();

    // 渲染分页控件
    renderPagination(tools.length, page);
}

// 绑定工具按钮事件
function bindToolButtonEvents() {
    // 查看工具详情
    $('.view-tool-btn').on('click', function() {
        const mcpServerId = $(this).data('mcpserver-id');
        const toolName = $(this).data('name');
        viewToolDetail(mcpServerId, toolName);
    });

    // 编辑工具
    $('.edit-tool-btn').on('click', function() {
        // 检查按钮是否被禁用
        if ($(this).prop('disabled')) {
            return;
        }

        const mcpServerId = $(this).data('mcpserver-id');
        const toolName = $(this).data('name');
        loadToolForEdit(mcpServerId, toolName);
    });

    // 删除工具
    $('.delete-tool-btn').on('click', function() {
        // 检查按钮是否被禁用
        if ($(this).prop('disabled')) {
            return;
        }

        const mcpServerId = $(this).data('mcpserver-id');
        const toolName = $(this).data('name');
        const toolType = $(this).data('type');
        const owner = $(this).data('owner');

        // 再次检查工具类型，确保只有ToolAnnotation类型工具不能被删除
        if (toolType === 'ToolAnnotation') {
            showAlert('ToolAnnotation类型工具不允许删除', 'warning');
            return;
        }
        $('#confirmDeleteToolModal .tool-name').text(toolName);
        $('#confirmDeleteToolModal .confirm-delete-tool-btn').data('mcpserver-id', mcpServerId);
        $('#confirmDeleteToolModal .confirm-delete-tool-btn').data('name', toolName);
        $('#confirmDeleteToolModal .confirm-delete-tool-btn').data('type', toolType);
        $('#confirmDeleteToolModal .confirm-delete-tool-btn').data('owner', owner);
        $('#confirmDeleteToolModal').modal('show');
    });
}

// 渲染分页控件
function renderPagination(totalItems, currentPage) {
    const totalPages = Math.ceil(totalItems / pageSize);
    const container = $('#toolPagination');
    container.empty();

    if (totalPages <= 1) {
        return;
    }

    const pagination = $('<ul class="pagination"></ul>');
    container.append(pagination);

    // 上一页按钮
    const prevBtn = $(`
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `);
    pagination.append(prevBtn);

    // 页码按钮
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = $(`
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `);
        pagination.append(pageBtn);
    }

    // 下一页按钮
    const nextBtn = $(`
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `);
    pagination.append(nextBtn);

    // 绑定页码点击事件
    pagination.find('.page-link').on('click', function(e) {
        e.preventDefault();

        if ($(this).parent().hasClass('disabled')) {
            return;
        }

        let page = $(this).data('page');

        if (!page) {
            if ($(this).attr('aria-label') === 'Previous') {
                page = currentPage - 1;
            } else if ($(this).attr('aria-label') === 'Next') {
                page = currentPage + 1;
            }
        }

        if (page && page !== currentPage) {
            renderTools(filteredTools, page);
        }
    });
}

// 查看工具详情
function viewToolDetail(mcpServerId, toolName) {
    $.ajax({
        url: '/mcp/api/tools/getToolByName',
        type: 'GET',
        data: {
            mcpServerId: mcpServerId,
            toolName: toolName
        },
        success: function(response) {
            if (response.code === 0) {
                const tool = JSON.parse(response.data);

                // 构建工具详情HTML
                let detailHtml = `
                    <div class="tool-details">
                        <table class="table table-bordered">
                            <tr>
                                <th width="20%">工具名称</th>
                                <td>${tool.name}</td>
                            </tr>
                            <tr>
                                <th>工具类型</th>
                                <td><span class="tool-type ${getToolTypeClass(tool.type)}">${tool.type}</span></td>
                            </tr>
                            <tr>
                                <th>负责人</th>
                                <td>${tool.owner || '未设置'}</td>
                            </tr>
                            <tr>
                                <th>描述</th>
                                <td>${tool.description ? 
                                    (function() {
                                        // 去除HTML标签
                                        const plainText = tool.description.replace(/<[^>]*>/g, '');
                                        // 截断文本
                                        return plainText.length > 30 ? plainText.substring(0, 30) + '...' : plainText;
                                    })() : 
                                    '无描述'}</td>
                            </tr>
                            <tr>
                                <th>超时时间</th>
                                <td>${tool.timeOut || '6000'} ms</td>
                            </tr>
                        </table>
                `;

                // 根据工具类型添加特定信息
                if (tool.type === 'HTTP') {
                    detailHtml += `
                        <h5 class="mt-4">HTTP配置</h5>
                        <table class="table table-bordered">
                            <tr>
                                <th width="20%">URL</th>
                                <td>${tool.url || ''}</td>
                            </tr>
                            <tr>
                                <th>请求方法</th>
                                <td>${tool.methodName || 'GET'}</td>
                            </tr>
                        </table>
                    `;

                    // 添加请求头
                    if (tool.headers && tool.headers.length > 0) {
                        detailHtml += `
                            <h6>请求头</h6>
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>值</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        tool.headers.forEach(function(header) {
                            detailHtml += `
                                <tr>
                                    <td>${header.name}</td>
                                    <td>${header.value}</td>
                                </tr>
                            `;
                        });

                        detailHtml += `
                                </tbody>
                            </table>
                        `;
                    }
                } else if (tool.type === 'THRIFT' || tool.type === 'PIGEON') {
                    detailHtml += `
                        <h5 class="mt-4">${tool.type}配置</h5>
                        <table class="table table-bordered">
                            <tr>
                                <th width="20%">服务AppKey</th>
                                <td>${tool.appKey || ''}</td>
                            </tr>
                            <tr>
                                <th>接口全路径</th>
                                <td>${tool.interfaceName || ''}</td>
                            </tr>
                            <tr>
                                <th>方法名称</th>
                                <td>${tool.methodName || ''}</td>
                            </tr>
                            <tr>
                                <th>泳道</th>
                                <td>${tool.cell || ''}</td>
                            </tr>
                    `;

                    if (tool.type === 'THRIFT') {
                        detailHtml += `
                            <tr>
                                <th>IP地址</th>
                                <td>${tool.ip || ''}</td>
                            </tr>
                            <tr>
                                <th>端口号</th>
                                <td>${tool.port || ''}</td>
                            </tr>
                            <tr>
                                <th>长连接任务</th>
                                <td>${tool.isLongTermTask === 1 ? '是' : '否'}</td>
                            </tr>
                        `;
                    }

                    detailHtml += `
                        </table>
                    `;
                }

                // 添加Avitor脚本内容显示（如果有）
                if ((tool.type === 'THRIFT' || tool.type === 'PIGEON') && tool.avitorScriptCode) {
                    detailHtml += `
                        <h5 class="mt-4">Avitor脚本处理器</h5>
                        <div class="bg-dark p-3 border rounded">
                            <pre class="mb-0"><code class="text-light">${tool.avitorScriptCode.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
                        </div>
                    `;
                }

                // 添加参数列表
                if (tool.toolParams && tool.toolParams.length > 0) {
                    detailHtml += `
                        <h5 class="mt-4">参数列表</h5>
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>参数名称</th>
                                    <th>参数类型</th>
                                    <th>参数描述</th>
                                    <th>是否必填</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    tool.toolParams.forEach(function(param) {
                        let paramTypeDisplay = param.type || 'String';
                        if (param.type === 'CustomObject' && param.customType) {
                            paramTypeDisplay = `自定义对象类型<br><small class="text-muted">${param.customType}</small>`;
                        }

                        detailHtml += `
                            <tr>
                                <td>${param.name}</td>
                                <td>${paramTypeDisplay}</td>
                                <td>${param.description || ''}</td>
                                <td>${param.required ? '是' : '否'}</td>
                            </tr>
                        `;
                    });

                    detailHtml += `
                            </tbody>
                        </table>
                    `;
                }

                detailHtml += '</div>';

                // 显示工具详情
                $('#viewToolModal .modal-body').html(detailHtml);
                $('#viewToolModal').modal('show');
            } else {
                showAlert('获取工具详情失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('获取工具详情失败: ' + error, 'danger');
        }
    });
}

// 编辑工具
function editTool(mcpServerId, toolName) {
    console.log("编辑工具函数调用 - mcpServerId:", mcpServerId, "toolName:", toolName);
    $.ajax({
        url: '/mcp/api/tools/getToolByName',
        type: 'GET',
        data: {
            mcpServerId: mcpServerId,
            toolName: toolName
        },
        success: function(response) {
            console.log("获取工具详情响应:", response);
            if (response.code === 0) {
                let tool;
                try {
                    if (typeof response.data === 'string') {
                        tool = JSON.parse(response.data);
                    } else {
                        tool = response.data;
                    }
                    console.log("解析后的工具数据:", tool);
                } catch (e) {
                    console.error("解析工具数据失败", e);
                    showAlert('解析工具数据失败', 'danger');
                    return;
                }

                // 填充通用字段
                $('#updateToolId').val(tool.id);
                $('#updateToolMcpServerId').val(mcpServerId);
                $('#updateToolType').val(tool.type);
                $('#updateToolName').val(tool.name);

                // 处理文本类型和描述
                const textType = tool.textType || 2; // 默认为普通文本

                // 调试信息
                console.log("=== editTool 加载工具编辑数据 ===");
                console.log("工具名称:", tool.name);
                console.log("原始textType:", tool.textType);
                console.log("使用的textType:", textType);
                console.log("工具描述:", tool.description);
                console.log("================================");

                // 清理之前的错误状态
                $('#updateToolDescription').closest('.form-group').removeClass('has-error');
                $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
                $('#updateToolDescriptionPlain').removeClass('is-invalid');

                // 设置 textType radio 按钮
                $(`input[name="updateTextType"][value="${textType}"]`).prop('checked', true);

                // 更新按钮状态
                $('label[for="updatePlainTextType"]').removeClass('active');
                $('label[for="updateRichTextType"]').removeClass('active');
                if (textType === 1) {
                    $('label[for="updateRichTextType"]').addClass('active');
                } else {
                    $('label[for="updatePlainTextType"]').addClass('active');
                }

                // 根据 textType 显示对应的编辑器并填充内容
                if (textType === 1) {
                    // 富文本
                    console.log("editTool - 显示富文本编辑器，内容:", tool.description);
                    $('#updateRichTextEditor').show();
                    $('#updatePlainTextEditor').hide();
                    $('#updateToolDescription').summernote('code', tool.description || '');
                    $('#updateToolDescriptionPlain').val(''); // 清空普通文本框
                } else {
                    // 普通文本
                    console.log("editTool - 显示普通文本编辑器，内容:", tool.description);
                    $('#updateRichTextEditor').hide();
                    $('#updatePlainTextEditor').show();
                    $('#updateToolDescription').summernote('code', ''); // 清空富文本框
                    $('#updateToolDescriptionPlain').val(tool.description || '');
                }

                // 确保编辑器状态正确显示
                setTimeout(function() {
                    if (textType === 1) {
                        $('#updateRichTextEditor').show();
                        $('#updatePlainTextEditor').hide();
                    } else {
                        $('#updateRichTextEditor').hide();
                        $('#updatePlainTextEditor').show();
                    }
                }, 100);

                $('#updateToolOwner').val(tool.owner);
                $('#updateToolTimeout').val(tool.timeOut || 6000);

                // 隐藏所有工具表单
                $('.updatetool-form').hide();

                // 根据工具类型显示对应表单
                if (tool.type === 'HTTP') {
                    $('#updateHTTPForm').show();
                    $('#updateHttpUrl').val(tool.url || '');
                    $('#updateHttpMethod').val(tool.method || 'GET');
                    // 清空请求头列表
                    $('#updateHttpHeaderList').empty();
                    // 添加请求头
                    if (tool.headers && tool.headers.length > 0) {
                        tool.headers.forEach(function(header) {
                            addHeaderItem($('#updateHttpHeaderList'), header.name, header.value);
                        });
                    }

                    // 清空参数列表
                    $('#updateHTTPParamList').empty();

                    // 添加参数
                    if (tool.toolParams && tool.toolParams.length > 0) {
                        tool.toolParams.forEach(function(param) {
                            addParamItem($('#updateHTTPParamList'), 'HTTP', param);
                        });
                    }
                } else if (tool.type === 'THRIFT') {
                    $('#updateTHRIFTForm').show();
                    $('#updateThriftAppKey').val(tool.appKey || '');
                    $('#updateThriftInterfaceName').val(tool.interfaceName || '');
                    $('#updateThriftMethodName').val(tool.methodName || '');
                    $('#updateThriftCell').val(tool.cell || '');
                    $('#updateThriftIp').val(tool.ip || '');
                    $('#updateThriftPort').val(tool.port || '');

                    // 设置是否是长连接任务
                    const isLongTermTask = tool.isLongTermTask === 1;
                    $('#updateIsLongTermTask').prop('checked', isLongTermTask);
                    $('#updateIsLongTermTask').next('label').text(isLongTermTask ? '是' : '否');

                    // 设置Avitor脚本内容
                    setTimeout(() => {
                        setEditorContent('updateThriftAvitorScript', tool.avitorScriptCode || '');
                    }, 100);

                    // 清空参数列表
                    $('#updateTHRIFTParamList').empty();

                    // 添加参数
                    if (tool.toolParams && tool.toolParams.length > 0) {
                        tool.toolParams.forEach(function(param) {
                            addParamItem($('#updateTHRIFTParamList'), 'THRIFT', param);
                        });
                    }
                } else if (tool.type === 'PIGEON') {
                    $('#updatePIGEONForm').show();
                    $('#updatePigeonAppKey').val(tool.appKey || '');
                    $('#updatePigeonInterfaceName').val(tool.interfaceName || '');
                    $('#updatePigeonMethodName').val(tool.methodName || '');
                    $('#updatePigeonCell').val(tool.cell || '');

                    // 设置Avitor脚本内容
                    setTimeout(() => {
                        setEditorContent('updatePigeonAvitorScript', tool.avitorScriptCode || '');
                    }, 100);

                    // 清空参数列表
                    $('#updatePIGEONParamList').empty();

                    // 添加参数
                    if (tool.toolParams && tool.toolParams.length > 0) {
                        tool.toolParams.forEach(function(param) {
                            addParamItem($('#updatePIGEONParamList'), 'PIGEON', param);
                        });
                    }
                }

                // 显示更新工具模态框
                $('#updateToolModal').modal('show');

                // 检查AI生成按钮状态
                if (tool.type === 'THRIFT') {
                    checkAIButtonStatus('THRIFT', 'update');
                } else if (tool.type === 'PIGEON') {
                    checkAIButtonStatus('PIGEON', 'update');
                }
            } else {
                showAlert('获取工具详情失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('获取工具详情失败: ' + error, 'danger');
        }
    });
}

// 删除工具
function deleteTool(mcpServerId, toolName, toolType, owner) {
    // 只有ToolAnnotation类型工具不能被删除
    if (toolType === 'ToolAnnotation') {
        showAlert('ToolAnnotation类型工具不允许删除', 'warning');
        return;
    }

    $.ajax({
        url: '/mcp/api/tools/delete',
        type: 'GET',
        data: {
            mcpServerId: mcpServerId,
            toolName: toolName,
            toolType: toolType,
            owner: owner
        },
        success: function(response) {
            if (response.code === 0) {
                showAlert('工具删除成功', 'success');
                $('#confirmDeleteToolModal').modal('hide');
                loadToolsByMcpServerId(mcpServerId);
            } else {
                showAlert('工具删除失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('工具删除失败: ' + error, 'danger');
        }
    });
}


function addHeaderItem(headerList, name = '', value = '') {
    const id = Date.now();
    const headerItem = `
        <div class="header-item">
            <div class="form-row">
                <div class="col-md-5">
                    <div class="form-group">
                        <label>名称</label>
                        <input type="text" class="form-control header-name" value="${name}" required>
                    </div>
                </div>
                <div class="col-md-5">
                    <div class="form-group">
                        <label>值</label>
                        <input type="text" class="form-control header-value" value="${value}" required>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button" class="btn btn-danger btn-block remove-header-btn">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    headerList.append(headerItem);

    // 绑定删除按钮事件
    headerList.find('.remove-header-btn').last().on('click', function() {
        $(this).closest('.header-item').remove();
    });
}

// 添加参数项


function addParamItem(paramList, toolType, param = null) {
    const id = Date.now();
    let paramItem = `
        <div class="param-item">
            <div class="form-row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>参数名称</label>
                        <input type="text" class="form-control param-name" value="${param ? param.name : ''}" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>参数描述</label>
                        <input type="text" class="form-control param-description" value="${param ? param.description : ''}" required>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>参数类型</label>
                        <select class="form-control param-type" required>
                            <option value="String" ${param && param.type === 'String' ? 'selected' : ''}>String</option>
                            <option value="Integer" ${param && param.type === 'Integer' ? 'selected' : ''}>Integer</option>
                            <option value="Long" ${param && param.type === 'Long' ? 'selected' : ''}>Long</option>
                            <option value="Double" ${param && param.type === 'Double' ? 'selected' : ''}>Double</option>
                            <option value="Boolean" ${param && param.type === 'Boolean' ? 'selected' : ''}>Boolean</option>
                            <option value="List" ${param && param.type === 'List' ? 'selected' : ''}>List</option>
                            <option value="Set" ${param && param.type === 'Set' ? 'selected' : ''}>Set</option>
                            <option value="Map" ${param && param.type === 'Map' ? 'selected' : ''}>Map</option>
                            <option value="CustomObject" ${param && param.type === 'CustomObject' ? 'selected' : ''}>自定义对象类型</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label>必填</label>
                        <div class="custom-control custom-switch mt-2">
                            <input type="checkbox" class="custom-control-input param-required" id="paramRequired${id}" ${param ? (param.required ? 'checked' : '') : 'checked'}>
                            <label class="custom-control-label" for="paramRequired${id}"></label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-row custom-object-path" style="display: ${param && param.type === 'CustomObject' ? 'block' : 'none'}">
                <div class="col-md-12">
                    <div class="form-group">
                        <label>对象类型全路径</label>
                        <input type="text" class="form-control param-custom-type" placeholder="请输入自定义对象类型的全路径，如：com.example.dto.UserDTO" value="${param && param.customType ? param.customType : ''}">
                    </div>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-danger remove-param-btn">
                <i class="fa fa-trash"></i> 删除参数
            </button>
        </div>
    `;

    paramList.append(paramItem);

    // 绑定删除按钮事件
    paramList.find('.remove-param-btn').last().on('click', function() {
        $(this).closest('.param-item').remove();
    });

    // 绑定自定义对象类型显示/隐藏逻辑
    paramList.find('.param-type').last().on('change', function() {
        const customObjectPathDiv = $(this).closest('.param-item').find('.custom-object-path');
        if ($(this).val() === 'CustomObject') {
            customObjectPathDiv.show();
        } else {
            customObjectPathDiv.hide();
        }
    });

}

// 获取工具类型对应的CSS类
function getToolTypeClass(type) {
    switch (type) {
        case 'HTTP':
            return 'tool-type-http';
        case 'THRIFT':
            return 'tool-type-thrift';
        case 'PIGEON':
            return 'tool-type-pigeon';
        case 'SystemDefault':
            return 'tool-type-systemdefault';
        case 'ToolAnnotation':
            return 'tool-type-toolannotation';
        default:
            return 'tool-type-system';
    }
}

// 按类型筛选工具
function filterToolsByType(type) {
    if (type === 'all') {
        // 重置为原始列表
        try {
            filteredTools = JSON.parse($('#toolCards').data('original-tools'));
        } catch (e) {
            console.error("解析原始工具数据失败", e);
        }
    } else {
        // 从原始列表中筛选
        try {
            const originalTools = JSON.parse($('#toolCards').data('original-tools'));
            filteredTools = originalTools.filter(tool => tool.type === type);
        } catch (e) {
            console.error("筛选工具数据失败", e);
        }
    }

    renderTools(filteredTools, 1);
}

// 按负责人筛选工具
function filterToolsByOwner(owner) {
    if (!owner) {
        // 重置为原始列表
        try {
            filteredTools = JSON.parse($('#toolCards').data('original-tools'));
        } catch (e) {
            console.error("解析原始工具数据失败", e);
        }
    } else {
        // 从原始列表中筛选
        try {
            const originalTools = JSON.parse($('#toolCards').data('original-tools'));
            filteredTools = originalTools.filter(tool => tool.owner === owner);
        } catch (e) {
            console.error("筛选工具数据失败", e);
        }
    }

    renderTools(filteredTools, 1);
}

// 按名称搜索工具
function searchToolsByName(searchTerm) {
    if (!searchTerm) {
        // 重置为原始列表
        try {
            filteredTools = JSON.parse($('#toolCards').data('original-tools'));
            renderTools(filteredTools, 1);
        } catch (e) {
            console.error("解析原始工具数据失败", e);
        }
        return;
    }

    $.ajax({
        url: '/mcp/api/tools/getToolByName',
        type: 'GET',
        data: {
            mcpServerId: currentMcpServerId,
            toolName: searchTerm
        },
        success: function(response) {
            if (response.code === 0) {
                const tool = JSON.parse(response.data);
                filteredTools = [tool];
                renderTools(filteredTools, 1);
            } else {
                // 如果找不到精确匹配，尝试模糊搜索
                try {
                    const originalTools = JSON.parse($('#toolCards').data('original-tools'));
                    filteredTools = originalTools.filter(tool =>
                        tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        (tool.description && tool.description.toLowerCase().includes(searchTerm.toLowerCase()))
                    );

                    if (filteredTools.length > 0) {
                        renderTools(filteredTools, 1);
                    } else {
                        showAlert('未找到匹配的工具', 'warning');
                    }
                } catch (e) {
                    console.error("搜索工具数据失败", e);
                    showAlert('搜索工具失败', 'danger');
                }
            }
        },
        error: function(xhr, status, error) {
            showAlert('搜索工具失败: ' + error, 'danger');
        }
    });
}

// 初始化工具提示
function initTooltips() {
    $('[title]').tooltip({
        container: 'body',
        placement: 'top'
    });
}

// 文档加载完成后执行
$(document).ready(function() {
    // 初始化Summernote编辑器
    $('.summernote').summernote({
        height: 150,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'underline', 'clear']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ]
    });

    // 创建工具模态框的文本类型切换
    $('input[name="textType"]').on('change', function() {
        const textType = $(this).val();
        const $richEditor = $('#richTextEditor');
        const $plainEditor = $('#plainTextEditor');

        // 清理错误状态
        $('#toolDescription').closest('.form-group').removeClass('has-error');
        $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
        $('#toolDescriptionPlain').removeClass('is-invalid');

        console.log("=== 创建工具文本类型切换 ===");
        console.log("切换到文本类型:", textType);
        console.log("切换前普通文本内容:", $('#toolDescriptionPlain').val());
        console.log("切换前富文本内容:", $('#toolDescription').summernote('code'));

        // 添加切换提示
        if (textType === '1') {
            // 切换到富文本
            $plainEditor.fadeOut(200, function() {
                $richEditor.fadeIn(200, function() {
                    // 聚焦到富文本编辑器
                    $('#toolDescription').summernote('focus');
                });
            });
        } else {
            // 切换到普通文本
            $richEditor.fadeOut(200, function() {
                $plainEditor.fadeIn(200, function() {
                    // 聚焦到普通文本框
                    $('#toolDescriptionPlain').focus();
                });
            });
        }

        console.log("切换后普通文本内容:", $('#toolDescriptionPlain').val());
        console.log("切换后富文本内容:", $('#toolDescription').summernote('code'));
        console.log("===============================");
    });

    // 更新工具模态框的文本类型切换
    $('input[name="updateTextType"]').on('change', function() {
        const textType = $(this).val();
        const $richEditor = $('#updateRichTextEditor');
        const $plainEditor = $('#updatePlainTextEditor');

        // 清理错误状态
        $('#updateToolDescription').closest('.form-group').removeClass('has-error');
        $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
        $('#updateToolDescriptionPlain').removeClass('is-invalid');

        console.log("=== 更新工具文本类型切换 ===");
        console.log("切换到文本类型:", textType);
        console.log("切换前普通文本内容:", $('#updateToolDescriptionPlain').val());
        console.log("切换前富文本内容:", $('#updateToolDescription').summernote('code'));

        if (textType === '1') {
            // 切换到富文本
            $plainEditor.fadeOut(200, function() {
                $richEditor.fadeIn(200, function() {
                    // 聚焦到富文本编辑器
                    $('#updateToolDescription').summernote('focus');
                });
            });
        } else {
            // 切换到普通文本
            $richEditor.fadeOut(200, function() {
                $plainEditor.fadeIn(200, function() {
                    // 聚焦到普通文本框
                    $('#updateToolDescriptionPlain').focus();
                });
            });
        }

        console.log("切换后普通文本内容:", $('#updateToolDescriptionPlain').val());
        console.log("切换后富文本内容:", $('#updateToolDescription').summernote('code'));
        console.log("===============================");
    });

    // 初始化文本编辑器状态
    function initTextEditorState() {
        // 创建工具模态框 - 默认显示普通文本
        $('#plainTextEditor').show();
        $('#richTextEditor').hide();
        $('label[for="plainTextType"]').addClass('active');
        $('label[for="richTextType"]').removeClass('active');

        // 更新工具模态框 - 默认显示普通文本编辑器
        $('#updatePlainTextEditor').show();
        $('#updateRichTextEditor').hide();
        $('label[for="updatePlainTextType"]').addClass('active');
        $('label[for="updateRichTextType"]').removeClass('active');
    }

    // 页面加载完成后初始化状态
    initTextEditorState();

    // 工具名输入框的实时验证
    $('#toolName').on('input', function() {
        const toolType = $('#toolType').val();
        const toolName = $(this).val().trim();

        // 对HTTP、THRIFT和PIGEON类型的工具名进行中文字符检查
        if ((toolType === 'HTTP' || toolType === 'THRIFT' || toolType === 'PIGEON') && containsChinese(toolName)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after(`<div class="invalid-feedback">${toolType}类型的工具名不能包含中文字符，请使用英文字母、数字和下划线</div>`);
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // 更新工具名输入框的实时验证
    $('#updateToolName').on('input', function() {
        const toolType = $('#updateToolType').val();
        const toolName = $(this).val().trim();

        // 对HTTP、THRIFT和PIGEON类型的工具名进行中文字符检查
        if ((toolType === 'HTTP' || toolType === 'THRIFT' || toolType === 'PIGEON') && containsChinese(toolName)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after(`<div class="invalid-feedback">${toolType}类型的工具名不能包含中文字符，请使用英文字母、数字和下划线</div>`);
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // 工具类型切换事件，需要重新验证工具名
    $('#toolType').on('change', function() {
        $('#toolName').trigger('input');
    });

    // 更新工具类型切换事件，需要重新验证工具名
    $('#updateToolType').on('change', function() {
        $('#updateToolName').trigger('input');
    });

    // 初始化工具提示
    $('[data-toggle="tooltip"]').tooltip();

    // 长连接任务开关事件处理
    $('#isLongTermTask').on('change', function() {
        const isChecked = $(this).prop('checked');
        $(this).next('label').text(isChecked ? '是' : '否');
    });

    $('#updateIsLongTermTask').on('change', function() {
        const isChecked = $(this).prop('checked');
        $(this).next('label').text(isChecked ? '是' : '否');
    });

    // 工具类型切换事件，需要在切换到THRIFT类型时初始化长连接任务开关
    $('#toolType').on('change', function() {
        const toolType = $(this).val();
        $('#toolName').trigger('input');

        // 重置长连接任务开关（仅THRIFT类型显示）
        if (toolType === 'THRIFT') {
            $('#isLongTermTask').prop('checked', false);
            $('#isLongTermTask').next('label').text('否');
        }
    });

    // 更新工具类型切换事件，需要在切换到THRIFT类型时初始化长连接任务开关
    $('#updateToolType').on('change', function() {
        const toolType = $(this).val();
        $('#updateToolName').trigger('input');

        // 重置长连接任务开关（仅THRIFT类型显示）
        if (toolType === 'THRIFT') {
            $('#updateIsLongTermTask').prop('checked', false);
            $('#updateIsLongTermTask').next('label').text('否');
        }
    });

    // 工具类型切换事件
    $('#toolType').on('change', function() {
        const toolType = $(this).val();
        $('.tool-form').hide();
        $(`#${toolType}Form`).show();

        // 重置长连接任务开关（仅THRIFT类型显示）
        if (toolType === 'THRIFT') {
            $('#isLongTermTask').prop('checked', false);
            $('#isLongTermTask').next('label').text('否');
        }
    });

    // 更新工具类型切换事件
    $('#updateToolType').on('change', function() {
        const toolType = $(this).val();
        $('.updatetool-form').hide();
        $(`#update${toolType}Form`).show();

        // 重置长连接任务开关（仅THRIFT类型显示）
        if (toolType === 'THRIFT') {
            $('#updateIsLongTermTask').prop('checked', false);
            $('#updateIsLongTermTask').next('label').text('否');
        }
    });

    // 工具负责人筛选下拉框变化事件（支持“未设置”筛选，防止多次绑定）
    $('#ownerFilter').off('change').on('change', function() {
        const owner = $(this).val();
        if (owner === "未设置") {
            try {
                const originalTools = JSON.parse($('#toolCards').data('original-tools'));
                filteredTools = originalTools.filter(tool => !tool.owner || tool.owner === "" || tool.owner === null);
            } catch (e) {
                console.error("筛选未设置负责人工具数据失败", e);
            }
            renderTools(filteredTools, 1);
        } else {
            filterToolsByOwner(owner);
        }
    });

    // 添加请求头按钮点击事件
    $('.add-header-btn').on('click', function() {
        const headerList = $(this).closest('.form-group').find('.header-list');
        addHeaderItem(headerList);
    });

    // 添加参数按钮点击事件
    $('.add-param-btn').on('click', function() {
        const toolType = $(this).data('tool-type');
        const paramList = $(this).closest('.form-group').find('.param-list, .update-param-list');
        addParamItem(paramList, toolType, null);
    });

    // 创建工具表单提交事件 - 已在tool-fix.js中重新绑定
    /*$('#createToolForm').on('submit', function(e) {
        e.preventDefault();
        createTool();
    });*/

    // 更新工具表单提交事件 - 已在tool-fix.js中重新绑定
    /*$('#updateToolForm').on('submit', function(e) {
        e.preventDefault();
        updateTool();
    });*/

    // 删除工具确认按钮点击事件
    $('.confirm-delete-tool-btn').on('click', function() {
        const mcpServerId = $(this).data('mcpserver-id');
        const toolName = $(this).data('name');
        const toolType = $(this).data('type');
        const owner = $(this).data('owner');
        deleteTool(mcpServerId, toolName, toolType, owner);
    });

    // 工具搜索按钮点击事件
    $('#toolSearchBtn').on('click', function() {
        const searchTerm = $('#toolSearchInput').val().trim();
        searchToolsByName(searchTerm);
    });

    // 工具搜索输入框回车事件
    $('#toolSearchInput').on('keypress', function(e) {
        if (e.which === 13) {
            const searchTerm = $(this).val().trim();
            searchToolsByName(searchTerm);
        }
    });

    // 工具类型筛选按钮点击事件
    $('.tool-type-filter').on('click', function() {
        $('.tool-type-filter').removeClass('active');
        $(this).addClass('active');
        const toolType = $(this).data('type');
        filterToolsByType(toolType);
    });

// 工具负责人筛选下拉框变化事件
$('#ownerFilter').on('change', function() {
    const owner = $(this).val();
    filterToolsByOwner(owner);
});

// AI生成按钮点击事件
$('.ai-generate-btn').on('click', function() {
    const toolType = $(this).data('tool-type');
    const mode = $(this).data('mode');

    // 显示确认对话框
    if (confirm('本操作将对工具描述进行更新，是否继续？')) {
        generateToolInfo(toolType, mode);
    }
});

    // 创建工具按钮点击事件
    $('#createToolBtn').on('click', function() {
        // 重置表单
        $('#createToolForm')[0].reset();
        $('#toolDescription').summernote('code', '');
        $('#toolDescriptionPlain').val('');
        $('#HTTPParamList, #THRIFTParamList, #PIGEONParamList').empty();
        $('#httpHeaderList').empty();

        // 重置文本类型为普通文本（默认）
        $('input[name="textType"][value="2"]').prop('checked', true);
        $('label[for="plainTextType"]').addClass('active');
        $('label[for="richTextType"]').removeClass('active');
        $('#plainTextEditor').show();
        $('#richTextEditor').hide();

        // 清理所有验证错误状态
        $('#toolDescription').closest('.form-group').removeClass('has-error');
        $('#toolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
        $('#toolDescriptionPlain').removeClass('is-invalid');

        // 确保只有普通文本框有内容，富文本框为空
        $('#toolDescriptionPlain').val('');
        $('#toolDescription').summernote('code', '');

        console.log("=== 创建工具模态框重置完成 ===");
        console.log("默认文本类型: 普通文本(2)");
        console.log("显示的编辑器: 普通文本编辑器");
        console.log("普通文本框内容:", $('#toolDescriptionPlain').val());
        console.log("富文本框内容:", $('#toolDescription').summernote('code'));
        console.log("===============================");

        // 设置McpServer ID
        $('#mcpServerId').val(currentMcpServerId);

        // 默认显示HTTP表单
        $('#toolType').val('HTTP');
        $('.tool-form').hide();
        $('#HTTPForm').show();

        // 确保AI生成按钮是禁用状态
        $('.ai-generate-btn').prop('disabled', true);

        // 重置长连接任务开关
        $('#isLongTermTask').prop('checked', false);
        $('#isLongTermTask').next('label').text('否');

        // 显示创建工具模态框
        $('#createToolModal').modal('show');
    });

    // 返回按钮点击事件
    $('#backToMcpServerBtn').on('click', function() {
        $('#mcpServerListArea').show();
        $('#toolListArea').hide();
    });

    // 监听THRIFT和PIGEON表单中的必填字段变化，控制AI生成按钮的启用/禁用状态
    $('#thriftAppKey, #thriftInterfaceName, #thriftMethodName').on('input', function() {
        checkAIButtonStatus('THRIFT', 'create');
    });

    $('#pigeonAppKey, #pigeonInterfaceName, #pigeonMethodName').on('input', function() {
        checkAIButtonStatus('PIGEON', 'create');
    });

    $('#updateThriftAppKey, #updateThriftInterfaceName, #updateThriftMethodName').on('input', function() {
        checkAIButtonStatus('THRIFT', 'update');
    });

    $('#updatePigeonAppKey, #updatePigeonInterfaceName, #updatePigeonMethodName').on('input', function() {
        checkAIButtonStatus('PIGEON', 'update');
    });

    // 删除这里的选择默认工具按钮点击事件，因为已经在 default-tools.js 中定义了
});

// 检查AI生成按钮状态
function checkAIButtonStatus(toolType, mode) {
    let appKey, interfaceName, methodName;

    if (mode === 'create') {
        if (toolType === 'THRIFT') {
            appKey = $('#thriftAppKey').val().trim();
            interfaceName = $('#thriftInterfaceName').val().trim();
            methodName = $('#thriftMethodName').val().trim();
        } else if (toolType === 'PIGEON') {
            appKey = $('#pigeonAppKey').val().trim();
            interfaceName = $('#pigeonInterfaceName').val().trim();
            methodName = $('#pigeonMethodName').val().trim();
        }
    } else if (mode === 'update') {
        if (toolType === 'THRIFT') {
            appKey = $('#updateThriftAppKey').val().trim();
            interfaceName = $('#updateThriftInterfaceName').val().trim();
            methodName = $('#updateThriftMethodName').val().trim();
        } else if (toolType === 'PIGEON') {
            appKey = $('#updatePigeonAppKey').val().trim();
            interfaceName = $('#updatePigeonInterfaceName').val().trim();
            methodName = $('#updatePigeonMethodName').val().trim();
        }
    }

    // 获取对应的按钮
    const $btn = $(`.ai-generate-btn[data-tool-type="${toolType}"][data-mode="${mode}"]`);

    // 如果所有必填字段都已填写，启用按钮，否则禁用
    if (appKey && interfaceName && methodName) {
        $btn.prop('disabled', false);
    } else {
        $btn.prop('disabled', true);
    }
}

// 显示指定类型的工具表单
function showToolFormByType(toolType) {
    $('.tool-form').hide();
    $(`#${toolType}Form`).show();
}

// 加载工具用于编辑
function loadToolForEdit(mcpServerId, toolName) {
    $.ajax({
        url: '/mcp/api/tools/getToolByName',
        type: 'GET',
        data: {
            mcpServerId: mcpServerId,
            toolName: toolName
        },
        success: function(response) {
            if (response.code === 0) {
                const tool = JSON.parse(response.data);

                // 填充通用字段
                $('#updateToolId').val(tool.id);
                $('#updateToolMcpServerId').val(mcpServerId);
                $('#updateToolType').val(tool.type);
                $('#updateToolName').val(tool.name);

                // 处理文本类型和描述
                const textType = tool.textType || 2; // 默认为普通文本

                // 调试信息
                console.log("=== 加载工具编辑数据 ===");
                console.log("工具名称:", tool.name);
                console.log("原始textType:", tool.textType);
                console.log("使用的textType:", textType);
                console.log("工具描述:", tool.description);
                console.log("========================");

                // 清理之前的错误状态
                $('#updateToolDescription').closest('.form-group').removeClass('has-error');
                $('#updateToolDescription').closest('.form-group').find('.note-editor').css('border-color', '');
                $('#updateToolDescriptionPlain').removeClass('is-invalid');

                $(`input[name="updateTextType"][value="${textType}"]`).prop('checked', true);

                // 更新按钮状态
                $('label[for="updatePlainTextType"]').removeClass('active');
                $('label[for="updateRichTextType"]').removeClass('active');
                if (textType === 1) {
                    $('label[for="updateRichTextType"]').addClass('active');
                } else {
                    $('label[for="updatePlainTextType"]').addClass('active');
                }

                if (textType === 1) {
                    // 富文本
                    console.log("显示富文本编辑器，内容:", tool.description);
                    $('#updateRichTextEditor').show();
                    $('#updatePlainTextEditor').hide();
                    $('#updateToolDescription').summernote('code', tool.description || '');
                    $('#updateToolDescriptionPlain').val(''); // 清空普通文本框
                } else {
                    // 普通文本
                    console.log("显示普通文本编辑器，内容:", tool.description);
                    $('#updateRichTextEditor').hide();
                    $('#updatePlainTextEditor').show();
                    $('#updateToolDescription').summernote('code', ''); // 清空富文本框
                    $('#updateToolDescriptionPlain').val(tool.description || '');
                }

                $('#updateToolOwner').val(tool.owner);
                $('#updateToolTimeout').val(tool.timeOut || 6000);

                // 隐藏所有工具表单
                $('.updatetool-form').hide();

                // 根据工具类型显示对应表单
                if (tool.type === 'HTTP') {
                    $('#updateHTTPForm').show();
                    $('#updateHttpUrl').val(tool.url || '');
                    $('#updateHttpMethod').val(tool.method || 'GET');
                    // 清空请求头列表
                    $('#updateHttpHeaderList').empty();
                    // 添加请求头
                    if (tool.headers && tool.headers.length > 0) {
                        tool.headers.forEach(function(header) {
                            addHeaderItem($('#updateHttpHeaderList'), header.name, header.value);
                        });
                    }

                    // 清空参数列表
                    $('#updateHTTPParamList').empty();

                    // 添加参数
                    if (tool.toolParams && tool.toolParams.length > 0) {
                        tool.toolParams.forEach(function(param) {
                            addParamItem($('#updateHTTPParamList'), 'HTTP', param);
                        });
                    }
                } else if (tool.type === 'THRIFT') {
                    $('#updateTHRIFTForm').show();
                    $('#updateThriftAppKey').val(tool.appKey || '');
                    $('#updateThriftInterfaceName').val(tool.interfaceName || '');
                    $('#updateThriftMethodName').val(tool.methodName || '');
                    $('#updateThriftCell').val(tool.cell || '');
                    $('#updateThriftIp').val(tool.ip || '');
                    $('#updateThriftPort').val(tool.port || '');

                    // 设置是否是长连接任务
                    const isLongTermTask = tool.isLongTermTask === 1;
                    $('#updateIsLongTermTask').prop('checked', isLongTermTask);
                    $('#updateIsLongTermTask').next('label').text(isLongTermTask ? '是' : '否');

                    // 设置Avitor脚本内容
                    setTimeout(() => {
                        setEditorContent('updateThriftAvitorScript', tool.avitorScriptCode || '');
                    }, 100);

                    // 清空参数列表
                    $('#updateTHRIFTParamList').empty();

                    // 添加参数
                    if (tool.toolParams && tool.toolParams.length > 0) {
                        tool.toolParams.forEach(function(param) {
                            addParamItem($('#updateTHRIFTParamList'), 'THRIFT', param);
                        });
                    }
                } else if (tool.type === 'PIGEON') {
                    $('#updatePIGEONForm').show();
                    $('#updatePigeonAppKey').val(tool.appKey || '');
                    $('#updatePigeonInterfaceName').val(tool.interfaceName || '');
                    $('#updatePigeonMethodName').val(tool.methodName || '');
                    $('#updatePigeonCell').val(tool.cell || '');

                    // 设置Avitor脚本内容
                    setTimeout(() => {
                        setEditorContent('updatePigeonAvitorScript', tool.avitorScriptCode || '');
                    }, 100);

                    // 清空参数列表
                    $('#updatePIGEONParamList').empty();

                    // 添加参数
                    if (tool.toolParams && tool.toolParams.length > 0) {
                        tool.toolParams.forEach(function(param) {
                            addParamItem($('#updatePIGEONParamList'), 'PIGEON', param);
                        });
                    }
                }

                // 显示更新工具模态框
                $('#updateToolModal').modal('show');

                // 检查AI生成按钮状态
                if (tool.type === 'THRIFT') {
                    checkAIButtonStatus('THRIFT', 'update');
                } else if (tool.type === 'PIGEON') {
                    checkAIButtonStatus('PIGEON', 'update');
                }
            } else {
                showAlert('获取工具详情失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('获取工具详情失败: ' + error, 'danger');
        }
    });
}

/**
 * AI生成工具信息（仅生成描述）
 */
function generateToolInfo(toolType, mode) {
    // 获取接口全路径和方法名称
    let interfaceName, methodName, appKey;
    let toolParams = [];

    if (mode === 'create') {
        if (toolType === 'THRIFT') {
            interfaceName = $('#thriftInterfaceName').val().trim();
            methodName = $('#thriftMethodName').val().trim();
            appKey = $('#thriftAppKey').val().trim();

            console.log("THRIFT参数列表容器:", $('#THRIFTParamList'));
            console.log("THRIFT参数项数量:", $('#THRIFTParamList .param-item').length);
            // 收集参数列表
            $('#THRIFTParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();

                console.log("收集到的参数:", {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType
                });

                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                // 如果是自定义对象类型，添加自定义类型路径
                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    toolParams.push(paramData);
                }
            });
        } else if (toolType === 'PIGEON') {
            interfaceName = $('#pigeonInterfaceName').val().trim();
            methodName = $('#pigeonMethodName').val().trim();
            appKey = $('#pigeonAppKey').val().trim();

            console.log("PIGEON参数列表容器:", $('#PIGEONParamList'));
            console.log("PIGEON参数项数量:", $('#PIGEONParamList .param-item').length);
            // 收集参数列表
            $('#PIGEONParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();

                console.log("收集到的参数:", {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType
                });

                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                // 如果是自定义对象类型，添加自定义类型路径
                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    toolParams.push(paramData);
                }
            });
        }
    } else if (mode === 'update') {
        if (toolType === 'THRIFT') {
            interfaceName = $('#updateThriftInterfaceName').val().trim();
            methodName = $('#updateThriftMethodName').val().trim();
            appKey = $('#updateThriftAppKey').val().trim();

            console.log("更新THRIFT参数列表容器:", $('#updateTHRIFTParamList'));
            console.log("更新THRIFT参数项数量:", $('#updateTHRIFTParamList .param-item').length);
            // 收集参数列表
            $('#updateTHRIFTParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();

                console.log("收集到的更新参数:", {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType
                });

                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                // 如果是自定义对象类型，添加自定义类型路径
                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    toolParams.push(paramData);
                }
            });
        } else if (toolType === 'PIGEON') {
            interfaceName = $('#updatePigeonInterfaceName').val().trim();
            methodName = $('#updatePigeonMethodName').val().trim();
            appKey = $('#updatePigeonAppKey').val().trim();

            console.log("更新PIGEON参数列表容器:", $('#updatePIGEONParamList'));
            console.log("更新PIGEON参数项数量:", $('#updatePIGEONParamList .param-item').length);
            // 收集参数列表
            $('#updatePIGEONParamList .param-item').each(function() {
                const paramName = $(this).find('.param-name').val().trim();
                const paramDescription = $(this).find('.param-description').val().trim();
                const paramRequired = $(this).find('.param-required').prop('checked');
                const paramType = $(this).find('.param-type').val();

                console.log("收集到的更新参数:", {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType
                });

                let paramData = {
                    name: paramName,
                    description: paramDescription,
                    required: paramRequired,
                    type: paramType || 'String'
                };

                // 如果是自定义对象类型，添加自定义类型路径
                if (paramType === 'CustomObject') {
                    paramData.customType = $(this).find('.param-custom-type').val().trim();
                }

                if (paramName && paramDescription) {
                    toolParams.push(paramData);
                }
            });
        }
    }

    console.log("最终收集到的参数列表:", toolParams);
    console.log("请求数据:", {
        toolType: toolType,
        interfaceName: interfaceName,
        methodName: methodName,
        appKey: appKey,
        toolParams: toolParams
    });

    // 验证必填字段
    if (!interfaceName || !methodName) {
        showAlert('请先填写接口全路径和方法名称', 'warning');
        return;
    }

    // 显示加载状态
    const $btn = $(`.ai-generate-btn[data-tool-type="${toolType}"][data-mode="${mode}"]`);
    const originalText = $btn.html();
    $btn.html('<i class="fa fa-spinner fa-spin"></i> 生成中...');
    $btn.prop('disabled', true);

    // 调用后端接口
    $.ajax({
        url: '/mcp/api/tools/llmRequest',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            toolType: toolType,
            interfaceName: interfaceName,
            methodName: methodName,
            appKey: appKey,
            toolParams: toolParams
        }),
        success: function(response) {
            // 恢复按钮状态
            $btn.html(originalText);
            $btn.prop('disabled', false);

            if (response.code === 0) {
                try {
                    // 只更新工具描述字段
                    if (mode === 'create') {
                        // 填充工具描述
                        $('#toolDescription').summernote('code', response.data || '');
                    } else if (mode === 'update') {
                        // 填充工具描述
                        $('#updateToolDescription').summernote('code', response.data || '');
                    }

                    // 初始化工具提示
                    initTooltips();

                    showAlert('AI生成工具描述成功', 'success');
                } catch (e) {
                    console.error("解析工具描述失败", e);
                    showAlert('解析工具描述失败', 'danger');
                }
            } else {
                showAlert('生成工具描述失败: ' + response.message, 'danger');
            }
        },
        error: function(xhr, status, error) {
            // 恢复按钮状态
            $btn.html(originalText);
            $btn.prop('disabled', false);

            showAlert('生成工具描述失败: ' + error, 'danger');
        }
    });
}
