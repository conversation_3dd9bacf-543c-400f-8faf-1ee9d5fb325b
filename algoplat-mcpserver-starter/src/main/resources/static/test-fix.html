<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具管理修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 引入jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <!-- 引入修复脚本 -->
    <script src="/js/tool-manager-fix.js"></script>
    <div class="container">
        <h1>工具管理修复效果测试</h1>

        <div class="test-section">
            <h3>修复状态检查</h3>
            <div id="fixStatus" class="status info">正在检查修复状态...</div>
            <button onclick="checkFixStatus()">重新检查</button>
        </div>

        <div class="test-section">
            <h3>重复提交测试</h3>
            <p>这个测试模拟用户快速连续点击创建/更新/删除按钮的情况</p>
            <button id="testCreateBtn" onclick="testMultipleCreates()">测试重复创建</button>
            <button id="testUpdateBtn" onclick="testMultipleUpdates()">测试重复更新</button>
            <button id="testDeleteBtn" onclick="testMultipleDeletes()">测试重复删除</button>
            <div id="submitTestResult" class="status info">点击按钮开始测试</div>
        </div>

        <div class="test-section">
            <h3>事件绑定测试</h3>
            <p>这个测试检查事件是否被正确绑定，没有重复绑定</p>
            <button onclick="testEventBinding()">测试事件绑定</button>
            <div id="eventTestResult" class="status info">点击按钮开始测试</div>
        </div>

        <div class="test-section">
            <h3>控制台日志</h3>
            <div id="consoleLog" class="log">等待测试开始...</div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        // 日志记录函数
        function log(message) {
            const logDiv = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('consoleLog').innerHTML = '日志已清空<br>';
        }

        // 检查修复状态
        function checkFixStatus() {
            const statusDiv = document.getElementById('fixStatus');

            // 检查修复脚本是否加载
            const scripts = Array.from(document.scripts).map(s => s.src);
            const hasFixScript = scripts.some(src => src.includes('tool-manager-fix.js'));

            // 检查jQuery是否加载
            const hasJQuery = typeof $ !== 'undefined';

            // 检查修复脚本的功能是否可用
            const hasFixFunctions = window.toolManagerFix &&
                                  typeof window.toolManagerFix.isCreating === 'function' &&
                                  typeof window.toolManagerFix.isUpdating === 'function' &&
                                  typeof window.toolManagerFix.isDeleting === 'function';

            // 检查编辑和删除按钮事件绑定
            const editBtnEvents = $._data(document, 'events');
            let hasEditEvents = false;
            let hasDeleteEvents = false;

            if (editBtnEvents && editBtnEvents.click) {
                editBtnEvents.click.forEach(function(event) {
                    if (event.selector === '.edit-tool-btn') {
                        hasEditEvents = true;
                    }
                    if (event.selector === '.delete-tool-btn') {
                        hasDeleteEvents = true;
                    }
                });
            }

            if (hasFixScript && hasJQuery && hasFixFunctions) {
                statusDiv.className = 'status success';
                let statusText = '✓ 修复脚本已加载并正常工作';
                if (hasEditEvents && hasDeleteEvents) {
                    statusText += '<br>✓ 编辑和删除按钮事件已绑定';
                } else {
                    statusText += '<br>⚠ 编辑或删除按钮事件可能未正确绑定';
                }
                statusDiv.innerHTML = statusText;
                log('修复脚本检查: 已加载');
                log('jQuery检查: 已加载');
                log('修复功能检查: 正常');
            } else if (hasFixScript && hasJQuery && !hasFixFunctions) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '✗ 修复脚本已加载，但功能不可用';
                log('修复脚本检查: 已加载');
                log('jQuery检查: 已加载');
                log('修复功能检查: 异常');
            } else if (hasFixScript && !hasJQuery) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '✗ 修复脚本已加载，但jQuery未加载';
                log('修复脚本检查: 已加载');
                log('jQuery检查: 未加载');
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '✗ 修复脚本未加载';
                log('修复脚本检查: 未加载');
            }
        }

        // 测试重复创建
        function testMultipleCreates() {
            const btn = document.getElementById('testCreateBtn');
            const resultDiv = document.getElementById('submitTestResult');

            btn.disabled = true;
            resultDiv.className = 'status info';
            resultDiv.innerHTML = '正在测试重复创建...';

            let createCount = 0;
            const maxCreates = 5;

            // 确保初始状态为false
            if (window.toolManagerFix) {
                window.toolManagerFix.setCreating(false);
            }

            // 模拟快速连续点击（同步执行，模拟真实的快速点击）
            for (let i = 0; i < maxCreates; i++) {
                // 模拟创建请求
                if (window.toolManagerFix && typeof window.toolManagerFix.isCreating === 'function') {
                    if (window.toolManagerFix.isCreating()) {
                        log(`创建测试 ${i+1}: 被防重复提交机制阻止`);
                    } else {
                        // 模拟设置创建状态
                        window.toolManagerFix.setCreating(true);
                        createCount++;
                        log(`创建测试 ${i+1}: 请求已发送`);
                    }
                } else {
                    createCount++;
                    log(`创建测试 ${i+1}: 无防重复提交机制，请求已发送`);
                }
            }

            // 模拟请求完成后重置状态
            setTimeout(() => {
                if (window.toolManagerFix) {
                    window.toolManagerFix.setCreating(false);
                }

                // 测试完成
                btn.disabled = false;
                if (createCount === 1) {
                    resultDiv.className = 'status success';
                    resultDiv.innerHTML = `✓ 防重复提交测试通过 (只发送了1个请求)`;
                } else {
                    resultDiv.className = 'status error';
                    resultDiv.innerHTML = `✗ 防重复提交测试失败 (发送了${createCount}个请求)`;
                }
            }, 100);
        }

        // 测试重复更新
        function testMultipleUpdates() {
            const btn = document.getElementById('testUpdateBtn');
            const resultDiv = document.getElementById('submitTestResult');

            btn.disabled = true;
            resultDiv.className = 'status info';
            resultDiv.innerHTML = '正在测试重复更新...';

            let updateCount = 0;
            const maxUpdates = 5;

            // 确保初始状态为false
            if (window.toolManagerFix) {
                window.toolManagerFix.setUpdating(false);
            }

            // 模拟快速连续点击（同步执行，模拟真实的快速点击）
            for (let i = 0; i < maxUpdates; i++) {
                // 模拟更新请求
                if (window.toolManagerFix && typeof window.toolManagerFix.isUpdating === 'function') {
                    if (window.toolManagerFix.isUpdating()) {
                        log(`更新测试 ${i+1}: 被防重复提交机制阻止`);
                    } else {
                        // 模拟设置更新状态
                        window.toolManagerFix.setUpdating(true);
                        updateCount++;
                        log(`更新测试 ${i+1}: 请求已发送`);
                    }
                } else {
                    updateCount++;
                    log(`更新测试 ${i+1}: 无防重复提交机制，请求已发送`);
                }
            }

            // 模拟请求完成后重置状态
            setTimeout(() => {
                if (window.toolManagerFix) {
                    window.toolManagerFix.setUpdating(false);
                }

                // 测试完成
                btn.disabled = false;
                if (updateCount === 1) {
                    resultDiv.className = 'status success';
                    resultDiv.innerHTML = `✓ 防重复提交测试通过 (只发送了1个请求)`;
                } else {
                    resultDiv.className = 'status error';
                    resultDiv.innerHTML = `✗ 防重复提交测试失败 (发送了${updateCount}个请求)`;
                }
            }, 100);
        }

        // 测试重复删除
        function testMultipleDeletes() {
            const btn = document.getElementById('testDeleteBtn');
            const resultDiv = document.getElementById('submitTestResult');

            btn.disabled = true;
            resultDiv.className = 'status info';
            resultDiv.innerHTML = '正在测试重复删除...';

            let deleteCount = 0;
            const maxDeletes = 5;

            // 确保初始状态为false
            if (window.toolManagerFix) {
                window.toolManagerFix.setDeleting(false);
            }

            // 模拟快速连续点击（同步执行，模拟真实的快速点击）
            for (let i = 0; i < maxDeletes; i++) {
                // 模拟删除请求
                if (window.toolManagerFix && typeof window.toolManagerFix.isDeleting === 'function') {
                    if (window.toolManagerFix.isDeleting()) {
                        log(`删除测试 ${i+1}: 被防重复提交机制阻止`);
                    } else {
                        // 模拟设置删除状态
                        window.toolManagerFix.setDeleting(true);
                        deleteCount++;
                        log(`删除测试 ${i+1}: 请求已发送`);
                    }
                } else {
                    deleteCount++;
                    log(`删除测试 ${i+1}: 无防重复提交机制，请求已发送`);
                }
            }

            // 模拟请求完成后重置状态
            setTimeout(() => {
                if (window.toolManagerFix) {
                    window.toolManagerFix.setDeleting(false);
                }

                // 测试完成
                btn.disabled = false;
                if (deleteCount === 1) {
                    resultDiv.className = 'status success';
                    resultDiv.innerHTML = `✓ 防重复提交测试通过 (只发送了1个请求)`;
                } else {
                    resultDiv.className = 'status error';
                    resultDiv.innerHTML = `✗ 防重复提交测试失败 (发送了${deleteCount}个请求)`;
                }
            }, 100);
        }

        // 测试事件绑定
        function testEventBinding() {
            const resultDiv = document.getElementById('eventTestResult');
            resultDiv.className = 'status info';
            resultDiv.innerHTML = '正在测试事件绑定...';

            // 检查jQuery事件绑定
            if (typeof $ !== 'undefined') {
                log('开始事件绑定测试');

                // 创建一个唯一的测试按钮类名，避免与现有事件冲突
                const testBtn = $('<button class="test-edit-btn" data-mcpserver-id="1" data-name="test">测试按钮</button>');
                $('body').append(testBtn);

                let clickCount = 0;
                let testCompleted = false;

                // 绑定测试事件（使用唯一的类名）
                const testHandler = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    clickCount++;
                    log(`测试按钮被点击，点击次数: ${clickCount}`);

                    // 防止重复完成测试
                    if (!testCompleted) {
                        testCompleted = true;

                        setTimeout(() => {
                            // 解绑测试事件
                            $(document).off('click', '.test-edit-btn', testHandler);
                            testBtn.remove();

                            if (clickCount === 1) {
                                resultDiv.className = 'status success';
                                resultDiv.innerHTML = '✓ 事件绑定测试通过 (没有重复绑定)';
                                log('事件绑定测试通过');
                            } else {
                                resultDiv.className = 'status error';
                                resultDiv.innerHTML = `✗ 事件绑定测试失败 (事件被触发${clickCount}次)`;
                                log(`事件绑定测试失败，触发了${clickCount}次`);
                            }
                        }, 100);
                    }
                };

                $(document).on('click', '.test-edit-btn', testHandler);

                // 模拟点击
                setTimeout(() => {
                    log('模拟点击测试按钮');
                    testBtn.trigger('click');

                    // 如果5秒后还没有完成测试，认为测试失败
                    setTimeout(() => {
                        if (!testCompleted) {
                            testCompleted = true;
                            $(document).off('click', '.test-edit-btn', testHandler);
                            testBtn.remove();
                            resultDiv.className = 'status error';
                            resultDiv.innerHTML = '✗ 事件绑定测试超时';
                            log('事件绑定测试超时');
                        }
                    }, 5000);
                }, 500);
            } else {
                resultDiv.className = 'status error';
                resultDiv.innerHTML = '✗ jQuery未加载，无法测试事件绑定';
                log('jQuery未加载，无法测试事件绑定');
            }
        }

        // 页面加载完成后自动检查修复状态
        window.addEventListener('load', function() {
            setTimeout(checkFixStatus, 1000);
            log('测试页面加载完成');
        });
    </script>
</body>
</html>

