<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>创建工具调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-info { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
        button { padding: 8px 16px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>创建工具调试页面</h1>

    <div class="debug-section">
        <h3>环境检查</h3>
        <div id="envCheck" class="debug-info">检查中...</div>
        <button onclick="checkEnvironment()">重新检查</button>
    </div>

    <div class="debug-section">
        <h3>McpServerId 检查</h3>
        <div id="mcpServerIdCheck" class="debug-info">检查中...</div>
        <button onclick="checkMcpServerId()">检查McpServerId</button>
        <button onclick="setTestMcpServerId()">设置测试McpServerId</button>
    </div>

    <div class="debug-section">
        <h3>表单字段检查</h3>
        <div id="formFieldsCheck" class="debug-info">检查中...</div>
        <button onclick="checkFormFields()">检查表单字段</button>
        <button onclick="fillTestData()">填充测试数据</button>
    </div>

    <div class="debug-section">
        <h3>事件绑定检查</h3>
        <div id="eventBindingCheck" class="debug-info">检查中...</div>
        <button onclick="checkEventBinding()">检查事件绑定</button>
        <button onclick="simulateFormSubmit()">模拟表单提交</button>
    </div>

    <!-- 引入jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <!-- 引入Summernote -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.js"></script>
    <!-- 引入修复脚本 -->
    <script src="/js/tool-manager-fix.js"></script>

    <script>
        function log(message, type = 'info') {
            console.log(message);
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            return `<span class="${className}">${message}</span><br>`;
        }

        function checkEnvironment() {
            const envDiv = document.getElementById('envCheck');
            let result = '';

            // 检查jQuery
            if (typeof $ !== 'undefined') {
                result += log('✓ jQuery已加载', 'success');
            } else {
                result += log('✗ jQuery未加载', 'error');
            }

            // 检查Summernote
            if ($.fn.summernote) {
                result += log('✓ Summernote已加载', 'success');
            } else {
                result += log('✗ Summernote未加载', 'error');
            }

            // 检查修复脚本
            if (window.toolManagerFix) {
                result += log('✓ 工具管理修复脚本已加载', 'success');
                if (typeof window.toolManagerFix.isCreating === 'function') {
                    result += log('✓ 创建工具功能可用', 'success');
                } else {
                    result += log('✗ 创建工具功能不可用', 'error');
                }
            } else {
                result += log('✗ 工具管理修复脚本未加载', 'error');
            }

            envDiv.innerHTML = result;
        }

        function checkMcpServerId() {
            const mcpServerIdDiv = document.getElementById('mcpServerIdCheck');
            let result = '';

            // 检查隐藏字段
            const hiddenField = $('#mcpServerId').val();
            result += log(`隐藏字段 #mcpServerId: "${hiddenField}"`);

            // 检查页面元素属性
            const dataAttr = $('#toolListTitle').attr('data-mcpserver-id');
            result += log(`页面元素 data-mcpserver-id: "${dataAttr}"`);

            // 检查全局变量
            if (typeof currentMcpServerId !== 'undefined') {
                result += log(`全局变量 currentMcpServerId: "${currentMcpServerId}"`);
            } else {
                result += log('全局变量 currentMcpServerId: 未定义', 'warning');
            }

            mcpServerIdDiv.innerHTML = result;
        }

        function setTestMcpServerId() {
            // 设置测试用的McpServerId
            $('#mcpServerId').val('123');
            $('#toolListTitle').attr('data-mcpserver-id', '123');
            if (typeof window !== 'undefined') {
                window.currentMcpServerId = 123;
            }

            const result = log('✓ 已设置测试McpServerId为123', 'success');
            document.getElementById('mcpServerIdCheck').innerHTML = result;
        }

        function checkFormFields() {
            const formDiv = document.getElementById('formFieldsCheck');
            let result = '';

            const fields = [
                { id: 'mcpServerId', name: 'McpServer ID' },
                { id: 'toolType', name: '工具类型' },
                { id: 'toolName', name: '工具名称' },
                { id: 'toolDescription', name: '工具描述' },
                { id: 'toolOwner', name: '负责人' },
                { id: 'toolTimeout', name: '超时时间' }
            ];

            fields.forEach(field => {
                const element = $(`#${field.id}`);
                if (element.length > 0) {
                    const value = element.val();
                    result += log(`✓ ${field.name} (${field.id}): "${value}"`, 'success');
                } else {
                    result += log(`✗ ${field.name} (${field.id}): 元素不存在`, 'error');
                }
            });

            formDiv.innerHTML = result;
        }

        function fillTestData() {
            // 填充测试数据
            $('#toolType').val('HTTP');
            $('#toolName').val('TestTool');
            $('#toolOwner').val('TestUser');
            $('#toolTimeout').val('6000');

            // 初始化Summernote并设置内容
            if (!$('#toolDescription').hasClass('note-editor')) {
                $('#toolDescription').summernote({
                    height: 200,
                    toolbar: [
                        ['style', ['style']],
                        ['font', ['bold', 'underline', 'clear']],
                        ['color', ['color']],
                        ['para', ['ul', 'ol', 'paragraph']],
                        ['table', ['table']],
                        ['insert', ['link', 'picture', 'video']],
                        ['view', ['fullscreen', 'codeview', 'help']]
                    ]
                });
            }
            $('#toolDescription').summernote('code', '<p>这是一个测试工具的描述</p>');

            const result = log('✓ 已填充测试数据', 'success');
            document.getElementById('formFieldsCheck').innerHTML = result;
        }

        function checkEventBinding() {
            const eventDiv = document.getElementById('eventBindingCheck');
            let result = '';

            // 检查表单是否存在
            const form = $('#createToolForm');
            if (form.length > 0) {
                result += log('✓ 创建工具表单存在', 'success');

                // 检查事件绑定
                const events = $._data(form[0], 'events');
                if (events && events.submit) {
                    result += log(`✓ 表单提交事件已绑定 (${events.submit.length}个处理器)`, 'success');
                } else {
                    result += log('✗ 表单提交事件未绑定', 'error');
                }
            } else {
                result += log('✗ 创建工具表单不存在', 'error');
            }

            // 检查提交按钮
            const submitBtn = $('#createToolModal .modal-footer button[type="submit"]');
            if (submitBtn.length > 0) {
                result += log('✓ 提交按钮存在', 'success');

                const btnEvents = $._data(submitBtn[0], 'events');
                if (btnEvents && btnEvents.click) {
                    result += log(`✓ 按钮点击事件已绑定 (${btnEvents.click.length}个处理器)`, 'success');
                } else {
                    result += log('✗ 按钮点击事件未绑定', 'error');
                }
            } else {
                result += log('✗ 提交按钮不存在', 'error');
            }

            eventDiv.innerHTML = result;
        }

        function simulateFormSubmit() {
            const eventDiv = document.getElementById('eventBindingCheck');
            let result = '';

            // 确保有测试数据
            fillTestData();
            setTestMcpServerId();

            result += log('开始模拟表单提交...', 'info');

            // 检查修复脚本状态
            if (window.toolManagerFix) {
                const isCreating = window.toolManagerFix.isCreating();
                result += log(`当前创建状态: ${isCreating}`, 'info');

                // 模拟表单提交事件
                try {
                    $('#createToolForm').trigger('submit');
                    result += log('✓ 表单提交事件已触发', 'success');
                } catch (error) {
                    result += log(`✗ 表单提交失败: ${error.message}`, 'error');
                }
            } else {
                result += log('✗ 修复脚本不可用', 'error');
            }

            eventDiv.innerHTML = result;
        }

        // 页面加载完成后自动检查环境
        $(document).ready(function() {
            // 创建必要的DOM元素用于测试
            if ($('#toolListTitle').length === 0) {
                $('body').append('<div id="toolListTitle" style="display:none;"></div>');
            }

            if ($('#createToolForm').length === 0) {
                $('body').append(`
                    <form id="createToolForm" style="display:none;">
                        <input type="hidden" id="mcpServerId">
                        <select id="toolType"><option value="HTTP">HTTP</option></select>
                        <input type="text" id="toolName">
                        <textarea id="toolDescription"></textarea>
                        <input type="text" id="toolOwner">
                        <input type="number" id="toolTimeout" value="6000">
                    </form>
                    <div id="createToolModal" style="display:none;">
                        <div class="modal-footer">
                            <button type="submit">创建</button>
                        </div>
                    </div>
                `);
            }

            setTimeout(() => {
                checkEnvironment();
                checkMcpServerId();
                checkFormFields();
                checkEventBinding();
            }, 1000);
        });
    </script>
</body>
</html>

