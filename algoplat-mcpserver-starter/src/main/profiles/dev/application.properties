#�康�����置,����请�步修�check.sh�件TEST_URL
management.server.port=8080
management.endpoints.web.base-path=/monitor
management.endpoints.web.path-mapping.health=/alive
#server��
server.port=8080
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true

# Tomcat??
# ??SO_LINGER?????"Invalid argument"??
server.tomcat.connection-timeout=20000
server.tomcat.max-connections=10000
server.tomcat.accept-count=100
server.tomcat.max-threads=200
# ???-1????SO_LINGER
server.tomcat.socket-properties.so-linger=-1

### SSE????
#spring.mvc.async.request-timeout=60000
