package com.sankuai.mcpserver.algoplat.mcpserver.domain.entity;

import lombok.Data;

import java.util.List;

/**
 * McpServer请求实体类
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Data
public class McpServerRequest {
    
    /**
     * McpServer ID
     */
    private Long id;
    
    /**
     * 业务线ID
     */
    private Long businessLineId;
    
    /**
     * McpServer名称
     */
    private String mcpServerName;
    
    /**
     * McpServer描述
     */
    private String description;
    
    /**
     * McpServer负责人
     */
    private String owner;
    
    /**
     * McpServer ID列表（用于批量操作）
     */
    private List<Long> ids;
}