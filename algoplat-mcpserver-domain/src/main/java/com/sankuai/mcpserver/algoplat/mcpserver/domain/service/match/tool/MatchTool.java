package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.tool;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.service.TAgentChatService;
import com.sankuai.algoplatform.matchops.api.request.mcptool.TMcpTestMissionRequest;
import com.sankuai.algoplatform.matchops.api.response.mcptool.TMcpTestMissionResponse;
import com.sankuai.algoplatform.matchops.api.service.TMcpTestMissionService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DxService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.ThriftToHttpService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.McpToolResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.AnnotationLabelingToolRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.matchtesttool.ExecuteTestToolMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.MatchToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpMissionDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.MissionTypeEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.factory.ThreadPoolFactory;
import io.modelcontextprotocol.server.McpAsyncServerExchange;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.modelcontextprotocol.spec.McpSchema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/18
 */
@Slf4j
@Service
public class MatchTool {

    @Autowired
    private MatchToolService matchToolService;

    @Autowired
    private McpMissionDao mcpMissionDao;

    @Autowired
    private DxService dxService;

    @Autowired
    private ThriftToHttpService thriftToHttpService;

    @Resource
    private TMcpTestMissionService tMcpTestMissionService;

    @Resource
    private TAgentChatService tAgentChatService;

//    @Tool(name = "test_sum_a_b", description = "测试加法")
//    public Map<String, String> testSumab(@ToolParam(description = "a的值") Integer a, @ToolParam(description = "b的值", required = true) Integer b, @ToolParam(description = "ToolContext字段不用主动传递，Spring会自动注入这个字段的值", required = false) ToolContext context) {
//        Map<String, String> map = new HashMap<>();
//        map.put("a", String.valueOf(a));
//        map.put("b", String.valueOf(b));
//        map.put("sum", String.valueOf(a + b));
//        map.put("sessionId", querySessionIdFromToolContext(context));
//        ThreadPoolExecutor taskRunThreadPool = ThreadPoolFactory.getInvokeMissionThreadPool();
//        taskRunThreadPool.execute(() -> {
//            try {
//                Thread.sleep(1000 * 6);
//                AgentMessageDTO agentMessageDTO = new AgentMessageDTO();
//                agentMessageDTO.setSessionId(querySessionIdFromToolContext(context));
//                agentMessageDTO.setContent("/Users/<USER>/IdeaProjects/algoplat-predictor/predictor-service/target/classes/python/algo_pack/bed103bcde3/memory/商分分析结果-关联特征.xlsx;/Users/<USER>/IdeaProjects/algoplat-predictor/predictor-service/target/classes/python/algo_pack/bed103bcde3/memory/正餐匹配定义.json");
//                tAgentChatService.chat("sunrunlai", agentMessageDTO);
//            } catch (InterruptedException e) {
//                log.error(e.getMessage(), e);
//            }
//        });
//        return map;
//    }

    @Tool(name = "execute_friday_model_factory_process", description = "工具作用：执行Friday任务\n" +
            "输入参数：ExecuteFridayMissionRequest对象的JSON字符串\n" +
            "inputS3Url：测试集地址，必填。\n" +
            "s3Urls：数据集S3地址，可选。用于创建或选择数据集版本。\n" +
            "datasetName：数据集名称，可选。\n" +
            "versionList：数据集版本号，可选。\n" +
            "trainMethod：训练方法，必填（sft/dpo）。\n" +
            "modelTrainAdvancedParams：训练高级参数，可选。\n" +
            "modelTrainDefaultParams：模型训练默认参数，可选。\n" +
            "misId：用户mis账号，必填。\n" +
            "modelParam：评测参数，必填。\n" +
            "modelBaseName：模型基座名称，必填。\n" +
            "\n" +
            "输出参数：McpServerResponse\n" +
            "code：返回码（-1失败，0成功）\n" +
            "message：执行信息\n" +
            "data：任务的执行结果信息 包含以下字段：1、status -1失败 1成功 0执行中 -2未开始执行 2、request 任务请求原始信息透传 3、missionResult 测评结果地址 4、missionId 任务执行Id 5、contextInfo 执行的上下文信息。\n" +
            "\n" +
            "提示1\n" +
            "s3Urls、datasetName、versionList三个参数之间的关系如下：\n" +
            "1、s3Urls != null &&  datasetName == null。此时基于S3文件链接创建一个新的数据集，数据集的命名规则为：dataset_+当前时间。如dataset_20250511123134\n" +
            "2、s3Urls != null && datasetName != null 。该数据集下会根据s3Urls中的文件 新增一个数据集版本号，并且该文件在上传后会默认添加进versionList的值里并进行模型训练。\n" +
            "3、s3Urls ==null && datasetName!=null && versionList!=null。此时选择数据集下的已有的指定版本进行训练。\n" +
            "提示2\n" +
            "modelTrainAdvancedParams和modelTrainDefaultParams二者不可同时为空，若同时存在会使用modelTrainDefaultParams中的参数，其中modelTrainDefaultParams的参数示例：{\"lr\":0.00001,\"globalBatchSize\":128,\"seqLength\":8000,\"epoch\":8,\"devRatio\":\"10\",\"seed\":42}\n")
    public McpToolResponse executeFridayModelFactoryProcess(@ToolParam(description = "参数:request ExecuteFridayMissionRequest 类对象， 是任务执行请求入参。其中一种请求情况示例：{\n" +
            "    \"modelBaseName\": \"LongCat-8B-8K-Chat\",\n" +
            "    \"trainMethod\": \"sft\",\n" +
            "    \"s3Urls\": \"https://msstest.vip.sankuai.com/dj-model-diff-input/accurate_dinner_data_train_update_v2.jsonl?Signature=1OS8XVOCyj4vQNG7XgrjDKKMcvk%3D&Expires=1747395640&AWSAccessKeyId=SRV_6oTleJtW7ItXco4zl91hIvIqLWSfkiNs\",\n" +
            "    \"inputS3Url\": \"https://msstest.vip.sankuai.com/dj-model-diff-input/accurate_dinner_test_data.jsonl?Signature=GbNjYMoGraz%2FJvCj39jVocWShPg%3D&Expires=1747387565&AWSAccessKeyId=SRV_6oTleJtW7ItXco4zl91hIvIqLWSfkiNs\",\n" +
            "    \"modelParam\": {\n" +
            "        \"top_p\": \"0.7\",\n" +
            "        \"batch_size\": \"512\",\n" +
            "        \"max_new_tokens\": \"50\",\n" +
            "        \"prompt_prefix\": \"\",\n" +
            "        \"top_k\": \"1\",\n" +
            "        \"prompt_suffix\": \"\",\n" +
            "        \"temperature\": \"0.001\",\n" +
            "        \"max_seq_length\": \"8000\"\n" +
            "    },\n" +
            "    \"modelTrainDefaultParams\": {\n" +
            "        \"lr\": 0.00001,\n" +
            "        \"globalBatchSize\": 128,\n" +
            "        \"seqLength\": 8000,\n" +
            "        \"epoch\": 8,\n" +
            "        \"devRatio\": \"10\",\n" +
            "        \"seed\": 42\n" +
            "    },\n" +
            "    \"misId\": \"zhouzehao02\"\n" +
            "}", required = true) String request, @ToolParam(description = "toolContext字段，是ToolContext类对象，工具调用执行上下文,此字段不用传递，因为Spring会自动将这个入参注入进去，如果调用失败，可尝试传递{}", required = false) ToolContext toolContext) {
        String missionId = null;
        String sessionId = null;
        try {
            if (request == null) {
                return McpToolResponse.paramsError("请求参数为空", null);
            }
            ExecuteFridayMissionRequest fridayRequest = JSONObject.parseObject(request, ExecuteFridayMissionRequest.class);
            //获取sessionId
            if (toolContext != null) {
                sessionId = querySessionIdFromToolContext(toolContext);
            } else {
                sessionId = "Mock_" + DigestUtils.md5Hex(JacksonUtil.toJsonStr(fridayRequest) + System.currentTimeMillis());
            }
            String checkResult = checkFridayRequest(fridayRequest);
            if (checkResult != null) {
                Cat.logError(new Exception(checkResult));
                return McpToolResponse.paramsError(checkResult, null);
            }
            McpMissionInfo mcpMissionInfoByMission = mcpMissionDao.getMcpMissionInfoByMissionId(fridayRequest.getMissionId());
            Boolean isExist = fridayRequest != null && fridayRequest.getMissionId() != null && mcpMissionInfoByMission != null;
            if (isExist) {
                missionId =fridayRequest.getMissionId();
                String missionContext = mcpMissionInfoByMission.getMissionContext();
                ExecuteFridayMissionContext context = JSONObject.parseObject(missionContext, ExecuteFridayMissionContext.class);
                context.setStatus(0);
                context.setExecuteMessage(String.format("正在重新发起Friday执行流程,missionId:%s", context.getMissionId()));
                mcpMissionInfoByMission.setMissionContext(JSONObject.toJSONString(context));
                mcpMissionInfoByMission.setStatus(0);
                mcpMissionInfoByMission.setMisId(fridayRequest.getMisId());
                mcpMissionDao.updateMissionInfo(mcpMissionInfoByMission);
            } else {
                missionId = DigestUtils.md5Hex(JacksonUtil.toJsonStr(fridayRequest) + System.currentTimeMillis());
                executeFriday(missionId, fridayRequest, sessionId);
            }
            Map<String, Object> result = new HashMap<>();
            result.put("missionId", missionId);
            result.put("sessionId", sessionId);
            result.put("isLongTermTask", "是");
            return McpToolResponse.success("成功", result);
        } catch (Exception e) {
            
            log.error("execute_friday_model_factory_process error", e);
            Map<String, Object> result = new HashMap<>();
            result.put("missionId", missionId);
            result.put("sessionId", sessionId);
            return McpToolResponse.fail(e.getMessage(), null);
        }
    }

    @Tool(name = "query_friday_mission_result", description = "工具作用:Friday任务执行结果查询工具")
    private McpToolResponse queryFridayMissionResult(@ToolParam(description = "missionId 任务执行ID String类型") String missionId) {
        Transaction t = Cat.newTransaction(MatchTool.class.getSimpleName(), "query_friday_mission_result");
        if (StringUtils.isBlank(missionId)) {
            t.setStatus("任务ID为空");
            return McpToolResponse.paramsError();
        }
        Cat.logEvent("queryFridayMissionStatus", missionId);
        try {
            log.info("查询Friday任务结果，missionId：{}", missionId);
            McpToolResponse mcpToolResponse = thriftToHttpService.queryFridayMissionStatus(missionId);
            t.setSuccessStatus();
            log.info("查询Friday任务结果成功：missionId：{},result{}", missionId, JSONObject.toJSONString(mcpToolResponse));
            return mcpToolResponse;
        } catch (Exception e) {
            t.setStatus(e);
            
            log.error("查询Friday任务结果失败！missionId:{}", missionId, e);
            return McpToolResponse.fail(e.getMessage(), null);
        }
    }

    @Tool(name = "submit_matching_test_tool_task", description = "提交匹配测试工具任务")
    public McpToolResponse submitMatchingTestToolTask(@ToolParam(description = "TMcpTestMissionRequest对象") TMcpTestMissionRequest request, @ToolParam(description = "工具调用的上下文信息，此字段不用传递，在工具调用时Spring会自动填充此字段") ToolContext toolContext) {

        Transaction t = Cat.newTransaction(MatchTool.class.getSimpleName(), "submit_matching_test_tool_task");
        ExecuteTestToolMissionContext mcpMissionContext = new ExecuteTestToolMissionContext();
        String missionId = null;
        String sessionId = null;
        try {
            log.info("submitMatchingTestToolTask", JSONObject.toJSONString(request));
            //获取sessionId
            sessionId = querySessionIdFromToolContext(toolContext);
            if (sessionId == null) {
                sessionId = request.getSessionId();
            }
            request.setSessionId(sessionId);
            missionId = DigestUtils.md5Hex(String.valueOf(System.currentTimeMillis()));
            mcpMissionContext.setMissionId(missionId);
            mcpMissionContext.setStatus(0);
            mcpMissionContext.setMessage("任务执行中,missionId=" + missionId);
            mcpMissionContext.setMissionId(missionId);
            mcpMissionContext.setRequest(request);
            mcpMissionContext.setSessionId(sessionId);
            mcpMissionContext.setMisId(request.getMisId());
            McpMissionInfo mcpMissionInfo = new McpMissionInfo();
            mcpMissionInfo.setMissionRequest(JSONObject.toJSONString(request));
            Map<String, String> extra = new HashMap<>();
            mcpMissionInfo.setMisId(request.getMisId());
            mcpMissionInfo.setType(MissionTypeEnum.CHECK.getCode());
            mcpMissionInfo.setMissionContext(JSONObject.toJSONString(mcpMissionContext));
            mcpMissionInfo.setMissionId(missionId);
            mcpMissionInfo.setStatus(0);
            mcpMissionInfo.setMisId(request.getMisId());
            extra.put("missionId", missionId);
            extra.put("sessionId", sessionId);
            request.setExtraInfo(extra);
            mcpMissionDao.insertMcpMissionInfo(mcpMissionInfo);
            TMcpTestMissionResponse tMcpTestMissionResponse = tMcpTestMissionService.submitMcpTestTask(request);
            McpMissionInfo mcpMissionInfoByMissionId = mcpMissionDao.getMcpMissionInfoByMissionId(missionId);
            if (tMcpTestMissionResponse.getCode() != 0) {
                String msg = "测试任务执行失败，missionId:" + missionId + ",msg:" + tMcpTestMissionResponse.getMessage();
                log.error(msg);
                mcpMissionContext.setStatus(-1);
                mcpMissionContext.setMessage(tMcpTestMissionResponse.getMessage());
                mcpMissionInfoByMissionId.setMissionContext(JSONObject.toJSONString(mcpMissionContext));
                mcpMissionDao.updateMissionInfo(mcpMissionInfoByMissionId);
                t.setStatus(msg);
                return McpToolResponse.fail(msg, null);
            }
            Map<String, String> data = tMcpTestMissionResponse.getData();
            if (CollectionUtils.isEmpty(data)) {
                throw new RuntimeException("测试任务执行失败，missionId:" + missionId + ",msg:" + tMcpTestMissionResponse.getMessage());
            }

            String taskId = data.get("taskId");
            mcpMissionContext.setTaskId(Long.valueOf(taskId));
            mcpMissionInfoByMissionId.setMissionContext(JSONObject.toJSONString(mcpMissionContext));
            mcpMissionInfoByMissionId.setStatus(0);
            mcpMissionInfoByMissionId.setUpdateTime(new Date());
            mcpMissionDao.updateMissionInfo(mcpMissionInfoByMissionId);
            t.setSuccessStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("missionId", missionId);
            result.put("sessionId", sessionId);
            result.put("isLongTermTask", "是");
            return McpToolResponse.success("成功", result);
        } catch (Exception e) {
            McpMissionInfo mcpMissionInfoByMissionId = mcpMissionDao.getMcpMissionInfoByMissionId(missionId);
            mcpMissionInfoByMissionId.setStatus(-1);
            mcpMissionContext.setStatus(-1);
            mcpMissionContext.setMessage(e.getMessage());
            mcpMissionInfoByMissionId.setUpdateTime(new Date());
            mcpMissionDao.updateMissionInfo(mcpMissionInfoByMissionId);
            t.setStatus(e);
            log.error("submitMatchingTestToolTask error,missionId:{}", missionId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("missionId", missionId);
            result.put("sessionId", sessionId);
            return McpToolResponse.fail(e.getMessage(), null);
        }
    }

    private String querySessionIdFromToolContext(ToolContext toolContext) {
        if (toolContext == null) {
            return null;
        }
        Map<String, Object> context = toolContext.getContext();
        if (MapUtils.isEmpty(context)) {
            return null;
        }
        Object exchange = context.get("exchange");
        if (exchange == null) {
            return null;
        }
        try {
            McpSyncServerExchange mcpAsyncServerExchange = (McpSyncServerExchange) exchange;
            // 获取 exchange 字段
            Field exchangeField = McpSyncServerExchange.class.getDeclaredField("exchange");
            exchangeField.setAccessible(true);
            McpAsyncServerExchange asyncExchange = (McpAsyncServerExchange) exchangeField.get(mcpAsyncServerExchange);
            McpSchema.Implementation clientInfo = asyncExchange.getClientInfo();
            return clientInfo.name();
        } catch (Exception e) {
            log.error("querySessionIdFromToolContext error", e);
            
            return null;
        }
    }

    @Tool(name = "query_mcp_mission_result_by_mission_id", description = "根据任务ID查询任务执行结果")
    public McpToolResponse queryMatchingTestToolTaskResult(@ToolParam(description = "missionId 任务id String") String missionId) {

        Transaction t = Cat.newTransaction(MatchTool.class.getSimpleName(), "query_mcp_mission_result_by_mission_id");
        if (StringUtils.isBlank(missionId)) {
            t.setStatus("任务ID为空");
            return McpToolResponse.paramsError();
        }
        Cat.logEvent("queryMatchingTestToolTaskResult", missionId);
        try {
            log.info("查询任务结果，missionId：{}", missionId);
            McpMissionInfo mcpMissionInfoByMissionId = mcpMissionDao.getMcpMissionInfoByMissionId(missionId);
            if (mcpMissionInfoByMissionId == null) {
                throw new RuntimeException("任务不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("status", mcpMissionInfoByMissionId.getStatus());
            String missionResultJson = mcpMissionInfoByMissionId.getMissionResult();
            if (!StringUtils.isBlank(missionResultJson) && isJsonMap(missionResultJson)) {
                Map map = JSONObject.parseObject(missionResultJson, Map.class);
                result.put("result", map.get("result"));
                result.put("link", map.get("link"));
            }
//            else if (!StringUtils.isBlank(missionResultJson) && isListJson(missionResultJson) && !"[]".equals(missionResultJson)) {
//                List list = JSONObject.parseObject(missionResultJson, List.class);
//                JSONObject reporterJSON = (JSONObject) list.get(0);
//                result.put("link", reporterJSON.getString("reporterAddr"));
//            }
            else {
                result.put("result", missionResultJson);
            }
            t.setSuccessStatus();
            log.info("查询MCP任务结果成功：missionId：{},result{}", missionId, JSONObject.toJSONString(result));
            return McpToolResponse.success("查询成功", result);
        } catch (Exception e) {
            t.setStatus(e);
            
            log.error("查询Friday任务结果失败！missionId:{}", missionId, e);
            return McpToolResponse.fail(e.getMessage(), null);
        }
    }

    private boolean isListJson(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return false;
        }
        String trimmed = jsonString.trim();
        return trimmed.startsWith("[") && trimmed.endsWith("]");
    }

    public static boolean isJsonMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return false;
        }
        String trimmed = jsonString.trim();
        return trimmed.startsWith("{") && trimmed.endsWith("}");
    }

    private void executeFriday(String missionId, ExecuteFridayMissionRequest fridayRequest, String sessionId) {
        Transaction t = Cat.newTransaction(MatchTool.class.getSimpleName(), "execute_friday_model_factory_process");
        ExecuteFridayMissionRequest executeFridayMissionRequest = null;
        ExecuteFridayMissionContext executeFridayMissionContext = null;
        executeFridayMissionContext = new ExecuteFridayMissionContext();
        executeFridayMissionContext.setMissionId(missionId);
        executeFridayMissionContext.setRequest(fridayRequest);
        executeFridayMissionContext.setSessionId(sessionId);
        try {
            executeFridayMissionContext.setStatus(0);
            executeFridayMissionContext.setExecuteMessage("Friday任务已创建，等待被调度!");
            McpMissionInfo mcpMissionInfo = new McpMissionInfo();
            mcpMissionInfo.setMissionId(missionId);
            mcpMissionInfo.setStatus(0);
            mcpMissionInfo.setType(MissionTypeEnum.FRIDAY.getCode());
            mcpMissionInfo.setMisId(fridayRequest.getMisId());
            mcpMissionInfo.setMissionRequest(JSONObject.toJSONString(fridayRequest));
            mcpMissionInfo.setMissionContext(JSONObject.toJSONString(executeFridayMissionContext));
            mcpMissionDao.insertMcpMissionInfo(mcpMissionInfo);
            log.info("executeFridayModelFactoryProcess success,request:{}", executeFridayMissionRequest);
            t.setSuccessStatus();
        } catch (Exception e) {
            log.error("executeFridayModelFactoryProcess error,request:{}", executeFridayMissionRequest, e);
            
            t.setStatus(e);
        } finally {
            t.complete();
        }
    }

    private McpMissionInfo buildFridayMissionInfo(ExecuteFridayMissionContext context, ExecuteFridayMissionRequest fridayRequest, McpMissionInfo fridayMissionInfo) {
        fridayMissionInfo.setUpdateTime(new Date());
        fridayMissionInfo.setMissionId(context.getMissionId());
        fridayMissionInfo.setStatus(context.getStatus());
        fridayMissionInfo.setMissionRequest(JSONObject.toJSONString(fridayRequest));
        fridayMissionInfo.setMissionResult(context.getMissionResult());
        fridayMissionInfo.setMissionContext(JSONObject.toJSONString(context));
        fridayMissionInfo.setMissionId(context.getMissionId());
        return fridayMissionInfo;
    }

    private String checkFridayRequest(ExecuteFridayMissionRequest fridayRequest) {
        if (fridayRequest != null && fridayRequest.getMissionId() != null) {
            return null;
        }
        if (fridayRequest == null) {
            return "Friday任务入参为空";
        } else if (StringUtils.isBlank(fridayRequest.getModelBaseName())) {
            return "Friday模型基座名称,modelBaseName字段为空";
        } else if (StringUtils.isBlank(fridayRequest.getMisId())) {
            return "Friday任务执行人,misId字段为空,请输入执行人misId,例如：zhouzehao02";
        } else if (StringUtils.isBlank(fridayRequest.getTrainMethod())) {
            return "Friday训练方,trainMethod字段为空,请输入训练方式:sft或dpo";
        } else if (StringUtils.isBlank(fridayRequest.getInputS3Url())) {
            return "Friday评测集,inputS3Url字段为空,请输入评测数据S3链接";
        } else if (CollectionUtils.isEmpty(fridayRequest.getModelParam())) {
            return "Friday训练参数,modelParam字段为空,请输入模型训练参数";
        } else if (CollectionUtils.isEmpty(fridayRequest.getModelTrainAdvancedParams()) && CollectionUtils.isEmpty(fridayRequest.getModelTrainDefaultParams())) {
            return "Friday训练参数,modelTrainAdvancedParams或modelTrainDefaultParams字段为空,请输入模型高级训练参数或默认训练参数中的一个";
        } else if (StringUtils.isBlank(fridayRequest.getDatasetName()) && CollectionUtils.isEmpty(fridayRequest.getVersionList()) && StringUtils.isBlank(fridayRequest.getS3Urls())) {
            return "Friday训练数据集,datasetName、s3Urls字段不能同时为空,请按照规定进行请求：" +
                    "情况一：\n" +
                    "\n" +
                    "s3Urls != null &&  datasetName == null。\n" +
                    "\n" +
                    "此时基于S3文件链接创建一个新的数据集，并使用s3Urls对应的文件进行模型的训练。数据集的命名规则为：dataset_+当前时间。如dataset_20250511123134\n" +
                    "\n" +
                    "情况二：\n" +
                    "\n" +
                    "s3Urls != null && datasetName != null \n" +
                    "\n" +
                    "该数据集下会根据s3Urls中的文件 新增一个数据集版本号，并且该文件在上传后会默认添加进versionList的值里并进行模型训练。\n" +
                    "\n" +
                    "情况三：\n" +
                    "\n" +
                    "s3Urls ==null && datasetName!=null && versionList!=null\n" +
                    "\n" +
                    "此时选择数据集下的已有的指定版本进行训练。\n" +
                    "\n";
        } else {
            return null;
        }
    }

    @Tool(name = "search_annotation_data_using_labeling_tool", description = "工具描述：获取标注数据")
    public McpToolResponse<Map<String, Object>> searchAnnotationDataUsingLabelingTool(@ToolParam(description = "AnnotationLabelingToolRequest") AnnotationLabelingToolRequest annotationLabelingToolRequest, @ToolParam(description = "工具调用的上下文信息，此字段不用传递，在工具调用时Spring会自动填充此字段") ToolContext toolContext) {
        Transaction transaction = Cat.newTransaction(MatchTool.class.getSimpleName(), "search_annotation_data_using_labeling_tool");
        log.info("开始获取标注数据,请求参数:{}", JSONObject.toJSONString(annotationLabelingToolRequest));
        if (annotationLabelingToolRequest == null
                || CollectionUtils.isEmpty(annotationLabelingToolRequest.getDataSetName())
                || annotationLabelingToolRequest.getAnnotationType() == null
                //|| !ANNOTATION_TASK_TYPE.contains(annotationLabelingToolRequest.getAnnotationType())
                || annotationLabelingToolRequest.getMatchType() == null
                //|| !MATCH_TYPE.contains(annotationLabelingToolRequest.getMatchType())
                || annotationLabelingToolRequest.getBizLine() == null
            //|| !INDUSTY_TYPE.contains(annotationLabelingToolRequest.getBizLine())
            //|| checkSceneName(annotationLabelingToolRequest.getBizLine(), annotationLabelingToolRequest.getSceneName())
        ) {
            return McpToolResponse.paramsError( "请求参数有误！", null);
        }
        String sessionId = null;
        try {
            List<String> dataSetName = annotationLabelingToolRequest.getDataSetName();
            if (CollectionUtils.isEmpty(dataSetName)) {
                throw new RuntimeException("数据集名称为空");
            }

            String misId = querySessionIdFromToolContext(toolContext);
            if (StringUtils.isBlank(misId)) {
                misId = "raomaoyuan";
            }
            Tracer.putContext("userMis", misId);
            Tracer.putContext("userName", misId);
            Map<String, Object> UrlResults = new HashMap();
            sessionId = querySessionIdFromToolContext(toolContext);
            UrlResults.put("sessionId", sessionId);
            Map<String, String> urls = new HashMap<>();
            for (String name : dataSetName) {
                String url = matchToolService.searchAnnotationDataUsingLabelingTool(name, annotationLabelingToolRequest);
                urls.put(name, url);
            }
            UrlResults.put("dataSetUrls", JSONObject.toJSONString(urls));
            log.info("request:{},标注数据URLS:{}", JSONObject.toJSONString(annotationLabelingToolRequest), JSONObject.toJSONString(UrlResults));
            transaction.setSuccessStatus();
            return McpToolResponse.success("成功", UrlResults);
        } catch (Exception e) {
            transaction.setStatus(e);
            Map<String, Object> UrlResults = new HashMap();
            UrlResults.put("sessionId", sessionId);
            log.error("获取标注数据失败,请求参数:{}", JSONObject.toJSONString(annotationLabelingToolRequest), e);
            
            return McpToolResponse.fail(e.getMessage(), UrlResults);
        } finally {
            transaction.complete();
        }
    }

    private boolean checkSceneName(Integer industryType, String sceneName) {
        if (StringUtils.isBlank(sceneName)) {
            return true;
        }
        if (industryType == 1001 && !"境内直连".contains(sceneName)) {
            return true;
        } else if (industryType == 1002 && !"境外直连".contains(sceneName)) {
            return true;
        } else if (industryType == 1007 && !Lists.newArrayList("美发", "球类运动", "美甲").contains(sceneName)) {
            return true;
        } else if (industryType == 1006 && !Lists.newArrayList("正餐", "轻快餐").contains(sceneName)) {
            return true;
        } else {
            return false;
        }
    }

}
