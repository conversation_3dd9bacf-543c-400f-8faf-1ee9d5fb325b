package com.sankuai.mcpserver.algoplat.mcpserver.domain.configs;


import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.common.domain.GenericType;
import com.google.common.collect.Maps;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamQueryThriftService;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamWriteThriftService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.algoplatform.agentapi.client.service.TAgentChatService;
import com.sankuai.algoplatform.agentapi.client.service.TAgentSessionService;
import com.sankuai.algoplatform.agentapi.client.service.TAgentLearningService;
import com.sankuai.algoplatform.matchops.api.service.TXuechengService;
import com.sankuai.guiguzi.annotation.thrift.*;
import com.sankuai.guiguzi.annotation.thrift.nosso.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ThriftToolInfo;
import com.sankuai.nib.data.zb.flow.platform.api.service.FlowExecutorThriftService;
import com.sankuai.nib.data.zb.parse.platform.api.service.ParseRuleValidService;
import com.sankuai.zb.metadata.api.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.Objects;

import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.LionConfig.*;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/10
 */
@Slf4j
@Configuration
public class ThriftClientProxyBeanConfig {

    public static Map<String, ThriftClientProxy> serviceClient = Maps.newConcurrentMap();

    @Bean
    public ThriftClientProxy parseRuleThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.zb.metadata");
        proxy.setServerIpPorts(metadataIp);
        proxy.setServiceInterface(ParseRuleThriftService.class);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy flowManagerThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.zb.metadata");
        proxy.setServerIpPorts(metadata9001);
        proxy.setServiceInterface(FlowManagerThriftService.class);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy deliverConfigThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.zb.metadata");
        proxy.setServerIpPorts(metadataIp);
        proxy.setServiceInterface(DeliverConfigThriftService.class);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy taskFlowThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.zb.metadata");
        proxy.setServerIpPorts(metadata9001);
        proxy.setServiceInterface(TaskFlowThriftService.class);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy parseRuleVersionThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.zb.metadata");
        proxy.setServerIpPorts(metadata9001);
        proxy.setServiceInterface(ParseRuleVersionThriftService.class);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy FlowExecutorThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.zb.flow.platform");
        proxy.setServiceInterface(FlowExecutorThriftService.class);
        proxy.setServerIpPorts(zbflowPlatformIp9001);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy FlowQueryThriftService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.zb.metadata");
        proxy.setServiceInterface(FlowQueryThriftService.class);
        proxy.setServerIpPorts(metadata9001);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy parseRuleValidService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.zb.parse.platform");
        proxy.setServiceInterface(ParseRuleValidService.class);
        proxy.setServerIpPorts(parsePlatformIp);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy tXuechengService() throws Exception {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.algoplatform.matchops");
        proxy.setServiceInterface(TXuechengService.class);
        proxy.setServerIpPorts(matchopsIp);
        proxy.setTimeout(10000);
        proxy.afterPropertiesSet();
        return proxy;
    }

    @Bean
    public ThriftClientProxy IDataStreamWriteThriftService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.cms.knowledge.build");
        proxy.setServiceInterface(IDataStreamWriteThriftService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000);
        return proxy;
    }

    @Bean
    public ThriftClientProxy IDataStreamQueryThriftService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.cms.knowledge.build");
        proxy.setServiceInterface(IDataStreamQueryThriftService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000);
        return proxy;
    }

    @Bean
    public ThriftClientProxy TAgentChatService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.algoplatform.agentapi");
        proxy.setServiceInterface(TAgentChatService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000 * 6);
        return proxy;
    }

    @Bean
    public ThriftClientProxy TAgentLearningService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.algoplatform.agentapi");
        proxy.setServiceInterface(TAgentLearningService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000 * 6);
        return proxy;
    }

    @Bean
    public ThriftClientProxy TAgentSessionService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.algoplatform.agentapi");
        proxy.setServiceInterface(TAgentSessionService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000);
        return proxy;
    }

    @Bean
    public ThriftClientProxy AlgorithmAnnotationTService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.guiguzi.annotation");
        proxy.setServiceInterface(AlgorithmAnnotationTService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000);
        return proxy;
    }

    public ThriftClientProxy createThriftProxy(ThriftToolInfo thriftToolInfo) throws Exception {
        String thriftProxyKey = getThriftProxyKey(thriftToolInfo);
        if (serviceClient.containsKey(thriftProxyKey)) {
            return serviceClient.get(thriftProxyKey);
        }
        ThriftClientProxy clientProxy = new ThriftClientProxy();
        clientProxy.setGeneric(GenericType.JSON_SIMPLE.getName());
        clientProxy.setAppKey(ServiceConstants.APP_KEY);
        clientProxy.setTimeout(thriftToolInfo.getTimeOut() == null ? 6000 : thriftToolInfo.getTimeOut());
        clientProxy.setGenericServiceName(thriftToolInfo.getInterfaceName());

        if (thriftToolInfo.getAppKey() != null) {
            clientProxy.setRemoteAppkey(thriftToolInfo.getAppKey());
            if (!StringUtils.isBlank(thriftToolInfo.getPort())) {
                clientProxy.setRemoteServerPort(Integer.parseInt(thriftToolInfo.getPort()));
            } else {
                clientProxy.setFilterByServiceName(true);
            }
        } else if (!StringUtils.isBlank(thriftToolInfo.getIp()) && !StringUtils.isBlank(thriftToolInfo.getPort())) {
            clientProxy.setRemoteUniProto(true);
            clientProxy.setServerIpPorts(thriftToolInfo.getIp() + ":" + thriftToolInfo.getPort());
        }
        clientProxy.setRemoteUniProto(true);
        clientProxy.afterPropertiesSet();
        serviceClient.put(thriftProxyKey, clientProxy);
        return clientProxy;
    }

    public void destroy(String proxyKey) {
        ThriftClientProxy clientProxy = serviceClient.remove(proxyKey);
        if (Objects.isNull(clientProxy)) {
            return;
        }
        try {
            clientProxy.destroy();
        } catch (Exception e) {
            log.error("GeneralizeClient.destroy clientProxy:{}", clientProxy, e);
        }
    }

    @PreDestroy
    private void destroyAll() {
        if (MapUtils.isEmpty(serviceClient)) {
            return;
        }
        for (Map.Entry<String, ThriftClientProxy> entry : serviceClient.entrySet()) {
            destroy(entry.getKey());
            log.info("destroy thrift proxy, key:{}", entry.getKey());
        }
    }

    private String getThriftProxyKey(ThriftToolInfo thriftToolInfo) {
        return thriftToolInfo.getAppKey() + "_" + thriftToolInfo.getInterfaceName() + "_" + thriftToolInfo.getCell() + "_" + thriftToolInfo.getIp() + "_" + thriftToolInfo.getPort() + "_" + thriftToolInfo.getTimeOut();
    }

    public void destroy(ThriftToolInfo thriftToolInfo) {
        String proxyKey = getThriftProxyKey(thriftToolInfo);
        ThriftClientProxy clientProxy = serviceClient.remove(proxyKey);
        if (Objects.isNull(clientProxy)) {
            return;
        }
        try {
            clientProxy.destroy();
        } catch (Exception e) {
            log.error("GeneralizeClient.destroy clientProxy:{}", clientProxy, e);
        }
    }
}
