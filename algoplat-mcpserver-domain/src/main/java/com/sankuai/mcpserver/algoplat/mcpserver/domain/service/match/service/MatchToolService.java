package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service;

import com.sankuai.algoplatform.matchops.api.request.mcptool.TMcpTestMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.McpMissionCommonContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.AnnotationLabelingRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.AnnotationLabelingToolRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/11
 */
public interface MatchToolService {

    String searchAnnotationDataUsingLabelingTool(String dataSetName, AnnotationLabelingToolRequest request) throws Exception;

    McpMissionCommonContext executeMcpLoadTestTask(String tMcpTestMissionRequestJson,String missionId) throws Exception;
}
