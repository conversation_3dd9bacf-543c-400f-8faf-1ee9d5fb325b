package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service;


public interface GitService {
    /**
     * 检查分支或Tag是否存在
     *
     * @param project 仓库组名
     * @param repo 仓库名
     * @param name 分支名或Tag名
     * @param type 类型(branch或tag)
     * @return 存在返回true，否则返回false
     */
    boolean isBranchOrTagExist(String project, String repo, String name, String type, String misId);
}
