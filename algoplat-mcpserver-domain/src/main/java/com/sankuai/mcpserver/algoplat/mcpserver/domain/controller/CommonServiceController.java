package com.sankuai.mcpserver.algoplat.mcpserver.domain.controller;

import com.google.common.collect.Lists;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsKeywordQueryRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsScalarQueryRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsTextVectorQueryRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsTextVectorWriteRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.CreateCitadelDocReq;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.XueChengRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.XueChengRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DefaultToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DxService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.XmCitadelService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.McpToolResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.impl.FridayServiceImpl;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.tool.MatchTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/19
 */
@Slf4j
@RestController
@RequestMapping("/common")
public class CommonServiceController {

    @Autowired
    private DefaultToolService defaultToolService;

    @Autowired
    private XmCitadelService xmCitadelService;

    @Resource
    private DxService dxService;

    @Resource
    private MatchTool matchTool;

    @Resource
    private FridayServiceImpl fridayServiceImpl;

    @GetMapping("/getCacheData")
    public McpToolResponse<String> getCacheData(@RequestParam  String queryKey) {
        return defaultToolService.loadIntermediateInfo(queryKey);
    }

    @GetMapping("/getIter")
    public String getIter(@RequestParam  String instanceId, @RequestParam String jobId,@RequestParam Integer rankId,@RequestParam String trainMethod,@RequestParam String misId) throws Exception {

        return fridayServiceImpl.getIter(instanceId, jobId, rankId, trainMethod, misId);
    }

    @PostMapping("/createXueChengDoc")
    public McpServerResponse createXueChengDoc(@RequestBody CreateCitadelDocReq createCitadelDocReq) {
        McpServerResponse response = xmCitadelService.createCitadelDoc(createCitadelDocReq);
        return response;
    }

    @GetMapping("/getXueChengDoc")
    public McpServerResponse getXueChengDoc(@RequestParam("url") String url, @RequestParam("misId") String misId) throws Exception {
        McpServerResponse xuechengContentWithMarkdown = xmCitadelService.getXuechengContentWithMarkdown(url, misId);
        return xuechengContentWithMarkdown;
    }

    @GetMapping("/getXueChengDocWithJson")
    public McpServerResponse getXueChengDocWithJson(@RequestParam("url") String url, @RequestParam("misId") String misId) throws Exception {
        McpServerResponse xuechengContentWithJson = xmCitadelService.getCitadelContentJson(misId, url);
        return xuechengContentWithJson;
    }

    @PostMapping("/uploadFileToS3")
    public McpToolResponse<String> uploadFileToS3AndGetFileUrl(@RequestParam("file") MultipartFile file) {
        McpToolResponse<String> stringMcpToolResponse = defaultToolService.uploadFileToS3AndGetFileUrl(file);
        return stringMcpToolResponse;
    }

    @PostMapping("/uploadFileToS3WithTimeLimit")
    public McpToolResponse<String> uploadFileToS3WithTimeLimit(@RequestParam("file") MultipartFile file,@RequestParam("useTime") Long useTime) {
        McpToolResponse<String> stringMcpToolResponse = defaultToolService.uploadFileToS3AndGetFileUrlWithTimeLimit(file,useTime);
        return stringMcpToolResponse;
    }

    @GetMapping("/sendDXMessage")
    public McpServerResponse sendDXMessage(@RequestParam("message") String message, @RequestParam("misId") String misId) {
        boolean b = dxService.sendMsg2Users(message, Lists.newArrayList(misId),null);
        if(b){
            return McpServerResponse.success();
        }
        return McpServerResponse.fail();
    }

    @PostMapping("/fetchSubDocLinkAndContentBasedOnParentDocId")
    public McpToolResponse<List<Map<String, Object>>> fetchSubDocLinkAndContentBasedOnParentDocId(@RequestBody XueChengRequest xueChengRequest) {
        McpToolResponse<List<Map<String, Object>>> listMcpToolResponse = defaultToolService.fetchSubDocLinkAndContentBasedOnParentDocId(xueChengRequest);
        return listMcpToolResponse;
    }
    @PostMapping("/textVectorQuery")
    public McpServerResponse textVectorQuery(@RequestBody DsTextVectorQueryRequest dsTextVectorQueryRequest) {
        String s = defaultToolService.textVectorQuery(dsTextVectorQueryRequest);
        return McpServerResponse.success("成功", s);
    }

    @PostMapping("/textVectorDelete")
    public McpServerResponse textVectorDelete(@RequestBody DsTextVectorWriteRequest request) {
        McpServerResponse mcpServerResponse = defaultToolService.textVectorDelete(request);
        return mcpServerResponse;
    }

    @PostMapping("/textVectorBuild")
    public McpServerResponse textVectorBuild(@RequestBody DsTextVectorWriteRequest request) {
        String s = defaultToolService.textVectorBuild(request);
        return McpServerResponse.success("成功", s);
    }

    @PostMapping("/keywordQuery")
    public McpServerResponse keywordQuery(@RequestBody DsKeywordQueryRequest request) {
        String s = defaultToolService.keywordQuery(request);
        return McpServerResponse.success("成功", s);
    }
    @GetMapping("/getTestToolMissionResult")
    public McpToolResponse getFridayMissionResult(@RequestParam("missionId") String missionId) {
        McpToolResponse mcpToolResponse = matchTool.queryMatchingTestToolTaskResult(missionId);
        return mcpToolResponse;
    }

    @PostMapping("/scalarQuery")
    public McpServerResponse scalarQuery(@RequestBody DsScalarQueryRequest request) {
        String s = defaultToolService.scalarQuery(request);
        return McpServerResponse.success("成功", s);
    }


}
