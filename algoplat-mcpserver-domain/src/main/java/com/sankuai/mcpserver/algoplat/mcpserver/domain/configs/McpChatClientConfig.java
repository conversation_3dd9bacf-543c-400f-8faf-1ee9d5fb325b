package com.sankuai.mcpserver.algoplat.mcpserver.domain.configs;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.LionConfig;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/1
 */
@Configuration
public class McpChatClientConfig {

    private String baseUrl = "https://aigc.sankuai.com/v1/openai/native";

    // 添加 completions-path 配置
    private String completionsPath = "/chat/completions";

    /**
     * 创建 ChatClient.Builder Bean
     */
    @Bean("chatClientBuilderBean")
    @Primary
    public ChatClient.Builder chatClientBuilder() {
        OpenAiChatOptions options = OpenAiChatOptions.builder()
                .model(LionConfig.modelName)
                .temperature(0.7)
                .build();
        OpenAiApi openAiApi = OpenAiApi.builder().apiKey(LionConfig.llmApiKey).completionsPath(completionsPath).baseUrl(baseUrl).build();
        ChatModel openAiChatModel = new OpenAiChatModel(openAiApi, options);
        return ChatClient.builder(openAiChatModel);
    }


    @Bean("paramsBuilderBean")
    @Primary
    public ChatClient.Builder paramsBuilderBean() {
        OpenAiChatOptions options = OpenAiChatOptions.builder()
                .model(LionConfig.modelName)
                .temperature(0.7)
                .build();
        OpenAiApi openAiApi = OpenAiApi.builder().apiKey(LionConfig.llmApiKey).completionsPath(completionsPath).baseUrl(baseUrl).build();
        ChatModel openAiChatModel = new OpenAiChatModel(openAiApi, options);
        return ChatClient.builder(openAiChatModel);
    }

    @Bean("toolExceptionAnalysisBuilderBean")
    public ChatClient.Builder toolExceptionAnalysisBuilderBean() {
        OpenAiChatOptions options = OpenAiChatOptions.builder()
                .model(LionConfig.modelName)
                .temperature(0.7)
                .build();
        OpenAiApi openAiApi = OpenAiApi.builder().apiKey(LionConfig.llmApiKey).completionsPath(completionsPath).baseUrl(baseUrl).build();
        ChatModel openAiChatModel = new OpenAiChatModel(openAiApi, options);
        return ChatClient.builder(openAiChatModel);
    }

}
