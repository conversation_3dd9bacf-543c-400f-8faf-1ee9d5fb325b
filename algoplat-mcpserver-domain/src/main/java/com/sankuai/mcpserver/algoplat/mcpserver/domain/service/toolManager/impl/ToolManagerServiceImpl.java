package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.llm.ChatClientFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.mcpserver.CustomMcpServerConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.ToolRegister;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.ToolRegisterFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DefaultToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.McpBeanRecordService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.tool.MatchTool;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.parser.ParserToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.ToolManagerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.BeanNameManager;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.ToolAnnotationCollector;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.InterfaceInfoParseUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.TypeUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.HttpToolInfoPo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.PigeonToolInfoPo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ThriftToolInfoPo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.StatusEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import io.modelcontextprotocol.server.McpServerFeatures;
import io.modelcontextprotocol.server.McpSyncServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/7
 */

@Slf4j
@Service
public class ToolManagerServiceImpl implements ToolManagerService {
    @Resource
    private PigeonToolInfoDao pigeonToolInfoDao;

    @Resource
    private HttpToolInfoDao httpToolInfoDao;

    @Resource
    private ThriftToolInfoDao thriftToolInfoDao;

    @Resource
    private ToolsInfoDao toolsInfoDao;

    @Resource
    private McpServerDao mcpServerDao;

    @Resource
    private BusinessLineDao businessLineDao;

    @Resource
    @Lazy
    private ToolRegisterFactory toolRegisterFactory;

    @Resource
    private InterfaceInfoParseUtil interfaceInfoParseUtil;

    @Resource
    private CustomMcpServerConfig customMcpServerConfig;

    @Autowired
    private ApplicationContext applicationContext;

    private ChatClientFactory chatClientFactory;

    @Autowired
    private McpServerService mcpServerService;

    @Resource
    private ToolAnnotationCollector toolAnnotationCollector;


    @Resource
    private McpBeanRecordService mcpBeanRecordService;


    private static Gson GSON = new Gson();

    @Override
    public Long registerTool(Long serverId, Map<String, Object> toolInfoMap) throws Exception {
        log.info("registerTool - 接收到的工具信息: {},textType字段值: {}", JSONObject.toJSONString(toolInfoMap), toolInfoMap.get("textType"));
        ToolTypeEnum toolTypeEnum = ToolTypeEnum.valueOf(toolInfoMap.get("type").toString());
        ToolInfo toolInfo;
        if (toolTypeEnum == ToolTypeEnum.HTTP) {
            toolInfo = JacksonUtil.toBeanWithNullDefault(JacksonUtil.toJsonStrWithEmptyDefault(toolInfoMap), HttpToolInfo.class);
        } else if (toolTypeEnum == ToolTypeEnum.PIGEON) {
            toolInfo = JacksonUtil.toBeanWithNullDefault(JacksonUtil.toJsonStrWithEmptyDefault(toolInfoMap), PigeonToolInfo.class);
        } else {
            toolInfo = JacksonUtil.toBeanWithNullDefault(JacksonUtil.toJsonStrWithEmptyDefault(toolInfoMap), ThriftToolInfo.class);
        }

        log.info("registerTool - 转换后的工具信息textType: {},转换后的工具信息description: {}", toolInfo.getTextType(), toolInfo.getDescription());
        if (toolInfo == null || checkToolisExist(serverId, toolInfo.getName())) {
            log.info("registerTool - 工具信息有误或工具名称已经存在！");
            throw new Exception("工具信息有误或工具名称已经存在！");
        }
        if (Objects.isNull(toolInfo.getToolVersion())) {
            toolInfo.setToolVersion(1);
        }
        // 校验工具信息
        String validateResult = validateRegisterToolInfo(serverId, toolInfo);
        if (!"success".equals(validateResult)) {
            log.info("registerTool - 工具信息校验失败！:{}", validateResult);
            throw new Exception(validateResult);
        }
        if (toolInfo.getMcpServerId() == null) {
            toolInfoMap.put("mcpServerId", serverId);
            toolInfo.setMcpServerId(serverId);
        }

        // 注册工具
        ToolRegister toolRegister = toolRegisterFactory.getToolRegister(ToolTypeEnum.valueOf(toolInfo.getType()));
        if (Objects.isNull(toolRegister)) {
            log.info("registerTool - 工具类型不支持！");
            throw new Exception("工具类型不支持！");
        }
        ToolRegisterContext context = new ToolRegisterContext();
        context.setToolInfoMap(toolInfoMap);
        context.setHasRegistered(checkToolisExist(serverId, toolInfo.getName()));
        context.setToolInfo(toolInfo);
        // 更新工具信息
        ToolInfo toolInfoNew = updateToolInfoCacheAndDataBase(context);
        if (Objects.isNull(toolInfoNew)) {
            log.error("registerTool - 工具注册失败！");
            throw new Exception("工具注册失败！");
        }
        toolRegister.registerTool(context,toolInfoNew.getId());
        return toolInfoNew.getId();
    }

    @Override
    public Long updateTool(Long serverId, Map<String, Object> toolInfoMap) {
        try {
            log.info("updateTool, toolInfoMap: {}", JacksonUtil.toJsonStr(toolInfoMap));
            if (toolInfoMap.isEmpty() || Objects.isNull(toolInfoMap.get("name"))) {
                return null;
            }
            Long oriId = Long.valueOf(toolInfoMap.get("id").toString());
            ToolInfo oriToolInfo = toolsInfoDao.getToolInfoByToolId(oriId);

            if (oriToolInfo != null) {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> mapInfo = (Map<String, Object>) mapper.convertValue(oriToolInfo, Map.class);
                unregisterTool(mapInfo);
            }
            Long newToolId = registerTool(serverId, toolInfoMap);
            return newToolId;
        } catch (Exception e) {
            log.error("updateTool failed, toolInfoMap: {}", toolInfoMap, e);
            return null;
        }
    }

    @Override
    public McpServerResponse listToolsByType(Long mcpServerId, ToolTypeEnum toolType) {
        try {
            if (mcpServerId == null || Objects.isNull(toolType)) {
                return McpServerResponse.fail("工具类型和McpServerId不能为空");
            }
            List<ToolInfo> allToolsInServer = getAllToolsInServer(mcpServerId);
            List<ToolInfo> result = new ArrayList<>();
            if (CollectionUtils.isEmpty(allToolsInServer)) {
                allToolsInServer = new ArrayList<>();
            }
            for (ToolInfo toolInfo : allToolsInServer) {
                if (toolInfo.getType().equals(toolType)) {
                    result.add(toolInfo);
                }
            }
            return McpServerResponse.success("成功", JSONObject.toJSONString(result));
        } catch (Exception e) {

            log.error("listToolsByType failed, toolType: {}", toolType, e);
            return McpServerResponse.fail("获取工具列表失败");
        }
    }

    @Override
    public McpServerResponse getToolInfo(Long mcpServerId, String toolName) {
        if (mcpServerId == null || StringUtils.isEmpty(toolName)) {
            return McpServerResponse.fail("工具名称和McpServerId不能为空");
        }
        try {
            List<ToolInfo> allToolsInServer = getAllToolsInServer(mcpServerId);
            if (CollectionUtils.isEmpty(allToolsInServer)) {
                return McpServerResponse.fail("未找到工具");
            }
            for (ToolInfo toolInfo : allToolsInServer) {
                if (toolName.equals(toolInfo.getName())) {
                    return McpServerResponse.success("成功", JSONObject.toJSONString(toolInfo));
                }
            }
            return McpServerResponse.success("未找到工具");
        } catch (Exception e) {

            log.error("getToolInfo failed, toolName: {}", toolName, e);
            return McpServerResponse.fail("获取工具信息失败");
        }
    }

    @Override
    public String getLLMModelContext(Map<String, Object> toolInfoMap) {
        String llmResponse = "";
        if (!MapUtils.isEmpty(toolInfoMap)) {
            ChatClientFactory chatClientFactory = getChatClientFactory();
            ChatClient defaultClient = chatClientFactory.getDefaultClient();
            llmResponse = defaultClient.prompt(JSONObject.toJSONString(toolInfoMap)).call().content();
        }

        return llmResponse;
    }

    @Override
    public McpServerResponse getLLMModelContext(String interfaceName, String methodName, ToolTypeEnum toolTypeEnum) {
        if (toolTypeEnum == null || toolTypeEnum == ToolTypeEnum.HTTP) {
            return null;
        }
        try {
            Map<String, Object> interfaceInfoContext = interfaceInfoParseUtil.getInterfaceInfoContext(interfaceName, methodName);
            ChatClientFactory chatClientFactory = getChatClientFactory();
            ChatClient defaultClient = chatClientFactory.getDefaultClient();
            Object mergeMapInterfaceInfo = interfaceInfoContext.get("mergeMapInterfaceInfo");
            String llmResponse = "";
            if (mergeMapInterfaceInfo != null) {
                llmResponse = defaultClient.prompt(interfaceInfoContext.toString()).call().content();
            }
            List<Map<String, Object>> requestParamsList = new ArrayList<>();
            Type typeListMap = new TypeToken<List<Map<String, Object>>>() {
            }.getType();
            Map<String, Object> methodBaseInfo = (Map<String, Object>) interfaceInfoContext.get("methodBaseInfo");
            if (methodBaseInfo.get("requestParams") != null) {
                requestParamsList = JSONObject.parseObject(methodBaseInfo.get("requestParams").toString(), typeListMap);
            }
            Map<String, Object> annotationParseResult = (Map<String, Object>) interfaceInfoContext.get("annotationParseResult");
            // 方法和参数信息
            Object methodAnnDocListObj = annotationParseResult.get("methodAnnDocList");
            Map<String, Object> annMethodAnnDoc = new HashMap<>();

            List<Map<String, Object>> annRequestParamsList = new ArrayList<>();
            if (methodAnnDocListObj != null && !"[]".equals(methodAnnDocListObj.toString())) {
                List<Map<String, Object>> methodAnnDocList = new ArrayList<>();
                if (methodAnnDocListObj != null) {
                    methodAnnDocList = JSONObject.parseObject(String.valueOf(methodAnnDocListObj), typeListMap);
                }
                annMethodAnnDoc = methodAnnDocList.get(0);
                if (!MapUtils.isEmpty(annMethodAnnDoc)) {
                    Map<String, Object> methodDoc = (Map<String, Object>) annMethodAnnDoc.get("methodDoc");
                    if (!MapUtils.isEmpty(methodDoc)) {
                        String parametersJSON = (String) methodDoc.get("parameters");
                        if (parametersJSON != null) {
                            annRequestParamsList = JSONObject.parseObject(parametersJSON, typeListMap);
                        }
                    }
                }
            }
            Map<String, String> nameAndDescription = new HashMap<>();
            if (!CollectionUtils.isEmpty(annRequestParamsList)) {
                nameAndDescription = annRequestParamsList.stream()
                        .collect(Collectors.toMap(
                                param -> param.get("name").toString(),  // Key 提取器
                                param -> param.get("description").toString(),  // Value 提取器
                                (existing, replacement) -> existing  // 重复键处理策略（保留已有值）
                        ));
            }


            if (toolTypeEnum == ToolTypeEnum.THRIFT) {
                ThriftToolInfo thriftToolInfo = new ThriftToolInfo();
                thriftToolInfo.setDescription(llmResponse);
                thriftToolInfo.setMethodName(methodName);
                thriftToolInfo.setName(methodName);
                thriftToolInfo.setToolParams(buildToolParameters(requestParamsList, nameAndDescription));
                return McpServerResponse.success("成功", JSONObject.toJSONString(thriftToolInfo));
            } else if (toolTypeEnum == ToolTypeEnum.PIGEON) {
                PigeonToolInfo pigeonToolInfo = new PigeonToolInfo();
                pigeonToolInfo.setDescription(llmResponse);
                pigeonToolInfo.setMethodName(methodName);
                pigeonToolInfo.setName(methodName);
                pigeonToolInfo.setToolParams(buildToolParameters(requestParamsList, nameAndDescription));
                return McpServerResponse.success("成功", JSONObject.toJSONString(pigeonToolInfo));
            } else {
                log.info("interfaceName:{},methodName:{},toolTypeEnum:{},type is not supported", interfaceName, methodName, toolTypeEnum);
                return McpServerResponse.fail("type is not supported");
            }
        } catch (Exception e) {
            log.error("getLLMModelContext error, interfaceName:{}, methodName:{}, toolTypeEnum:{}", interfaceName, methodName, toolTypeEnum, e);
            return McpServerResponse.fail("getLLMModelContext error");
        }
    }

    private List<ToolParameter> buildToolParameters(List<Map<String, Object>> requestParamsList, Map<String, String> nameAndDescription) {
        List<ToolParameter> parameters = new ArrayList<>();
        requestParamsList.forEach(param -> {
            ToolParameter toolParameter = new ToolParameter();
            String parameterName = (String) param.get("parameterName");
            String annDesc = nameAndDescription.get(parameterName);
            String paramDescription = annDesc == null ? genParmDesc(String.valueOf(param.get("paramDescription"))) : annDesc;
            String parameterTypeName = param.get("parameterTypeName") == null ? null : String.valueOf(param.get("parameterTypeName"));
            String parameterSimpleTypeName = TypeUtil.convertTypeName(parameterTypeName);
            toolParameter.setName(parameterName);
            toolParameter.setDescription(paramDescription);
            toolParameter.setType(parameterSimpleTypeName);
            parameters.add(toolParameter);
        });
        return parameters;
    }

    private String genParmDesc(String interfaceInfoContext) {
        try {
            log.info("genParmDesc interfaceInfoContext:{}", interfaceInfoContext);
            ChatClientFactory chatClientFactory = getChatClientFactory();
            ChatClient paramsClient = chatClientFactory.getParamsClient();
            String content = paramsClient.prompt(interfaceInfoContext).call().content();
            log.info("genParmDesc content:{}", content);
            return content;
        } catch (Exception e) {

            return null;
        }
    }

    /**
     * 懒加载获取 ChatClientFactory
     */
    public ChatClientFactory getChatClientFactory() {
        if (chatClientFactory == null) {
            try {
                chatClientFactory = applicationContext.getBean(ChatClientFactory.class);
            } catch (Exception e) {
                // 如果获取失败，返回 null
                log.error("getChatClientFactory error", e);
                return null;
            }
        }
        return chatClientFactory;
    }

    @Transactional
    public ToolInfo updateToolInfoCacheAndDataBase(ToolRegisterContext context) {
        ToolInfo toolInfoback = null;
        try {
            ToolInfo toolInfo = context.getToolInfo();
            toolInfo.setId(null);
            if (StringUtils.isBlank(toolInfo.getToolUuid())) {
                toolInfo.setToolUuid(UUID.randomUUID().toString());
            }
            if (context.isHasRegistered()) {
                toolInfo.setToolVersion(toolInfo.getToolVersion() + 1);
            }
            String type = toolInfo.getType();
            if (ToolTypeEnum.valueOf(type) == ToolTypeEnum.HTTP || toolInfo instanceof HttpToolInfo) {
                HttpToolInfoPo httpToolInfoPo = new HttpToolInfoPo();
                BeanUtils.copyProperties(toolInfo, httpToolInfoPo);
                httpToolInfoPo.setStatus(1);
                httpToolInfoPo.setNote(toolInfo.getDescription());
                Long httpToolInfoId = httpToolInfoDao.insert(httpToolInfoPo);
                Long infoId = insertToolInfo(toolInfo, ToolTypeEnum.HTTP, httpToolInfoId);
                ToolInfo toolInfoByToolId = toolsInfoDao.getToolInfoByToolId(infoId);
                HttpToolInfo httpToolInfoByToolId = httpToolInfoDao.getHttpToolInfoByToolId(httpToolInfoId);
                BeanUtils.copyProperties(toolInfoByToolId, httpToolInfoByToolId);
                toolInfoback = httpToolInfoByToolId;
            } else if (ToolTypeEnum.valueOf(type) == ToolTypeEnum.THRIFT || toolInfo instanceof ThriftToolInfo) {
                ThriftToolInfo thriftToolInfo = (ThriftToolInfo) toolInfo;
                ThriftToolInfoPo thriftToolInfoPo = new ThriftToolInfoPo();
                thriftToolInfoPo.setMethodName(thriftToolInfo.getMethodName());
                thriftToolInfoPo.setIp(thriftToolInfo.getIp());
                thriftToolInfoPo.setPort(thriftToolInfo.getPort());
                thriftToolInfoPo.setCell(thriftToolInfo.getCell());
                thriftToolInfoPo.setInterfaceName(thriftToolInfo.getInterfaceName());
                thriftToolInfoPo.setNote(thriftToolInfo.getName());
                thriftToolInfoPo.setAppKey(thriftToolInfo.getAppKey());
                thriftToolInfoPo.setStatus(1);
                Long thriftToolInfoPoId = thriftToolInfoDao.insert(thriftToolInfoPo);
                Long infoId = insertToolInfo(toolInfo, ToolTypeEnum.THRIFT, thriftToolInfoPoId);
                ToolInfo toolInfoByToolId = toolsInfoDao.getToolInfoByToolId(infoId);
                ThriftToolInfo thriftToolInfoByToolId = thriftToolInfoDao.getThriftToolInfoByToolId(thriftToolInfoPoId);
                BeanUtils.copyProperties(toolInfoByToolId, thriftToolInfoByToolId);
                toolInfoback = thriftToolInfoByToolId;
            } else if (ToolTypeEnum.valueOf(type) == ToolTypeEnum.PIGEON || toolInfo instanceof PigeonToolInfo) {
                PigeonToolInfo pigeonToolInfo = (PigeonToolInfo) toolInfo;
                PigeonToolInfoPo pigeonToolInfoPo = new PigeonToolInfoPo();
                pigeonToolInfoPo.setMethodName(pigeonToolInfo.getMethodName());
                pigeonToolInfoPo.setNote(pigeonToolInfo.getName());
                pigeonToolInfoPo.setAppKey(pigeonToolInfo.getAppKey());
                pigeonToolInfoPo.setInterfaceName(pigeonToolInfo.getInterfaceName());
                pigeonToolInfoPo.setCell(pigeonToolInfo.getCell());
                pigeonToolInfoPo.setNote(pigeonToolInfo.getName());
                pigeonToolInfoPo.setStatus(1);
                Long pigeonToolInfoPoId = pigeonToolInfoDao.insert(pigeonToolInfoPo);
                Long infoId = insertToolInfo(toolInfo, ToolTypeEnum.PIGEON, pigeonToolInfoPoId);
                ToolInfo toolInfoByToolId = toolsInfoDao.getToolInfoByToolId(infoId);
                PigeonToolInfo pigeonToolInfoPoByToolId = pigeonToolInfoDao.getPigeonToolInfoPoByToolId(pigeonToolInfoPoId);
                BeanUtils.copyProperties(toolInfoByToolId, pigeonToolInfoPoByToolId);
                toolInfoback = pigeonToolInfoPoByToolId;
            }
            return toolInfoback;
        } catch (Exception e) {

            log.error("updateToolInfoCacheAndDataBase error,context:{}", JSONObject.toJSON(context), e);
            throw new RuntimeException(e);
        }
    }

    private Long insertToolInfo(ToolInfo toolInfo, ToolTypeEnum toolTypeEnum, Long toolId) {
        if (toolId == null) {
            log.error("insertToolInfo failed,toolId is null,toolInfo:{}", JSONObject.toJSON(toolInfo));
            return null;
        }
        toolInfo.setStatus(1);
        if (toolInfo.getToolVersion() == null) {
            toolInfo.setToolVersion(1);
        } else {
            toolInfo.setToolVersion(toolInfo.getToolVersion() + 1);
        }
        toolInfo.setToolUuid(UUID.randomUUID().toString());
        toolInfo.setId(null);
        toolInfo.setUpdateTime(new Date());
        toolInfo.setAddTime(new Date());
        log.info("insertToolInfo - 插入前的工具信息textType: {},插入前的工具信息description: {}", toolInfo.getTextType(), toolInfo.getDescription());
        if (ToolTypeEnum.HTTP.getValue().equals(toolTypeEnum.getValue())) {
            toolInfo.setHttpToolId(toolId);
        } else if (ToolTypeEnum.THRIFT.getValue().equals(toolTypeEnum.getValue())) {
            toolInfo.setThriftToolId(toolId);
        } else if (ToolTypeEnum.PIGEON.getValue().equals(toolTypeEnum.getValue())) {
            toolInfo.setPigeonToolId(toolId);
        } else {
            log.error("insertToolInfo failed,toolId is null,toolInfo:{}", JSONObject.toJSON(toolInfo));
            throw new IllegalArgumentException("Invalid tool type");
        }
        Long insertId = toolsInfoDao.insert(toolInfo);
        log.info("insertToolInfo - 插入后返回的ID: {}", insertId);
        return insertId;
    }

    @Override
    public Long unregisterTool(Map<String, Object> toolInfoMap) throws Exception {
        ToolInfo toolInfo = JacksonUtil.toBeanWithNullDefault(JacksonUtil.toJsonStrWithEmptyDefault(toolInfoMap), ToolInfo.class);
        String validateResult = validateUnregisterToolInfo(toolInfo);
        if (!"success".equals(validateResult)) {
            throw new Exception(validateResult);
        }
        return unregisterTool(toolInfo);
    }

    @Override
    public void deleteToolWithService(Map<String, Object> toolInfoMap) {
        ToolInfo toolInfo = JacksonUtil.toBeanWithNullDefault(JacksonUtil.toJsonStrWithEmptyDefault(toolInfoMap), ToolInfo.class);
        if (ToolTypeEnum.SystemDefault.getValue().equals(toolInfo.getType())) {
            mcpServerService.deleteSystemDefaultTool(toolInfo.getMcpServerId(), toolInfo.getName());
            String mcpServerBeanName = BeanNameManager.getMcpServerBeanNameByToolInfo(toolInfo);
            ToolRegister toolRegister = ToolRegisterFactory.toolRegisterMap.get(toolInfo.getType());
            toolRegister.doUnregister(mcpServerBeanName, toolInfo.getName());
            mcpBeanRecordService.updateAliveMcpRecordForDefaultTool(toolInfo.getMcpServerId(), toolInfo.getName());
            return;
        }
        unregisterTool(toolInfo);
    }

    @Override
    public Long unregisterTool(ToolInfo toolInfo) {
        if (StringUtils.isBlank(toolInfo.getName()) || !checkToolisExist(toolInfo.getMcpServerId(), toolInfo.getName())) {
            return null;
        }
        ToolRegister toolRegister = toolRegisterFactory.getToolRegister(ToolTypeEnum.valueOf(toolInfo.getType()));
        if (Objects.isNull(toolRegister)) {
            throw new IllegalArgumentException("Tool type '" + toolInfo.getType() + "' is not supported");
        }
        return toolRegister.unregisterTool(toolInfo);
    }

    @Override
    public Boolean deleteToolByMcpServerEntity(McpServerEntity mcpServerEntity) {
        if (Objects.isNull(mcpServerEntity) || mcpServerEntity.getId() == null) {
            return false;
        }
        try {
            List<ToolInfo> toolInfoByMcpServerId = getAllToolListByMcpServerId(mcpServerEntity);
            for (ToolInfo toolInfo : toolInfoByMcpServerId) {
                unregisterTool(toolInfo);
            }
            return true;
        } catch (Exception e) {
            log.error("deleteToolByMcpServerEntity error,mcpServerEntity:{}", JacksonUtil.toJsonStrWithEmptyDefault(mcpServerEntity), e);
            return false;
        }
    }

    private List<ToolInfo> getAllToolListByMcpServerId(McpServerEntity mcpServerEntity) {
        List<ToolInfo> toolInfoByMcpServerId = toolsInfoDao.getToolInfoByMcpServerId(mcpServerEntity.getId(), StatusEnum.ACTIVE);
        List<String> defaultToolNameList = mcpServerEntity.getDefaultToolNameList();
        for (String defaultTool : defaultToolNameList) {
            ToolInfo toolInfo = new ToolInfo();
            toolInfo.setName(defaultTool);
            toolInfo.setType(ToolTypeEnum.SystemDefault.getValue());
            toolInfo.setMcpServerId(mcpServerEntity.getId());
            toolInfoByMcpServerId.add(toolInfo);
        }
        return toolInfoByMcpServerId;
    }

    private String validateUnregisterToolInfo(ToolInfo toolInfo) {
        if (Objects.isNull(toolInfo)) {
            return "Invalid tool info";
        }
        if (Objects.isNull(toolInfo.getType())) {
            return "Invalid tool type";
        }
        if (StringUtils.isBlank(toolInfo.getName())) {
            return "Invalid tool name";
        }

        if (toolInfo.getMcpServerId() == null) {
            return "NO tool McpServerId";
        }
        return "success";
    }


    private String validateRegisterToolInfo(Long serverId, ToolInfo toolInfo) {
        if (Objects.isNull(toolInfo)) {
            return "Invalid tool info";
        }
        if (Objects.isNull(toolInfo.getType())) {
            return "Invalid tool type";
        }
        if (StringUtils.isBlank(toolInfo.getName())) {
            return "Invalid tool name";
        }
        if (StringUtils.isBlank(toolInfo.getDescription())) {
            return "Invalid tool description";
        }
        if (StringUtils.isBlank(toolInfo.getOwner())) {
            return "Invalid tool owner";
        }
        if (serverId == null && toolInfo.getMcpServerId() == null) {
            return "No tool McpServerId";
        }
        return "success";
    }

    @Override
    public List<String> getUsedToolsNameInServer(Long mcpServerId) {
        if(mcpServerId==null){
            log.error("getUsedToolsInServer failed,mcpServerId is null");
            return null;
        }
        try{
            McpServerEntity mcpServerById = mcpServerDao.selectMcpServerById(mcpServerId);
            if (mcpServerById == null) {
                return null;
            }
            Long businessLineId = mcpServerById.getBusinessLineId();

            BusinessLine businessLineById = businessLineDao.getBusinessLineById(businessLineId);
            if(businessLineById==null){
                return null;
            }

            McpSyncServer server = applicationContext.getBean(BeanNameManager.genMcpServerBeanName(businessLineById.getBusinessLine(), mcpServerById.getMcpServerName()), McpSyncServer.class);
            if(server==null){
                log.error("getUsedToolsInServer failed,bean is null");
                return null;
            }
            List<String> toolsUsingReflection = getToolsUsingReflection(server);
            return toolsUsingReflection;
        } catch (Exception e) {
            log.error("getUsedToolsInServer error,mcpServerId:{}", mcpServerId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 使用反射获取McpSyncServer中的工具列表
     */
    @SuppressWarnings("unchecked")
    private static List<String> getToolsUsingReflection(McpSyncServer syncServer) throws Exception {
        // 获取异步服务器实例
        Field delegateField = syncServer.getClass().getDeclaredField("asyncServer");
        delegateField.setAccessible(true);
        Object asyncServer = delegateField.get(syncServer);

        // 获取AsyncServerImpl实例
        Field delegateImplField = asyncServer.getClass().getDeclaredField("delegate");
        delegateImplField.setAccessible(true);
        Object asyncServerImpl = delegateImplField.get(asyncServer);

        // 获取tools字段
        Field toolsField = asyncServerImpl.getClass().getDeclaredField("tools");
        toolsField.setAccessible(true);
        CopyOnWriteArrayList<McpServerFeatures.AsyncToolSpecification> toolSpecs =
                (CopyOnWriteArrayList<McpServerFeatures.AsyncToolSpecification>) toolsField.get(asyncServerImpl);

        // 提取工具信息
        List<String> tools = new ArrayList<>();
        for (McpServerFeatures.AsyncToolSpecification spec : toolSpecs) {
            tools.add(spec.tool().name());
        }

        return tools;
    }

    @Override
    public List<ToolInfo> getAllToolsInServer(Long mcpServerId) {
        // 获取所有ToolCallbackProvider类型的bean
        try {
            McpServerEntity mcpServerById = mcpServerDao.selectMcpServerById(mcpServerId);
            if (mcpServerById == null) {
                return null;
            }
            Long businessLineId = mcpServerById.getBusinessLineId();
            BusinessLine businessLineById = businessLineDao.getBusinessLineById(businessLineId);
            if (businessLineById == null) {
                return null;
            }
            List<ToolInfo> result = new ArrayList<>();
            // 添加Parser工具
            if ("ParserDefaultServer".equals(mcpServerById.getMcpServerName())) {
                List<FunctionCallback> parserToolServiceFunctionCallback = toolAnnotationCollector.getAtToolFunctionCallbacks(ParserToolService.class);
                List<ToolInfo> annToolInfos = convertFuncationCallBackToTool(parserToolServiceFunctionCallback, mcpServerId, ToolTypeEnum.ToolAnnotation);
                result.addAll(annToolInfos);
            }
            if ("MatcherDefaultServer".equals(mcpServerById.getMcpServerName())) {
                List<FunctionCallback> matchToolToolServiceFunctionCallback = toolAnnotationCollector.getAtToolFunctionCallbacks(MatchTool.class);
                List<ToolInfo> annToolInfos = convertFuncationCallBackToTool(matchToolToolServiceFunctionCallback, mcpServerId, ToolTypeEnum.ToolAnnotation);
                result.addAll(annToolInfos);
            }
            // 添加默认工具
            List<FunctionCallback> defaultToolFunctionCallbacks = toolAnnotationCollector.getAtToolFunctionCallbacks(DefaultToolService.class);
            List<String> defaultToolNameList = mcpServerById.getDefaultToolNameList();

            List<FunctionCallback> defaultFuncall = new ArrayList<>();
            if (!CollectionUtils.isEmpty(defaultToolNameList)) {
                for (FunctionCallback defaultToolFunctionCallback : defaultToolFunctionCallbacks) {
                    String name = defaultToolFunctionCallback.getName();
                    if (defaultToolNameList.contains(name)) {
                        defaultFuncall.add(defaultToolFunctionCallback);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(defaultFuncall)) {
                List<ToolInfo> defaultToolInfos = convertFuncationCallBackToTool(defaultFuncall, mcpServerId, ToolTypeEnum.SystemDefault);
                result.addAll(defaultToolInfos);
            }
            List<ToolInfo> toolInfoByMcpServerId = toolsInfoDao.getToolInfoByMcpServerId(mcpServerId,StatusEnum.ACTIVE);
            List<ToolInfo> toolInfos = processToolInfoByType(toolInfoByMcpServerId);
            result.addAll(toolInfos);
            return result;
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            log.error("getAllToolCallbacks error", e);
            return null;
        }
    }


    @Override
    public List<ToolInfo> convertFuncationCallBackToTool(List<FunctionCallback> functionCallbackList, Long mcpServerId, ToolTypeEnum toolTypeEnum) {
        List<ToolInfo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(functionCallbackList)) {
            return result;
        }
        for (FunctionCallback functionCallback : functionCallbackList) {
            ToolInfo toolInfo = new ToolInfo();
            toolInfo.setType(toolTypeEnum.getValue());
            toolInfo.setName(functionCallback.getName());
            toolInfo.setDescription(functionCallback.getDescription());
            toolInfo.setOwner("system");
            toolInfo.setStatus(1);
            toolInfo.setMcpServerId(mcpServerId);
            result.add(toolInfo);
        }
        return result;
    }

    @Override
    public List<BusinessMcpServerInfo> loadBusinessMcpServerInfoFromDataBase() {
        List<BusinessLine> allBusinessLine = businessLineDao.getAllBusinessLine();
        if (CollectionUtils.isEmpty(allBusinessLine)) {
            allBusinessLine = new ArrayList<>();
        }
        List<BusinessMcpServerInfo> businessMcpServerInfoList = new ArrayList<>();
        for (BusinessLine businessLine : allBusinessLine) {
            BusinessMcpServerInfo businessMcpServerInfo = new BusinessMcpServerInfo();
            businessMcpServerInfo.setBusinessLine(businessLine);
            Long businessLineId = businessLine.getId();
            List<McpServerEntity> mcpServerEntities = mcpServerDao.selectMcpServerByBusinessLineId(businessLineId,StatusEnum.ACTIVE);
            for (McpServerEntity mcpServerEntity : mcpServerEntities) {
                Long McpServerId = mcpServerEntity.getId();
                List<ToolInfo> toolInfos = toolsInfoDao.getToolInfoByMcpServerId(McpServerId,StatusEnum.ACTIVE);
                mcpServerEntity.setToolInfoList(processToolInfoByType(toolInfos));
            }
            businessMcpServerInfo.setMcpServerEntityList(mcpServerEntities);
            businessMcpServerInfoList.add(businessMcpServerInfo);
        }
        return businessMcpServerInfoList;
    }

    private List<ToolInfo> processToolInfoByType(List<ToolInfo> toolInfos) {
        List<ToolInfo> toolInfoResults = new ArrayList<>();
        if (CollectionUtils.isEmpty(toolInfos)) {
            return toolInfoResults;
        }
        for (ToolInfo toolInfo : toolInfos) {
            if (toolInfo != null && toolInfo.getType().equals(ToolTypeEnum.PIGEON.getValue())) {
                Long pigeonToolId = toolInfo.getPigeonToolId();
                PigeonToolInfo pigeonToolInfo = pigeonToolInfoDao.getPigeonToolInfoPoByToolId(pigeonToolId);
                if (pigeonToolInfo == null) {
                    log.error("pigeonToolInfo is null, toolInfo:{}", GSON.toJson(toolInfo));
                    continue;
                }
                BeanUtils.copyProperties(toolInfo, pigeonToolInfo);
                toolInfoResults.add(pigeonToolInfo);
            } else if (toolInfo != null && toolInfo.getType().equals(ToolTypeEnum.THRIFT.getValue())) {
                Long thriftToolId = toolInfo.getThriftToolId();
                ThriftToolInfo thriftToolInfo = thriftToolInfoDao.getThriftToolInfoByToolId(thriftToolId);
                if (thriftToolInfo == null) {
                    log.error("thriftToolInfo is null, toolInfo:{}", GSON.toJson(toolInfo));
                    continue;
                }
                BeanUtils.copyProperties(toolInfo, thriftToolInfo);
                toolInfoResults.add(thriftToolInfo);
            } else if (toolInfo != null && toolInfo.getType().equals(ToolTypeEnum.HTTP.getValue())) {
                Long httpToolId = toolInfo.getHttpToolId();
                HttpToolInfo httpToolInfo = httpToolInfoDao.getHttpToolInfoByToolId(httpToolId);
                if (httpToolInfo == null) {
                    log.error("httpToolInfo is null, toolInfo:{}", GSON.toJson(toolInfo));
                    continue;
                }
                BeanUtils.copyProperties(toolInfo, httpToolInfo);
                toolInfoResults.add(httpToolInfo);
            }
        }
        return toolInfoResults;

    }

    @Override
    public Boolean checkToolisExist(Long serverId, String toolName) {
        if (serverId == null || Objects.isNull(toolName)) {
            return false;
        }
        List<String> allToolsInServer = getUsedToolsNameInServer(serverId);
        return !CollectionUtils.isEmpty(allToolsInServer) && allToolsInServer.contains(toolName);
    }

    public List<ToolInfo> getAllDefaultTools(Long mcpServerId) {
        List<FunctionCallback> defaultToolFunctionCallbacks = toolAnnotationCollector.getAtToolFunctionCallbacks(DefaultToolService.class);
        List<ToolInfo> toolInfos = convertFuncationCallBackToTool(defaultToolFunctionCallbacks, mcpServerId, ToolTypeEnum.SystemDefault);
        return toolInfos;

    }

    @Override
    public List<ToolInfo> processUsedDefaultTool(Long mcpServerId) {
        List<ToolInfo> allDefaultTools = getAllDefaultTools(mcpServerId);
        List<ToolInfo> allToolsInServer = getAllToolsInServer(mcpServerId);
        if (CollectionUtils.isEmpty(allToolsInServer)) {
            return allDefaultTools;
        }
        List<String> collect = allToolsInServer.stream()
                .filter(tool -> ToolTypeEnum.SystemDefault.getValue().equals(tool.getType()))
                .map(ToolInfo::getName)
                .collect(Collectors.toList());
        for (ToolInfo toolInfo : allDefaultTools) {
            if (collect.contains(toolInfo.getName())) {
                toolInfo.setUsedInDefault(true);
            } else {
                toolInfo.setUsedInDefault(false);
            }
        }
        return allDefaultTools;
    }

}