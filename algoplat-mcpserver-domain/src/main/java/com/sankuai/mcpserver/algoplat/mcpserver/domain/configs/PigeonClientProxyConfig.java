package com.sankuai.mcpserver.algoplat.mcpserver.domain.configs;

import com.dianping.pigeon.remoting.ServiceFactory;
import com.dianping.pigeon.remoting.common.domain.GenericType;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.dianping.pigeon.remoting.invoker.config.InvokerConfig;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.PigeonToolInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/26
 */
@Slf4j
@Configuration
public class PigeonClientProxyConfig {

    public static Map<String, GenericService> pigeonGenericService = Maps.newConcurrentMap();

    public GenericService createPigeonProxy(PigeonToolInfo pigeonToolInfo) {
        GenericService genericService = pigeonGenericService.get(genPigeonToolKey(pigeonToolInfo));
        if (genericService != null) {
            return genericService;
        }

        int timeout = pigeonToolInfo.getTimeOut() <= 0 ? 6000 : pigeonToolInfo.getTimeOut();
        InvokerConfig<GenericService> invokerConfig = new InvokerConfig<>(pigeonToolInfo.getInterfaceName(), GenericService.class);
        invokerConfig.setTimeout(timeout);
        invokerConfig.setGeneric(GenericType.JSON_SIMPLE.getName());
        invokerConfig.setCallType("sync");
        invokerConfig.setRemoteAppKey(pigeonToolInfo.getAppKey());
        if (StringUtils.isNotBlank(pigeonToolInfo.getCell())) {
            invokerConfig.setCell(pigeonToolInfo.getCell());
        }
        GenericService service = ServiceFactory.getService(invokerConfig);
        pigeonGenericService.put(genPigeonToolKey(pigeonToolInfo), service);

        return service;
    }

    public void destoryPigeonProxy(PigeonToolInfo pigeonToolInfo) {
        String key = genPigeonToolKey(pigeonToolInfo);
        destoryPigeonProxyByPrefix(key);
    }

    public void destoryPigeonProxyByPrefix(String preFix) {
        GenericService genericService = pigeonGenericService.get(preFix);
        if (genericService == null) {
            return;
        }
        InvokerConfig invokerConfig = (InvokerConfig) genericService;
        String url = invokerConfig.getUrl();
        ServiceFactory.removeService(url);
        pigeonGenericService.remove(preFix);
    }

    @PreDestroy
    public void destroy() {
        for (Map.Entry<String, GenericService> stringGenericServiceEntry : pigeonGenericService.entrySet()) {
            GenericService genericService = stringGenericServiceEntry.getValue();
            InvokerConfig invokerConfig = (InvokerConfig) genericService;
            String url = invokerConfig.getUrl();
            ServiceFactory.removeService(url);
            pigeonGenericService.remove(stringGenericServiceEntry.getKey());
        }
    }

    private String genPigeonToolKey(PigeonToolInfo pigeonToolInfo) {
        return pigeonToolInfo.getAppKey() + "_" + pigeonToolInfo.getInterfaceName() + "_" + pigeonToolInfo.getCell() + "_" + pigeonToolInfo.getTimeOut();
    }

}
