package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpToolResponse<T> {

    private Integer code;

    private String message;

    private T data;

    public static <T> McpToolResponse<T> success(T data) {
        return McpToolResponse.<T>builder()
                .code(0)
                .message("成功")
                .data(data)
                .build();
    }

    public static <T> McpToolResponse<T> success() {
        return McpToolResponse.<T>builder()
                .code(0)
                .message("成功")
                .build();
    }


    public static <T> McpToolResponse<T> success(String message, T data) {
        return McpToolResponse.<T>builder()
                .code(0)
                .message(message)
                .data(data)
                .build();
    }

    public static <T> McpToolResponse<T> fail(T data) {
        return McpToolResponse.<T>builder()
                .code(-1)
                .message("服务内部异常")
                .data(data)
                .build();
    }

    public static <T> McpToolResponse<T> fail() {
        return McpToolResponse.<T>builder()
                .code(-1)
                .message("服务内部异常")
                .build();
    }

    public static <T> McpToolResponse<T> fail(String message, T data) {
        return McpToolResponse.<T>builder()
                .code(-1)
                .message(message)
                .data(data)
                .build();
    }

    public static <T> McpToolResponse<T> paramsError(String message, T data) {
        return McpToolResponse.<T>builder()
                .code(400)
                .message( message)
                .data(data)
                .build();
    }

    public static <T> McpToolResponse<T> authError(String message, T data) {
        return McpToolResponse.<T>builder()
                .code(403)
                .message(message)
                .data(data)
                .build();
    }
    public static <T> McpToolResponse<T> paramsError() {
        return McpToolResponse.<T>builder()
                .code(400)
                .message("入参格式错误")
                .build();
    }

    public static <T> McpToolResponse<T> authError() {
        return McpToolResponse.<T>builder()
                .code(403)
                .message("权限不足")
                .build();
    }
}
