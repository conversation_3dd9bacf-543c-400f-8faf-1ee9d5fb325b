package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工具注解收集器，用于收集类中被@Tool注解标记的方法及其name属性
 *
 * <AUTHOR>
 * @date 2025/4/19
 */
@Slf4j
@Component
public class ToolAnnotationCollector {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 获取指定类中所有被@Tool注解标记的方法
     *
     * @param clazz 要检查的类
     * @return 被@Tool注解标记的方法列表
     */
    public List<Method> getToolAnnotatedMethods(Class<?> clazz) {
        return Arrays.stream(clazz.getDeclaredMethods())
                .filter(method -> method.isAnnotationPresent(Tool.class))
                .collect(Collectors.toList());
    }

    /**
     * 获取指定类中所有被@Tool注解标记的方法及其name属性的映射
     *
     * @param clazz 要检查的类
     * @return 方法名到@Tool注解name属性的映射
     */
    public List<FunctionCallback> getAtToolFunctionCallbacks(Class<?> clazz) {
        Map<String, ToolCallbackProvider> providerMap = applicationContext.getBeansOfType(ToolCallbackProvider.class);
        Collection<ToolCallbackProvider> toolCallbackProviderList = providerMap.values();
        Map<String, FunctionCallback> toolCallbacksMap = new HashMap<>();
        for (ToolCallbackProvider provider : toolCallbackProviderList) {
            FunctionCallback[] toolCallbacks = provider.getToolCallbacks();
            for (FunctionCallback toolCallback : toolCallbacks) {
                toolCallbacksMap.put(toolCallback.getName(), toolCallback);
            }
        }
        List<FunctionCallback> toolCallbacks = new ArrayList<>();
        for (Method method : getToolAnnotatedMethods(clazz)) {
            Tool toolAnnotation = method.getAnnotation(Tool.class);
            String name = toolAnnotation.name();
            if (toolCallbacksMap.containsKey(name)) {
                toolCallbacks.add(toolCallbacksMap.get(name));
            }

        }
        return toolCallbacks;
    }
}