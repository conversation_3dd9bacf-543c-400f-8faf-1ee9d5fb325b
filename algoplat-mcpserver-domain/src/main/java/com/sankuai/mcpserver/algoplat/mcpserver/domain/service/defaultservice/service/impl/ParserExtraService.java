package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.grocery.dola.thrift.model.response.TBaseList;
import com.sankuai.grocery.dola.thrift.model.response.TBaseResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.McpToolResponse;
import com.sankuai.nib.data.zb.flow.platform.api.request.FlowVerifyRequestDTO;
import com.sankuai.nib.data.zb.flow.platform.api.request.FlowVersionVerifyByFileRequestDTO;
import com.sankuai.nib.data.zb.flow.platform.api.request.UploadContextRequestDTO;
import com.sankuai.nib.data.zb.flow.platform.api.response.FlowVerifyResponseDTO;
import com.sankuai.nib.data.zb.flow.platform.api.service.FlowExecutorThriftService;
import com.sankuai.nib.data.zb.parse.platform.api.service.ParseRuleValidService;
import com.sankuai.zb.metadata.api.dto.deliver.DeliverConfigTableDTO;
import com.sankuai.zb.metadata.api.dto.flow.standard.AnalysisModel;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowAnalysisDTO;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowInstanceStandardDTO;
import com.sankuai.zb.metadata.api.dto.rule.*;
import com.sankuai.zb.metadata.api.request.deliver.DeliverConfigTableListRequest;
import com.sankuai.zb.metadata.api.request.rule.ParseRuleVersionRequest;
import com.sankuai.zb.metadata.api.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/7/2
 */

@Slf4j
@Service
public class ParserExtraService {

    @Resource
    private ParseRuleThriftService parseRuleThriftService;

    @Resource
    private ParseRuleVersionThriftService parseRuleVersionThriftService;

    @Resource
    private FlowManagerThriftService flowManagerThriftService;


    @Resource
    private FlowExecutorThriftService flowExecutorThriftService;


    public TBaseResponse<Long> saveNewFlowEntity(FlowInstanceStandardDTO flowInstanceStandardDTO) {
        try {
            log.info("save_new_flow_entity,flowInstanceStandardDTO:{}", flowInstanceStandardDTO);
            TBaseResponse<Long> longTBaseResponse = flowManagerThriftService.flowInstanceQuicklySave(flowInstanceStandardDTO);
            return longTBaseResponse;
        } catch (Exception e) {
            log.error("save_new_flow_entity error,flowInstanceStandardDTO:{}", flowInstanceStandardDTO, e);
            throw e;
        }
    }

    public TBaseResponse<Long> addNewFlowEntityVersion(FlowAnalysisDTO flowAnalysisDTO) {
        try {
            log.info("add_new_flow_entity_version,flowAnalysisDTO:{}", flowAnalysisDTO);
            TBaseResponse<Long> longTBaseResponse = flowManagerThriftService.addFlowAnalysisRule(flowAnalysisDTO);
            log.info("add_new_flow_entity_version end,flowAnalysisDTO:{},outPut:{}", flowAnalysisDTO, JSONObject.toJSONString(longTBaseResponse));
            return longTBaseResponse;
        } catch (Exception e) {
            log.error("add_new_flow_entity_version error,flowAnalysisDTO:{}", flowAnalysisDTO, e);
            throw e;
        }
    }

    public TBaseResponse<Long> saveRuleEntity(CreateRuleWithVersionDTO createRuleWithVersionDTO) {
        try {
            log.info("save_rule_entity,createRuleWithVersionDTO:{}", createRuleWithVersionDTO);
            TBaseResponse<Long> longTBaseResponse = parseRuleThriftService.quickAddRuleWithVersion(createRuleWithVersionDTO);
            return longTBaseResponse;
        } catch (Exception e) {
            log.error("save_rule_entity error,createRuleWithVersionDTO:{}", createRuleWithVersionDTO, e);
            throw e;
        }
    }

    public TBaseResponse<Long> addRuleEntityVersion(ParseRuleVersionRequest parseRuleVersionRequest) {
        try {
            log.info("add_rule_entity_version,parseRuleVersionRequest:{}", parseRuleVersionRequest);
            TBaseResponse<Long> longTBaseResponse = parseRuleVersionThriftService.addParseRuleVersionAndReturn(parseRuleVersionRequest);
            return longTBaseResponse;
        } catch (Exception e) {
            log.error("add_rule_entity_version error,parseRuleVersionRequest:{}", parseRuleVersionRequest, e);
            throw e;
        }
    }


    public TBaseResponse<FlowVerifyResponseDTO> verifyFlowVersionByFile(FlowVersionVerifyByFileRequestDTO request) {
        try {
            log.info("verify_flow_version_by_file,FlowVerifyRequestDTO:{}", request);
            TBaseResponse<FlowVerifyResponseDTO> flowVerifyResponseDTOTBaseResponse = flowExecutorThriftService.verifyFlowVersionByFile(request);
            return flowVerifyResponseDTOTBaseResponse;
        } catch (Exception e) {
            log.error("verify_flow_version_by_file error,FlowVerifyRequestDTO:{}", request, e);
            throw e;

        }
    }
}
