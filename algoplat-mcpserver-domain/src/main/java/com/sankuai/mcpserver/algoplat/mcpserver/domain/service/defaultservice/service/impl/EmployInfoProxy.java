package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.dianping.cat.Cat;
import com.sankuai.meituan.org.opensdk.model.domain.Emp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.meituan.org.opensdk.service.EmpService;

@Service
public class EmployInfoProxy {
    @Autowired
    private EmpService empService;

    public Long getEmployId(String mis) {
        try {
            Emp emp = empService.queryByMis(mis, null);
            return Long.parseLong(emp.getEmpId());
        } catch (Exception e) {
            Cat.logError(e.getMessage(), e);
            throw new RuntimeException("Error fetching employee ID", e);
        }
    }
}
