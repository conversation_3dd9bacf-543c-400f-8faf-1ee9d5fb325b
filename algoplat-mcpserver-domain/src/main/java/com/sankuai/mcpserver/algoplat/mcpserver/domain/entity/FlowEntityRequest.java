package com.sankuai.mcpserver.algoplat.mcpserver.domain.entity;

import java.util.List;

public class FlowEntityRequest {
    private String flowName;
    private String interfaceCode;
    private Integer mjScheduleFlag;
    private String desc;
    private List<String> modelList;

    // Getters and Setters
    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public String getInterfaceCode() {
        return interfaceCode;
    }

    public void setInterfaceCode(String interfaceCode) {
        this.interfaceCode = interfaceCode;
    }

    public Integer getMjScheduleFlag() {
        return mjScheduleFlag;
    }

    public void setMjScheduleFlag(Integer mjScheduleFlag) {
        this.mjScheduleFlag = mjScheduleFlag;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<String> getModelList() {
        return modelList;
    }

    public void setModelList(List<String> modelList) {
        this.modelList = modelList;
    }
}