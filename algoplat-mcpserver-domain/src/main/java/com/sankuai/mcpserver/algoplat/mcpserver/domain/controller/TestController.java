package com.sankuai.mcpserver.algoplat.mcpserver.domain.controller;

import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.TestTaskScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private TestTaskScheduleService testTaskScheduleService;

    @GetMapping("/testTaskSchedule")
    public void testTaskSchedule() {
        testTaskScheduleService.testTaskSchedule();
    }
}
