package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.TemplateInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.EsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class EsServiceImpl implements EsService {

    private static final String esOpenApiUrl="http://openapi.eagle.test.sankuai.com/openapi";
    private static final String accessKey = "1905B057ACB685BE9938D3D9747892F0";
    private static final String appkey = "com.sankuai.algoplatform.mcpserver";

    /**
     * Description: 设置httpClient方式的Token鉴权header
     *
     * @param request http请求
     * @return
     */
    private void tokenAuth(HttpRequestBase request) {
        long ts = System.currentTimeMillis();
        String token = ts + "," + DigestUtils.md5Hex(ts + "::" + accessKey);
        request.addHeader("x-eagle-appkey", appkey);
        request.addHeader("x-eagle-token", token);
    }

    @Override
    public List<TemplateInfo> getClusterTemplates(String clusterName) {
        String url = String.format("%s/clusters/%s/templates", esOpenApiUrl, clusterName);

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(url);
            request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
            tokenAuth(request);

            // 执行请求并解析响应
            String response = client.execute(request, httpResponse -> {
                return org.apache.commons.io.IOUtils.toString(
                        httpResponse.getEntity().getContent(),
                        "UTF-8"
                );
            });

            // 解析响应JSON
            JSONObject jsonResponse = JSON.parseObject(response);
            if (jsonResponse.getInteger("code") != 0) {
                throw new RuntimeException("获取模板列表失败: " + jsonResponse.getString("message"));
            }

            // 转换为TemplateInfo对象列表
            List<TemplateInfo> templates = new ArrayList<>();
            jsonResponse.getJSONArray("data").forEach(item -> {
                JSONObject templateJson = (JSONObject) item;
                TemplateInfo template = new TemplateInfo();
                template.setTemplateName(templateJson.getString("templateName"));
                template.setTemplatePattern(templateJson.getString("templatePattern"));
                template.setAliases(templateJson.getJSONArray("aliases").toJavaList(String.class));
                templates.add(template);
            });

            return templates;
        } catch (Exception e) {
            log.error("Failed to get templates for cluster: {}", clusterName, e);
            throw new RuntimeException("获取ES模板列表失败", e);
        }
    }
    @Override
    public boolean createTemplate(String clusterName, String templateName, JSONObject templateContent) {
        String url = String.format("%s/clusters/%s/templates/%s", esOpenApiUrl, clusterName, templateName);

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPut request = new HttpPut(url);
            request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
            tokenAuth(request);

            StringEntity entity = new StringEntity(templateContent.toJSONString(), "UTF-8");
            request.setEntity(entity);

            String response = client.execute(request, httpResponse -> {
                int statusCode = httpResponse.getStatusLine().getStatusCode();
                String responseBody = org.apache.commons.io.IOUtils.toString(
                        httpResponse.getEntity().getContent(),
                        "UTF-8"
                );

                log.info("Create/Update template response - status:{}, body:{}", statusCode, responseBody);

                if (statusCode != 200) {
                    throw new RuntimeException(String.format("创建/更新模板失败 - status:%d, body:%s",
                            statusCode, responseBody));
                }

                return responseBody;
            });

            JSONObject jsonResponse = JSON.parseObject(response);
            if (jsonResponse.getInteger("code") != 0) {
                throw new RuntimeException("创建/更新模板失败: " + jsonResponse.getString("message"));
            }

            return jsonResponse.getJSONObject("data").getInteger("changed") == 1;
        } catch (Exception e) {
            log.error("Failed to create/update template for cluster:{}, template:{}", clusterName, templateName, e);
            throw new RuntimeException("创建/更新ES模板失败", e);
        }
    }
    @Override
    public boolean deleteTemplate(String clusterName, String templateName) {
        String url = String.format("%s/clusters/%s/templates/%s", esOpenApiUrl, clusterName, templateName);

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpDelete request = new HttpDelete(url);
            request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
            tokenAuth(request);

            String response = client.execute(request, httpResponse -> {
                int statusCode = httpResponse.getStatusLine().getStatusCode();
                String responseBody = org.apache.commons.io.IOUtils.toString(
                        httpResponse.getEntity().getContent(),
                        "UTF-8"
                );

                log.info("Delete template response - status:{}, body:{}", statusCode, responseBody);

                if (statusCode != 200) {
                    throw new RuntimeException(String.format("删除模板失败 - status:%d, body:%s",
                            statusCode, responseBody));
                }

                return responseBody;
            });

            JSONObject jsonResponse = JSON.parseObject(response);
            if (jsonResponse.getInteger("code") != 0) {
                throw new RuntimeException("删除模板失败: " + jsonResponse.getString("message"));
            }

            return jsonResponse.getJSONObject("data").getInteger("deleted") == 1;
        } catch (Exception e) {
            log.error("Failed to delete template for cluster:{}, template:{}", clusterName, templateName, e);
            throw new RuntimeException("删除ES模板失败", e);
        }
    }
    @Override
    public boolean createIndex(String clusterName, String indexName, JSONObject settings) {
        String url = String.format("%s/clusters/%s/indices/%s", esOpenApiUrl, clusterName, indexName);

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPut request = new HttpPut(url);
            request.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
            tokenAuth(request);

            StringEntity entity = new StringEntity(settings.toJSONString(), "UTF-8");
            request.setEntity(entity);

            String response = client.execute(request, httpResponse -> {
                int statusCode = httpResponse.getStatusLine().getStatusCode();
                String responseBody = org.apache.commons.io.IOUtils.toString(
                        httpResponse.getEntity().getContent(),
                        "UTF-8"
                );

                log.info("Create index response - status:{}, body:{}", statusCode, responseBody);

                if (statusCode != 200) {
                    throw new RuntimeException(String.format("创建索引失败 - status:%d, body:%s",
                            statusCode, responseBody));
                }

                return responseBody;
            });

            JSONObject jsonResponse = JSON.parseObject(response);
            if (jsonResponse.getInteger("code") != 0) {
                throw new RuntimeException("创建索引失败: " + jsonResponse.getString("message"));
            }

            return jsonResponse.getJSONObject("data").getInteger("created") == 1;
        } catch (Exception e) {
            log.error("Failed to create index for cluster:{}, index:{}, settings:{}",
                    clusterName, indexName, settings, e);
            throw new RuntimeException("创建索引失败", e);
        }
    }

}
