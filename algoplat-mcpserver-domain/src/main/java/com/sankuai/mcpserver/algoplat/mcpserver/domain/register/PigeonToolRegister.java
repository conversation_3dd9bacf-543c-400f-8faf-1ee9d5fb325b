package com.sankuai.mcpserver.algoplat.mcpserver.domain.register;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.pigeon.remoting.common.service.GenericService;
import com.dianping.zebra.util.StringUtils;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.configs.PigeonClientProxyConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.McpBeanRecordService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.RpcParameterUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.BusinessLineDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;

@Slf4j
@Service
public class PigeonToolRegister extends AbstractToolRegister {

    @Autowired
    private McpServerService mcpServerService;

    private static PigeonClientProxyConfig pigeonClientProxyConfig;  // 修改字段

    @Autowired
    private McpBeanRecordService mcpBeanRecordService;

    @Autowired
    public PigeonToolRegister(PigeonClientProxyConfig pigeonClientProxyConfig) {  // 添加构造方法
        this.pigeonClientProxyConfig = pigeonClientProxyConfig;
    }


    @Resource
    private BusinessLineDao businessLineDao;

    @Override
    public ToolCallback registerTool(ToolRegisterContext context,Long toolId) {
        PigeonToolInfo pigeonToolInfo = transform(context, PigeonToolInfo.class);
        context.setToolInfo(pigeonToolInfo);
        String toolName = pigeonToolInfo.getName();

        try {
            Long mcpServerId = pigeonToolInfo.getMcpServerId();
            McpServerEntity mcpServerById = mcpServerService.getMcpServerById(mcpServerId);
            if (Objects.isNull(mcpServerById)) {
                throw new RuntimeException("Failed to get mcpServerById");
            }
            Long businessLineId = mcpServerById.getBusinessLineId();
            BusinessLine existingLine = businessLineDao.getBusinessLineById(businessLineId);
            if (existingLine == null) {
                throw new RuntimeException("Failed to register Thrift tool: businessLine is null");
            }
            ToolCallback toolCallback = getToolCallback(context);
            registerToolForMcpServer(toolCallback, existingLine, mcpServerById);
            mcpBeanRecordService.updateAliveMcpRecord(mcpServerId, toolId);
            return toolCallback;
        } catch (Exception e) {
            log.error("Failed to register Pigeon tool: {}", toolName, e);
            throw new RuntimeException("Failed to register Pigeon tool: " + e.getMessage(), e);
        }
    }

    @Override
    public ToolTypeEnum getToolType() {
        return ToolTypeEnum.PIGEON;
    }

    @Override
    public ToolCallback getToolCallback(ToolRegisterContext context) {
        PigeonToolInfo pigeonToolInfo = transform(context, PigeonToolInfo.class);
        context.setToolInfo(pigeonToolInfo);
        String toolName = pigeonToolInfo.getName();

        try {
            if (context.isHasRegistered()) {
                unregisterTool(pigeonToolInfo);
            }
            ToolCallback toolCallback = FunctionToolCallback
                    .builder(pigeonToolInfo.getName(), new PigeonToolBiFunction(pigeonToolInfo))
                    .description(pigeonToolInfo.getDescription())
                    .inputType(Map.class)
                    .inputSchema(generateInputSchema(pigeonToolInfo))
                    .build();
            return toolCallback;
        } catch (Exception e) {

            log.error("Failed to getToolCallback Pigeon tool: {}", toolName, e);
            throw new RuntimeException("Failed to getToolCallback Pigeon tool: " + e.getMessage(), e);
        }
    }


    private record PigeonToolBiFunction(
            PigeonToolInfo pigeonToolInfo) implements BiFunction<Map<String, Object>, ToolContext, String> {
        @Override
        public String apply(Map<String, Object> toolRequest, ToolContext toolContext) {
            String result = null;
            Transaction t = Cat.newTransaction(ServiceConstants.JOB_NAME, "PigeonToolRegister");
            Pair<List<String>, List<String>> pair = null;
            try {
                GenericService pigeonProxy = pigeonClientProxyConfig.createPigeonProxy(pigeonToolInfo);
                if (Objects.isNull(pigeonProxy)) {
                    throw new RuntimeException("Failed to create Pigeon proxy");
                }
                if (pigeonToolInfo == null || toolRequest == null) {
                    t.setStatus("PigeonToolRegister param is null");
                    log.info("PigeonToolRegister param is null");
                    return null;
                }
                log.info("PigeonToolBiFunction apply begin.pigeonToolInfo:{} toolRequest:{}", JSONObject.toJSONString(pigeonToolInfo), JacksonUtil.toJsonStrWithEmptyDefault(toolRequest));
                List<ToolParameter> toolParams = pigeonToolInfo.getToolParams();
                String avitorScriptCode = pigeonToolInfo.getAvitorScriptCode();
                if (StringUtils.isNotBlank(avitorScriptCode)) {
                    Expression compile = AviatorEvaluator.compile(avitorScriptCode, true);
                    toolRequest = (Map<String, Object>) compile.execute(toolRequest);
                }
                Object jsonReplaceRule = toolRequest.get("jsonReplaceRule");
                if (jsonReplaceRule != null) {
                    toolRequest.remove("jsonReplaceRule");
                }
                pair = RpcParameterUtil.parseParamTypeAndValues(toolParams, toolRequest, jsonReplaceRule);
                result = pigeonProxy.$invoke(pigeonToolInfo.getMethodName(), pair.getLeft(), pair.getRight());
                t.setSuccessStatus();
                return result;
            } catch (Exception e) {
                t.setStatus(e);
                pigeonClientProxyConfig.destoryPigeonProxy(pigeonToolInfo);
                Cat.logError(String.format("Error,methodName:%s,ReqparmType:%s, ReqparmType:%s", pigeonToolInfo.getMethodName(), pair.getLeft(), pair.getRight()), e);
                log.error("PigeonToolBiFunction apply error,PigeonToolInfo:{},e:{}", JacksonUtil.toJsonStrWithEmptyDefault(pigeonToolInfo), e);
                return ExceptionUtils.getRootCauseMessage(e);
            } finally {
                log.info("PigeonToolBiFunction apply complete,request:{} result:{}", JSONObject.toJSONString(toolRequest), result);
                t.complete();
            }
        }

    }
}
