package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.matchtesttool;

import com.sankuai.algoplatform.matchops.api.request.mcptool.TMcpTestMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.McpMissionContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExecuteTestToolMissionContext extends McpMissionContext {

    private TMcpTestMissionRequest request;

    private Long taskId;

    private String message;

    private String sessionId;

    private String misId;

}
