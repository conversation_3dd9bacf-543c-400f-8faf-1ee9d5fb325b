package com.sankuai.mcpserver.algoplat.mcpserver.domain.register;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.victools.jsonschema.generator.Module;
import com.github.victools.jsonschema.generator.*;
import com.github.victools.jsonschema.module.jackson.JacksonModule;
import com.github.victools.jsonschema.module.jackson.JacksonOption;
import com.github.victools.jsonschema.module.swagger2.Swagger2Module;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.configs.PigeonClientProxyConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.configs.ThriftClientProxyBeanConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.McpBeanRecordService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.BeanNameManager;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.BusinessLineDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.PigeonToolInfoDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.ThriftToolInfoDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.ToolsInfoDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ParameterTypeEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import io.modelcontextprotocol.server.McpSyncServer;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.util.json.JsonParser;
import org.springframework.ai.util.json.schema.SpringAiSchemaModule;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public abstract class AbstractToolRegister implements ToolRegister, ApplicationContextAware {

    private static final boolean PROPERTY_REQUIRED_BY_DEFAULT = true;

    private static final SchemaGenerator TYPE_SCHEMA_GENERATOR;

    private static final SchemaGenerator SUBTYPE_SCHEMA_GENERATOR;

    @Autowired
    private ToolsInfoDao toolsInfoDao;

    @Resource
    @Lazy
    private McpServerService mcpServerService;

    @Resource
    private BusinessLineDao businessLineDao;

    @Resource
    private PigeonClientProxyConfig pigeonClientProxyConfig;

    @Resource
    private ThriftClientProxyBeanConfig thriftClientProxyBeanConfig;

    @Resource
    private McpBeanRecordService mcpBeanRecordService;

    @Resource
    private PigeonToolInfoDao pigeonToolInfoDao;

    @Resource
    private ThriftToolInfoDao thriftToolInfoDao;

    static {
        Module jacksonModule = new JacksonModule(JacksonOption.RESPECT_JSONPROPERTY_REQUIRED);
        Module openApiModule = new Swagger2Module();
        Module springAiSchemaModule = PROPERTY_REQUIRED_BY_DEFAULT ? new SpringAiSchemaModule()
                : new SpringAiSchemaModule(SpringAiSchemaModule.Option.PROPERTY_REQUIRED_FALSE_BY_DEFAULT);

        SchemaGeneratorConfigBuilder schemaGeneratorConfigBuilder = new SchemaGeneratorConfigBuilder(
                SchemaVersion.DRAFT_2020_12, OptionPreset.PLAIN_JSON)
                .with(jacksonModule)
                .with(openApiModule)
                .with(springAiSchemaModule)
                .with(Option.EXTRA_OPEN_API_FORMAT_VALUES)
                .with(Option.PLAIN_DEFINITION_KEYS);

        SchemaGeneratorConfig typeSchemaGeneratorConfig = schemaGeneratorConfigBuilder.build();
        TYPE_SCHEMA_GENERATOR = new SchemaGenerator(typeSchemaGeneratorConfig);

        SchemaGeneratorConfig subtypeSchemaGeneratorConfig = schemaGeneratorConfigBuilder
                .without(Option.SCHEMA_VERSION_INDICATOR)
                .build();
        SUBTYPE_SCHEMA_GENERATOR = new SchemaGenerator(subtypeSchemaGeneratorConfig);
    }

    protected ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public Long unregisterTool(ToolInfo toolInfo) {
        String toolName = toolInfo.getName();
        ToolTypeEnum type = ToolTypeEnum.valueOf(toolInfo.getType());
        try {
            ToolInfo deletedTool = toolsInfoDao.deleteToolInfoByToolName(toolName, type);
            if (Objects.isNull(deletedTool)) {
                log.info("Failed unregistered {} tool: {}", ToolTypeEnum.valueOf(toolInfo.getType()), toolName);
                return null;
            }
            Long mcpServerId = toolInfo.getMcpServerId();
            McpServerEntity mcpServerEntity = mcpServerService.getMcpServerById(mcpServerId);
            Long businessLineId = mcpServerEntity.getBusinessLineId();
            BusinessLine businessLine = businessLineDao.getBusinessLineById(businessLineId);
            String mcpServerBeanName = BeanNameManager.genMcpServerBeanName(businessLine.getBusinessLine(), mcpServerEntity.getMcpServerName());
            unregisterToolForMcpServer(mcpServerBeanName, deletedTool);
            mcpBeanRecordService.updateAliveMcpRecord(toolInfo.getMcpServerId(), deletedTool.getId());
            return deletedTool.getId();
        } catch (Exception e) {
            log.error("Failed to unregister {} tool: {}", ToolTypeEnum.valueOf(toolInfo.getType()), toolName, e);
            throw new RuntimeException("Failed to unregister " + ToolTypeEnum.valueOf(toolInfo.getType()) + " tool: " + e.getMessage(), e);
        }

    }

    protected <T> T transform(ToolRegisterContext context, Class<T> clazz) {
        String json = JacksonUtil.toJsonStrWithEmptyDefault(context.getToolInfoMap());
        return JacksonUtil.toBeanWithNullDefault(json, clazz);
    }

    protected void registerToolForMcpServer(ToolCallback toolCallback, BusinessLine bussiness, McpServerEntity mcpServer) {
        mcpServerService.registerToolForMcpServer(toolCallback, bussiness, mcpServer);
    }

    @Override
    public void doUnregister(String mcpServerBeanName, String toolName) {
        McpSyncServer server = applicationContext.getBean(mcpServerBeanName, McpSyncServer.class);
        if (server == null) {
            log.error("Can not find mcpServerBean named :{}", mcpServerBeanName);
            return;
        }
        server.removeTool(toolName);
        log.info("Unregister tool {} success", toolName);
    }

    @Override
    public void unregisterToolForMcpServer(String mcpServerBeanName, ToolInfo toolInfo) {
        doUnregister(mcpServerBeanName, toolInfo.getName());
        if (ToolTypeEnum.PIGEON.getValue().equals(toolInfo.getType())) {
            Long pigeonToolId = toolInfo.getPigeonToolId();
            PigeonToolInfo pigeonToolInfoPoByToolId = pigeonToolInfoDao.getPigeonToolInfoPoByToolId(pigeonToolId);
            String pigeonBeanName = BeanNameManager.genPigeonToolKey(pigeonToolInfoPoByToolId);
            pigeonClientProxyConfig.destoryPigeonProxyByPrefix(pigeonBeanName);
        } else if (ToolTypeEnum.THRIFT.getValue().equals(toolInfo.getType())) {
            Long thriftToolId = toolInfo.getThriftToolId();
            ThriftToolInfo thriftToolInfoByToolId = thriftToolInfoDao.getThriftToolInfoByToolId(thriftToolId);
            String thriftBeanName = BeanNameManager.genThriftProxyKey(thriftToolInfoByToolId);
            thriftClientProxyBeanConfig.destroy(thriftBeanName);
        }
        log.info("Unregister tool {} success", toolInfo.getName());
    }

    protected String generateInputSchema(ToolInfo toolInfo) {
        ObjectNode schema = JsonParser.getObjectMapper().createObjectNode();
        schema.put("$schema", SchemaVersion.DRAFT_2020_12.getIdentifier());
        schema.put("type", "object");

        ObjectNode properties = schema.putObject("properties");
        List<String> required = new ArrayList<>();

        List<ToolParameter> parameters = toolInfo.getToolParams();
        for (ToolParameter parameter : parameters) {
            String parameterName = parameter.getName();
            String parameterDescription = parameter.getDescription();
            Type type = ParameterTypeEnum.getTypeByCode(parameter.getType());
            ObjectNode parameterNode = SUBTYPE_SCHEMA_GENERATOR.generateSchema(type);
            if (StringUtils.hasText(parameterDescription)) {
                parameterNode.put("description", parameterDescription);
            }
            properties.set(parameterName, parameterNode);
            if (parameter.getRequired()) {
                required.add(parameterName);
            }
        }

        var requiredArray = schema.putArray("required");
        required.forEach(requiredArray::add);
        return schema.toPrettyString();
    }
}
