package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DXMessageRequest {
    @ToolParam(description = "大象公众号key,非必填，若为空则默认使用bmlMcpServer公众号", required = false)
    private String appKey;

    @ToolParam(description = "大象公众号pubId,非必填，若为空则默认使用bmlMcpServer公众号", required = false)
    private Long fromUid;

    @ToolParam(description = "消息内容", required = true)
    private String message = "空消息";

    @ToolParam(description = "大象群ID", required = false)
    private Long groupId;

    @ToolParam(description = "大象token", required = false)
    private String token;

    @ToolParam(description = "大象用户列表", required = false)
    private List<String> userList = new ArrayList<>();
}
