package com.sankuai.mcpserver.algoplat.mcpserver.domain.register;

import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.stereotype.Service;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/8/18
 */
@Slf4j
@Service
public class DefaultToolRegister extends AbstractToolRegister {
    @Override
    public ToolCallback registerTool(ToolRegisterContext context,Long toolId) {
        return null;
    }

    @Override
    public ToolTypeEnum getToolType() {
        return ToolTypeEnum.SystemDefault;
    }

    @Override
    public ToolCallback getToolCallback(ToolRegisterContext context) {
        return null;
    }
}
