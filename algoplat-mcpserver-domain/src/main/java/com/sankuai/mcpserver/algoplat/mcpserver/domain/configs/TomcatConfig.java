package com.sankuai.mcpserver.algoplat.mcpserver.domain.configs;

import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.http11.Http11NioProtocol;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自定义Tomcat配置类，用于解决socket相关问题
 */
@Configuration
@Slf4j
public class TomcatConfig {

    /**
     * 自定义Tomcat连接器配置
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> tomcatCustomizer() {
        return factory -> {
            factory.addConnectorCustomizers(connector -> {
                Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
                // 设置连接超时
                protocol.setConnectionTimeout(20000);
                // 设置最大连接数
                protocol.setMaxConnections(10000);
                // 设置最大线程数
                protocol.setMaxThreads(200);
                // 设置接受队列大小
                protocol.setAcceptCount(100);
                // 禁用SO_LINGER，避免"Invalid argument"错误
                // Http11NioProtocol没有直接的setSoLinger方法，需要通过Socket属性设置
                connector.setProperty("socket.soLinger", "-1");
                // 启用keep-alive
                protocol.setKeepAliveTimeout(60000);
                protocol.setMaxKeepAliveRequests(10000);
                // 设置异步超时时间，对SSE连接很重要
                connector.setAsyncTimeout(600000);
                log.info("Customized Tomcat connector: connectionTimeout={}, maxConnections={}, maxThreads={}, " +
                        "acceptCount={}, soLinger={}, keepAliveTimeout={}, asyncTimeout={}",
                        protocol.getConnectionTimeout(), protocol.getMaxConnections(), protocol.getMaxThreads(),
                        protocol.getAcceptCount(), connector.getProperty("socket.soLinger"),
                        protocol.getKeepAliveTimeout(), connector.getAsyncTimeout());
            });
        };
    }
}