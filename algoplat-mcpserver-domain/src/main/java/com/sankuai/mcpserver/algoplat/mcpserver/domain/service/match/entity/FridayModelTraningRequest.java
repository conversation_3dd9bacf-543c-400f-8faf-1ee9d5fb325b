package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FridayModelTraningRequest {
    /**
     * 训练任务名称
     */
    private String taskName;

    private String description = "";

    private String trainMethod ="sft";

    private String trainModelSource="base";

    private Map<String, Object> modelAdvancedParams = new HashMap<>();

    private String modelName;

    private List<Long> datasetList = new ArrayList<>();

}
