package com.sankuai.mcpserver.algoplat.mcpserver.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TalosRequest {

    @ToolParam(description = "任务执行Id", required = true)
    private String sql;

    @ToolParam(description = "最多数据条数，超过返回错误信息", required = false)
    private Integer maxCnt;
}
