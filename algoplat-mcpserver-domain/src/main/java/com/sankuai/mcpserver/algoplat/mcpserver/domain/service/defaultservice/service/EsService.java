package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.TemplateInfo;

import java.util.List;

public interface EsService {
    /**
     * 获取指定集群的索引模板信息
     *
     * @param clusterName 集群名称
     * @return 模板信息列表
     */
    List<TemplateInfo> getClusterTemplates(String clusterName);

    /**
     * 创建/更新索引模板
     *
     * @param clusterName 集群名称
     * @param templateName 模板名称
     * @param templateContent 模板内容（JSON对象）
     * @return 是否创建/更新成功
     */
    boolean createTemplate(String clusterName, String templateName, JSONObject templateContent);

    /**
     * 删除索引模板
     *
     * @param clusterName 集群名称
     * @param templateName 模板名称
     * @return 是否删除成功
     */
    boolean deleteTemplate(String clusterName, String templateName);

    /**
     * 创建索引
     *
     * @param clusterName 集群名称
     * @param indexName 索引名称
     * @param settings 索引设置
     * @return 是否创建成功
     */
    boolean createIndex(String clusterName, String indexName, JSONObject settings);
}
