package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/20
 */

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.xm.udb.common.UdbServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserInfoService {
    @Value("${udb.tenant}")
    public String tenant;
    private static final int BATCH_UINFO_QUERY_SIZE = 200;
    @Resource
    private UdbServiceI.Iface udbService;

    /**
     * 批量将用户misId转换为uid
     */
    public Map<String, Long> getUidByMisId(Set<String> misIds) {
        Map<String, Long> uidMap = Maps.newHashMapWithExpectedSize(misIds.size());
        if (CollectionUtils.isEmpty(misIds)) {
            return uidMap;
        }
        List<String> misIdList = new ArrayList<>(misIds);
        try {
            for (List<String> misIdBatch : Lists.partition(misIdList, BATCH_UINFO_QUERY_SIZE)) {
                Set<String> passports = misIdBatch.stream().map(misId -> misId + "@" + tenant).collect(Collectors.toSet());
                Map<String, Long> misId2Uid = udbService.getUidMap(passports);
                uidMap.putAll(misId2Uid);
            }
        } catch (Exception e) {
            log.error("get uidMap fail", e);
        }
        return uidMap;
    }

    /**
     * 将单个misId转换为uid
     */
    public Long getUid(String misId) {
        try {
            String passport = misId + "@" + tenant;
            return udbService.getUid(passport);
        } catch (Exception e) {
            log.error("get uid fail", e);
        }
        return null;
    }

}