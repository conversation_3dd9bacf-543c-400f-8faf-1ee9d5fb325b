package com.sankuai.mcpserver.algoplat.mcpserver.domain.mcpserver;

import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.util.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.ToolRegister;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.ToolRegisterFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.aspect.CatTransaction;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.AliveMcpRecord;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DefaultToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.McpBeanRecordService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.tool.MatchTool;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.parser.ParserToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.ToolManagerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.BeanNameManager;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.ToolAnnotationCollector;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessMcpServerInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import io.modelcontextprotocol.server.McpServer;
import io.modelcontextprotocol.server.McpServerFeatures;
import io.modelcontextprotocol.server.McpSyncServer;
import io.modelcontextprotocol.server.transport.WebMvcSseServerTransportProvider;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.spec.McpServerSession;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.mcp.McpToolUtils;
import org.springframework.ai.mcp.server.autoconfigure.McpServerAutoConfiguration;
import org.springframework.ai.mcp.server.autoconfigure.McpWebMvcServerAutoConfiguration;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.sankuai.mcpserver.algoplat.mcpserver.domain.constant.ServiceConstants.MATCHER_BUSSLINE;
import static com.sankuai.mcpserver.algoplat.mcpserver.domain.constant.ServiceConstants.PARSER_BUSSLINE;
import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants.*;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/19
 */
@Slf4j
@Configuration
@EnableAutoConfiguration(exclude = {
        McpServerAutoConfiguration.class,
        McpWebMvcServerAutoConfiguration.class
})
public class CustomMcpServerConfig {

    @Resource
    private ToolAnnotationCollector toolAnnotationCollector;

    @Autowired
    private DefaultListableBeanFactory beanFactory;

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    private ToolManagerService toolManagerService;

    @Resource
    @Lazy
    private ToolRegisterFactory toolRegisterFactory;

    @Resource
    private RouterFunctionRefresher routerFunctionRefresher;

    private ObjectMapper defaultObjectMapper = new ObjectMapper();

    @Resource
    private McpBeanRecordService mcpBeanRecordService;

    @PostConstruct
    public void initMcpServer() {
        List<BusinessMcpServerInfo> businessMcpServerInfos = toolManagerService.loadBusinessMcpServerInfoFromDataBase();
        initBusinessMcpServer(convertToBusinessLineMap(businessMcpServerInfos));
        routerFunctionRefresher.refreshRouterFunctions();
    }

    private List<ToolCallback> getToolCallbacks(List<ToolInfo> toolInfos) {
        List<ToolCallback> toolCallbacks = new ArrayList<>();
        if (CollectionUtils.isEmpty(toolInfos)) {
            return toolCallbacks;
        }
        for (ToolInfo toolInfo : toolInfos) {
            String type = toolInfo.getType();
            ToolRegister toolRegister = toolRegisterFactory.getToolRegister(ToolTypeEnum.valueOf(type));
            ToolRegisterContext context = new ToolRegisterContext();
            context.setToolInfoMap(JacksonUtil.toMapWithEmptyDefault(JacksonUtil.toJsonStrWithEmptyDefault(toolInfo), String.class, Object.class));
            ToolCallback toolCallback = toolRegister.getToolCallback(context);
            toolCallbacks.add(toolCallback);
        }
        return toolCallbacks;
    }

    public WebMvcSseServerTransportProvider createWebMvcSseServerTransportProvider(ObjectMapper objectMapper, String bussinessline, String mcpServerName) {
        if (objectMapper == null) {
            objectMapper = defaultObjectMapper;
        }
        if (StringUtils.isBlank(bussinessline)) {
            bussinessline = DEFAULT;
        }
        String messageEndpoint = "/mcp/" + bussinessline + "/" + mcpServerName + "/message";
        String sseEndpoint = "/mcp/" + bussinessline + "/" + mcpServerName + "/sse";

        // 检查是否已存在相同的 TransportProvider
        String transportProviderBeanName = BeanNameManager.genTransportProviderBeanName(bussinessline, mcpServerName);
        if (beanFactory.containsBean(transportProviderBeanName)) {
            log.info("TransportProvider {} 已存在，将重新创建", transportProviderBeanName);
            try {
                destroyProvider(transportProviderBeanName);
            } catch (Exception e) {
                log.warn("销毁已存在的 TransportProvider 失败: {}", e.getMessage());
            }
        }

        // 使用正确的构造函数创建 WebMvcSseServerTransportProvider
        WebMvcSseServerTransportProvider webMvcSseServerTransportProvider = new WebMvcSseServerTransportProvider(objectMapper, messageEndpoint, sseEndpoint);

        // 确保 isClosing 标志被设置为 false，允许新的连接
        try {
            Field isClosingField = WebMvcSseServerTransportProvider.class.getDeclaredField("isClosing");
            isClosingField.setAccessible(true);
            isClosingField.set(webMvcSseServerTransportProvider, false);

            // 确保 sessions 映射被正确初始化
            Field sessionsField = WebMvcSseServerTransportProvider.class.getDeclaredField("sessions");
            sessionsField.setAccessible(true);
            Object sessionsObj = sessionsField.get(webMvcSseServerTransportProvider);
            if (sessionsObj == null) {
                sessionsField.set(webMvcSseServerTransportProvider, new ConcurrentHashMap<String, McpServerSession>());
            }

            log.debug("已确保 TransportProvider 处于开放状态，可以接受新连接");
        } catch (Exception e) {
            log.warn("设置 TransportProvider 状态失败: {}", e.getMessage());
        }

        beanFactory.registerSingleton(transportProviderBeanName, webMvcSseServerTransportProvider);
        log.info("成功创建 TransportProvider: {}, messageEndpoint={}, sseEndpoint={}",
                transportProviderBeanName, messageEndpoint, sseEndpoint);
        return webMvcSseServerTransportProvider;
    }

    public void createRouterFunction(String bussinessline, String mcpServerName) {
        String transportProviderBeanName = BeanNameManager.genTransportProviderBeanName(bussinessline, mcpServerName);
        String routerFunctionBeanName = BeanNameManager.genRouterFunctionBeanName(bussinessline, mcpServerName);

        // 检查是否已存在相同的 RouterFunction
        if (beanFactory.containsBean(routerFunctionBeanName)) {
            log.info("RouterFunction {} 已存在，将重新创建", routerFunctionBeanName);
            try {
                destroyRouterFunction(routerFunctionBeanName);
            } catch (Exception e) {
                log.warn("销毁已存在的 RouterFunction 失败: {}", e.getMessage());
            }
        }

        WebMvcSseServerTransportProvider provider = applicationContext.getBean(transportProviderBeanName, WebMvcSseServerTransportProvider.class);
        beanFactory.registerSingleton(routerFunctionBeanName, provider.getRouterFunction());
        log.info("成功创建 RouterFunction: {}", routerFunctionBeanName);
    }

    public void createMcpSyncServer(List<ToolCallback> toolCallbacks, String bussinessline, String mcpServerName) {
        String transportProviderBeanName = BeanNameManager.genTransportProviderBeanName(bussinessline, mcpServerName);
        String mcpServerBeanName = BeanNameManager.genMcpServerBeanName(bussinessline, mcpServerName);

        // 检查是否已存在相同的 McpSyncServer
        if (beanFactory.containsBean(mcpServerBeanName)) {
            log.info("McpSyncServer {} 已存在，将重新创建", mcpServerBeanName);
            try {
                destroyServer(mcpServerBeanName);
            } catch (Exception e) {
                log.warn("销毁已存在的 McpSyncServer 失败: {}", e.getMessage());
            }
        }

        WebMvcSseServerTransportProvider provider = applicationContext.getBean(transportProviderBeanName, WebMvcSseServerTransportProvider.class);
        var capabilities = McpSchema.ServerCapabilities.builder()
                .tools(true)
                .logging()
                .build();

        List<McpServerFeatures.SyncToolSpecification> syncToolSpecification = McpToolUtils.toSyncToolSpecification(toolCallbacks);
        McpSyncServer server = McpServer.sync(provider)
                .serverInfo("BML MCP Server : " + bussinessline + SERVER, "1.0.0")
                .capabilities(capabilities)
                .tools(syncToolSpecification)
                .build();

        beanFactory.registerSingleton(mcpServerBeanName, server);
        log.info("成功创建 McpSyncServer: {}", mcpServerBeanName);
    }


    private Map<String, BusinessMcpServerInfo> convertToBusinessLineMap(List<BusinessMcpServerInfo> businessMcpServerInfos) {
        if (CollectionUtils.isEmpty(businessMcpServerInfos)) {
            return new HashMap<>();
        }
        return businessMcpServerInfos.stream()
                .filter(info -> info != null && info.getBusinessLine() != null)
                .collect(Collectors.toMap(
                        info -> info.getBusinessLine().getBusinessLine(),
                        info -> info,
                        (existing, replacement) -> existing
                ));
    }

    private void initBusinessMcpServer(Map<String, BusinessMcpServerInfo> businessMcpServerInfosMap) {
        List<FunctionCallback> parserToolServiceFunctionCallback = toolAnnotationCollector.getAtToolFunctionCallbacks(ParserToolService.class);
        List<FunctionCallback> defaultToolFunctionCallbacks = toolAnnotationCollector.getAtToolFunctionCallbacks(DefaultToolService.class);
        List<FunctionCallback> matchToolServiceFunctionCallback = toolAnnotationCollector.getAtToolFunctionCallbacks(MatchTool.class);
        for (Map.Entry<String, BusinessMcpServerInfo> businessConfig : businessMcpServerInfosMap.entrySet()) {
            String businessLine = businessConfig.getKey();
            BusinessMcpServerInfo businessMcpServerInfo = businessConfig.getValue();
            List<McpServerEntity> mcpServerList = businessMcpServerInfo.getMcpServerEntityList();
            for (McpServerEntity mcpServer : mcpServerList) {
                try {
                    createWebMvcSseServerTransportProvider(defaultObjectMapper, businessLine, mcpServer.getMcpServerName());
                    createRouterFunction(businessLine, mcpServer.getMcpServerName());
                    List<ToolInfo> toolInfos = mcpServer.getToolInfoList();
                    List<ToolCallback> toolCallbacks = getToolCallbacks(toolInfos);
                    List<String> defaultToolNameList = mcpServer.getDefaultToolNameList();
                    if (!CollectionUtils.isEmpty(defaultToolNameList)) {
                        for (FunctionCallback defaultToolFunctionCallback : defaultToolFunctionCallbacks) {
                            String name = defaultToolFunctionCallback.getName();
                            if (defaultToolNameList.contains(name)) {
                                toolCallbacks.add((ToolCallback) defaultToolFunctionCallback);
                            }
                        }
                    }
                    if (businessLine.equals(PARSER_BUSSLINE) && "ParserDefaultServer".equals(mcpServer.getMcpServerName()) && !parserToolServiceFunctionCallback.isEmpty()) {
                        parserToolServiceFunctionCallback.forEach(parserFunctionCallback -> toolCallbacks.add((ToolCallback) parserFunctionCallback));
                    }
                    if (businessLine.equals(MATCHER_BUSSLINE) && "MatcherDefaultServer".equals(mcpServer.getMcpServerName()) && !parserToolServiceFunctionCallback.isEmpty()) {
                        matchToolServiceFunctionCallback.forEach(matcherFunctionCallback -> toolCallbacks.add((ToolCallback) matcherFunctionCallback));
                    }
                    AliveMcpRecord aliveMcpRecord = new AliveMcpRecord();
                    aliveMcpRecord.setMcpServerId(mcpServer.getId());
                    Set<Long> initAliveMcpToolIds = toolInfos.stream().map(ToolInfo::getId).collect(Collectors.toSet());
                    aliveMcpRecord.setAliveMcpToolIds(initAliveMcpToolIds);
                    aliveMcpRecord.setAliveDefaultMcpToolNames(defaultToolNameList);
                    mcpBeanRecordService.addAliveMcpRecord(aliveMcpRecord);
                    createMcpSyncServer(toolCallbacks, businessLine, mcpServer.getMcpServerName());
                    log.info("成功初始化 McpServer: {}/{}", businessLine, mcpServer.getMcpServerName());
                } catch (Exception e) {
                    log.error("初始化 McpServer 失败: {}/{}, 错误: {}", businessLine, mcpServer.getMcpServerName(), e.getMessage(), e);
                    throw e;
                }
            }
        }
    }


    @CatTransaction(type = "CustomMcpServerConfig", name = "createMcpSyncServer")
    public void createMcpSyncServer(BusinessLine businessLineById, String mcpServerName, Long mcpServerId) {
        try {
            String businessLine = businessLineById.getBusinessLine();
            // Spring 中创建McpServer
            WebMvcSseServerTransportProvider provider = createWebMvcSseServerTransportProvider(null, businessLine, mcpServerName);

            // 确保 TransportProvider 已经准备好接受连接
            Field isClosingField = WebMvcSseServerTransportProvider.class.getDeclaredField("isClosing");
            isClosingField.setAccessible(true);
            isClosingField.set(provider, false);

            // 创建 RouterFunction
            createRouterFunction(businessLine, mcpServerName);

            // 刷新路由，确保路由能够立即生效
            routerFunctionRefresher.refreshRouterFunctions();

            // 等待一小段时间，确保路由有时间生效
            Thread.sleep(500);
            // 创建 McpSyncServer
            AliveMcpRecord aliveMcpRecord = new AliveMcpRecord();
            aliveMcpRecord.setMcpServerId(mcpServerId);
            mcpBeanRecordService.addAliveMcpRecord(aliveMcpRecord);
            createMcpSyncServer(new ArrayList<>(), businessLine, mcpServerName);

            // 再次确保 TransportProvider 处于开放状态
            Field isClosing = WebMvcSseServerTransportProvider.class.getDeclaredField("isClosing");
            isClosing.setAccessible(true);
            isClosing.set(provider, false);
            log.info("已再次确保 TransportProvider 处于开放状态，可以接受新连接");
            // 再次刷新路由，确保路由生效
            routerFunctionRefresher.refreshRouterFunctions();

        } catch (Exception e) {
            log.error("创建 McpSyncServer 失败: {}/{}, 错误: {}", businessLineById.getBusinessLine(), mcpServerName, e.getMessage(), e);
            throw new RuntimeException("创建 McpSyncServer 失败", e);
        }
    }

    public void destroyMcpSyncServer(String businessLineStr, String mcpServerName,Long mcpServerId) {
        log.info("开始销毁 McpSyncServer: {}/{}", businessLineStr, mcpServerName);

        String transportProviderBeanName = BeanNameManager.genTransportProviderBeanName(businessLineStr, mcpServerName);
        String routerFunctionBeanName = BeanNameManager.genRouterFunctionBeanName(businessLineStr, mcpServerName);
        String syncServerBeanName = BeanNameManager.genSyncServerBeanName(businessLineStr, mcpServerName);

        try {
            destroyServer(syncServerBeanName);
            Thread.sleep(500);
            destroyProvider(transportProviderBeanName, false);
            // 等待一小段时间，确保 TransportProvider 有时间完成关闭操作
            Thread.sleep(500);
            // 最后销毁 RouterFunction
            destroyRouterFunction(routerFunctionBeanName);
            // 刷新路由，确保变更立即生效
            routerFunctionRefresher.refreshRouterFunctions();
            mcpBeanRecordService.removeAliveMcpRecord(mcpServerId);
            log.info("McpSyncServer 销毁完成: {}/{}", businessLineStr, mcpServerName);
        } catch (Exception e) {
            log.error("销毁 McpSyncServer 失败: {}/{}, 错误: {}", businessLineStr, mcpServerName, e.getMessage(), e);
            throw new RuntimeException("销毁 McpSyncServer 失败", e);
        }
    }

    /**
     * 销毁 TransportProvider
     * 确保在销毁前关闭所有活跃的会话连接
     *
     * @param beanName TransportProvider 的 Bean 名称
     */
    public void destroyProvider(String beanName) {
        destroyProvider(beanName, true);
    }

    /**
     * 销毁 TransportProvider
     *
     * @param beanName              TransportProvider 的 Bean 名称
     * @param forceCloseAllSessions 是否强制关闭所有会话连接
     */
    public void destroyProvider(String beanName, boolean forceCloseAllSessions) {
        try {
            if (!beanFactory.containsBean(beanName)) {
                log.warn("TransportProvider {} not found", beanName);
                return;
            }

            WebMvcSseServerTransportProvider provider =
                    beanFactory.getBean(beanName, WebMvcSseServerTransportProvider.class);

            // 1. 首先设置 isClosing 标志，防止新的连接建立
            try {
                Field isClosingField = WebMvcSseServerTransportProvider.class.getDeclaredField("isClosing");
                isClosingField.setAccessible(true);
                isClosingField.set(provider, true);
                log.debug("已设置 TransportProvider 为关闭状态，不再接受新连接");
            } catch (Exception e) {
                log.warn("设置 isClosing 标志失败: {}", e.getMessage());
            }

            // 2. 获取当前活跃的会话
            ConcurrentHashMap<String, McpServerSession> sessions = null;
            try {
                Field sessionsField = WebMvcSseServerTransportProvider.class.getDeclaredField("sessions");
                sessionsField.setAccessible(true);
                @SuppressWarnings("unchecked")
                ConcurrentHashMap<String, McpServerSession> sessionsMap =
                        (ConcurrentHashMap<String, McpServerSession>) sessionsField.get(provider);
                sessions = sessionsMap;
            } catch (Exception e) {
                log.warn("获取会话连接信息时发生错误: {}", e.getMessage());
            }

            // 3. 处理现有会话连接
            if (sessions != null && !sessions.isEmpty()) {
                int sessionCount = sessions.size();
                log.info("当前有 {} 个活跃会话连接", sessionCount);

                if (forceCloseAllSessions) {
                    // 强制关闭所有会话连接
                    log.info("正在强制关闭 {} 个活跃会话连接", sessionCount);

                    // 创建会话ID列表的副本，避免并发修改异常
                    List<String> sessionIds = new ArrayList<>(sessions.keySet());

                    // 逐个关闭会话
                    for (String sessionId : sessionIds) {
                        try {
                            McpServerSession session = sessions.get(sessionId);
                            if (session != null) {
                                try {
                                    // 使用超时机制确保不会无限等待
                                    session.closeGracefully()
                                            .timeout(Duration.ofSeconds(3))
                                            .doOnError(e -> log.warn("关闭会话 {} 时发生错误: {}", sessionId, e.getMessage()))
                                            .onErrorComplete()
                                            .block();
                                    log.debug("成功关闭会话: {}", sessionId);
                                } catch (Exception e) {
                                    log.warn("关闭会话 {} 时发生错误: {}", sessionId, e.getMessage());
                                    // 尝试直接关闭
                                    try {
                                        session.close();
                                        log.debug("强制关闭会话: {}", sessionId);
                                    } catch (Exception ex) {
                                        log.warn("强制关闭会话 {} 时发生错误: {}", sessionId, ex.getMessage());
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.warn("处理会话 {} 时发生错误: {}", sessionId, e.getMessage());
                        }
                    }

                    // 等待一小段时间，确保会话有时间完成关闭操作
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }

                    // 检查是否有残留的会话
                    if (!sessions.isEmpty()) {
                        log.warn("仍有 {} 个会话未能正常关闭，将强制清除", sessions.size());
                        sessions.clear();
                    }

                    log.info("所有会话连接已关闭");
                } else {
                    // 温和模式：不强制关闭会话，让它们自然结束
                    log.info("使用温和模式销毁 TransportProvider，不强制关闭现有会话连接");
                    log.info("当前有 {} 个活跃会话连接，将保持它们运行直到自然结束", sessionCount);
                }
            } else {
                log.info("没有活跃的会话连接需要关闭");
            }

            // 4. 调用 closeGracefully 方法
            try {
                provider.closeGracefully()
                        .timeout(Duration.ofSeconds(5))
                        .doOnError(e -> log.warn("优雅关闭 TransportProvider 失败: {}, error: {}", beanName, e.getMessage()))
                        .onErrorComplete()
                        .block();
                log.info("Successfully closed TransportProvider gracefully: {}", beanName);
            } catch (Exception e) {
                log.warn("Failed to close TransportProvider gracefully: {}, error: {}", beanName, e.getMessage());
            }

            // 5. 从 Spring 容器中销毁 Bean
            try {
                beanFactory.destroySingleton(beanName);
                log.info("Successfully destroyed TransportProvider: {}", beanName);
            } catch (Exception e) {
                log.error("Failed to destroy TransportProvider bean: {}, error: {}", beanName, e.getMessage());

            }
        } catch (Exception e) {
            log.error("Failed to destroy TransportProvider: {}", beanName, e);
        }
    }

    /**
     * 销毁 RouterFunction
     */
    public void destroyRouterFunction(String beanName) {
        try {
            if (!beanFactory.containsBean(beanName)) {
                log.warn("RouterFunction {} not found", beanName);
                return;
            }
            beanFactory.destroySingleton(beanName);
            log.info("Successfully destroyed RouterFunction: {}", beanName);
        } catch (Exception e) {

            log.error("Failed to destroy RouterFunction: {}", beanName, e);
            throw new RuntimeException("Failed to destroy RouterFunction", e);
        }
    }

    /**
     * 销毁 McpSyncServer
     */
    public void destroyServer(String beanName) {
        try {
            if (!beanFactory.containsBean(beanName)) {
                log.warn("McpSyncServer {} not found", beanName);
                return;
            }

            McpSyncServer server = beanFactory.getBean(beanName, McpSyncServer.class);

            try {
                // 使用超时机制确保不会无限等待
                server.closeGracefully();
                log.info("Successfully closed McpSyncServer gracefully: {}", beanName);
            } catch (Exception e) {

                log.warn("Failed to close McpSyncServer gracefully: {}, falling back to force close", beanName, e);
                try {
                    server.close();
                    log.info("Successfully force closed McpSyncServer: {}", beanName);
                } catch (Exception ex) {
                    log.warn("Failed to force close McpSyncServer: {}", beanName, ex);
                }
            }
            try {
                beanFactory.destroySingleton(beanName);
                log.info("Successfully destroyed McpSyncServer bean: {}", beanName);
            } catch (Exception e) {
                log.error("Failed to destroy McpSyncServer bean: {}", beanName, e);

                throw e;
            }
        } catch (Exception e) {

            log.error("Failed to destroy McpSyncServer: {}", beanName, e);
            throw new RuntimeException("Failed to destroy McpSyncServer", e);
        }
    }
}