package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.util.CollectionUtils;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.LLMService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.CustomEvalModel;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.ModelRegistRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.Datasets;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ModelTraningReq;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.FridayService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.HttpUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.cache.TairClient;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpMissionDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.MissionTypeEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.factory.ThreadPoolFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.service.impl.S3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.lang.reflect.Type;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.sankuai.mcpserver.algoplat.mcpserver.domain.constant.ServiceConstants.*;
import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.LionConfig.*;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/18
 */

@Service
@Slf4j
public class FridayServiceImpl implements FridayService {

    @Resource
    private S3Service s3Service;

    @Autowired
    private TairClient tairClient;

    @Resource
    private McpMissionDao mcpMissionDao;

    private DateTimeFormatter shortFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    // 缓存机器标识，避免重复获取
    private static String machineIdentifier = null;

    @Resource
    private GeneralProcessingService generalProcessingService;

    @Resource
    private McpMissionDao mcpserverMissionDao;

    /**
     * LLM预测服务
     */
    @Autowired
    private LLMService llmPredictService;

    @Override
    public String performModelRegistration(String modelName, String tranName, String modelClass, String iter, String trainingTaskId, String misId) throws Exception {

        Long artifatcsVersionId = getArtifatcsVersionId(iter, trainingTaskId, misId);
        if (artifatcsVersionId == null) {
            throw new Exception("artifatcsVersionId没有获取成功，请检查iter和trainingTaskId是否正确" + iter + "_:_" + trainingTaskId);
        }
        List<Object> artifatcsVersionList = new ArrayList<>();
        artifatcsVersionList.add(trainingTaskId);
        artifatcsVersionList.add(artifatcsVersionId);
        Integer latestVersion = 1;
        ModelRegistRequest modelRegistRequest = ModelRegistRequest.builder()
                .name(modelName)
                .description(tranName)
                .modelClassType("text")
                .modelClass(modelClass)
                .latestVersion(latestVersion)
                .artifatcsVersionId(artifatcsVersionId)
                .artifatcsVersionList(artifatcsVersionList)
                .tenantId(TENANT_ID)
                .modelPrefabricatedName("")
                .modelPath("")
                .modelName("")
                .build();
        String result = HttpUtil.httpPostJson(FRIDAY_MODEAL_REGIST_URL, JacksonUtil.toJsonStr(modelRegistRequest), getFridayHeader(misId));
        if (result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        return result;
    }

    private Long getArtifatcsVersionId(String iter, String trainingTaskId, String misId) throws Exception {
        String runListByTenantId = getRunListByTenantId(misId);
        JSONObject jsonObject = JSONObject.parseObject(runListByTenantId);
        if (!"0".equals(jsonObject.get("code").toString()) || jsonObject.get("data") == null) {
            Cat.logError(new Exception("获取训练列表失败"));
            throw new Exception("获取训练列表失败,请检查iter和trainingTaskId是否正确。" + iter + "_:_" + trainingTaskId);
        }
        Object data = jsonObject.get("data");
        Type type = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        List<Map<String, Object>> dataList = JSONObject.parseObject(data.toString(), type);
        if (CollectionUtils.isEmpty(dataList)) {
            Cat.logError(new Exception("获取训练列表为空"));
            throw new Exception("获取训练列表为空,请检查iter和trainingTaskId是否正确。" + iter + "_:_" + trainingTaskId);
        }
        for (Map<String, Object> map : dataList) {
            if (trainingTaskId.equals(map.get("id"))) {
                Object artifactsVersionListObj = map.get("artifactsVersionList");
                if (artifactsVersionListObj == null) {
                    Cat.logError(new Exception("获取训练列表为空"));
                    throw new Exception("获取训练列表为空,请检查iter和trainingTaskId是否正确。" + iter + "_" + trainingTaskId);
                }
                List<Map<String, Object>> artifactsVersionList = JSONObject.parseObject(artifactsVersionListObj.toString(), type);
                for (Map<String, Object> element : artifactsVersionList) {
                    if (iter.equals(element.get("step"))) {
                        return Long.valueOf(element.get("artifactsVersionId").toString());
                    }
                }
            }
        }
        return null;
    }

    /**
     * 更新Friday任务信息到数据库
     */
    @Override
    public void updateFridayMissionInfo(ExecuteFridayMissionContext context, ExecuteFridayMissionRequest fridayRequest, McpMissionInfo fridayMissionInfo) {
        try {
            fridayMissionInfo.setStatus(context.getStatus());
            fridayMissionInfo.setMissionRequest(JSONObject.toJSONString(fridayRequest));
            // 确保missionResult是有效的JSON格式
            String missionResult = context.getMissionResult();
            if (missionResult != null && !missionResult.startsWith("{") && !missionResult.startsWith("[")) {
                // 如果不是JSON格式，将其转换为JSON对象
                JSONObject resultJson = new JSONObject();
                resultJson.put("result", missionResult);
                fridayMissionInfo.setMissionResult(resultJson.toJSONString());
            } else {
                fridayMissionInfo.setMissionResult(missionResult);
            }
            fridayMissionInfo.setMissionContext(JSONObject.toJSONString(context));
            fridayMissionInfo.setMisId(fridayRequest.getMisId());
            fridayMissionInfo.setType(MissionTypeEnum.FRIDAY.getCode());
            fridayMissionInfo.setUpdateTime(new Date());
            fridayMissionInfo.setMissionId(context.getMissionId());
            mcpMissionDao.updateMissionInfo(fridayMissionInfo);
        } catch (Exception e) {
            log.error("更新Friday任务信息失败,missionId:{}", context.getMissionId(), e);
            throw e;
        }
    }


    private String getRunListByTenantId(String misId) throws Exception {
        Map<String, String> param = new HashMap<>();
        param.put("tenantId", TENANT_ID);
        String result = HttpUtil.httpGet(FRIDAY_GET_TRAINING_LIST, param, getFridayHeader(misId));
        if (result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        return result;
    }

    @Override
    public String deployLLMServiceOnFriday(ExecuteFridayMissionContext executeFridayMissionContext, Integer maxSessionLen, String misId) throws Exception {
        //文本生成
        String modelName = executeFridayMissionContext.getModelDeployName();
        Map<String, Object> modelInfo = getModelInfo(modelName, misId, executeFridayMissionContext);
        if (MapUtils.isEmpty(modelInfo)) {
            Cat.logError(new Exception("模型信息为空"));
            throw new Exception("模型信息为空");
        }
        Map<String, Object> rateLimitConfig = new HashMap<>();
        rateLimitConfig.put("tpm", 78643200);
        rateLimitConfig.put("rpm", 19200);
        Map<String, Boolean> inferConfig = new HashMap<>();
        inferConfig.put("enableAdvancedConfig", true);
        inferConfig.put("useDefaultAdvancedConfig", true);
        Map<String, Object> chatTemplateConfig = new HashMap<>();
        chatTemplateConfig.put("useDefaultChatTemplate", true);
        chatTemplateConfig.put("chatTemplate", "");
        List<Map<String, Object>> queueInstanceCount = new ArrayList<>();

        String gpuCountDataForAllQueuesInProjectGroup = getGPUCountDataForAllQueuesInProjectGroup(misId);
        Map<String, String> queue = findQueue(gpuCountDataForAllQueuesInProjectGroup);

        Map<String, Object> queueMap = new HashMap<>();
        queueMap.put("queue", queue.get("queue"));
        queueMap.put("instanceNum", 1);
        queueInstanceCount.add(queueMap);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("tenantId", TENANT_ID);
        requestBody.put("serviceName", modelName);
        requestBody.put("serviceType", 1);
        requestBody.put("modelId", modelInfo.get("model_id"));
        requestBody.put("modelVersionId", modelInfo.get("modelVersionId"));
        requestBody.put("modelName", modelName);
        requestBody.put("modelVersion", modelInfo.get("version"));
        requestBody.put("describe", modelName);
        requestBody.put("defaultQueue", false);
        requestBody.put("mlpProject", GROUP_NAME);
        //单实例GPU数
        requestBody.put("gpuPerInstance", 1);
        //模型类型
        requestBody.put("gpuType", queue.get("gpuType"));
        requestBody.put("queueInstanceCount", queueInstanceCount);
        requestBody.put("deployMode", "turboMind");
        requestBody.put("enableKvCache", true);
        requestBody.put("rateLimitConfig", rateLimitConfig);
        requestBody.put("inferConfig", inferConfig);
        requestBody.put("chatTemplateConfig", chatTemplateConfig);
        requestBody.put("maxSessionLen", maxSessionLen);
        String result = HttpUtil.httpPostJson(FRIDAY_DEPLOY_LLM_MODEL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(misId));
        if (result == null) {
            log.warn("首次部署LLM服务请求返回null，进行重试，missionId: {}", executeFridayMissionContext.getMissionId());
            result = HttpUtil.httpPostJson(FRIDAY_DEPLOY_LLM_MODEL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(misId));
        }
        if (result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        return result;
    }

    public Map<String, Object> getModelInfo(String modelName, String misId, ExecuteFridayMissionContext executeFridayMissionContext) throws Exception {
        if (modelName == null) {
            throw new Exception("模型名称或模型版本为空");
        }
        String allModelsListUnderCurrentTenant = getAllModelsListUnderCurrentTenant(misId);
        JSONObject jsonObject = JSONObject.parseObject(allModelsListUnderCurrentTenant);
        if (allModelsListUnderCurrentTenant == null || !jsonObject.get("code").toString().equals("0") || jsonObject.get("data") == null) {
            Cat.logError(new Exception("获取模型列表失败"));
            throw new Exception("获取模型列表失败");
        }
        Object data = jsonObject.get("data");
        Type type = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        Map<String, Object> dataList = JSONObject.parseObject(data.toString(), Map.class);
        Object models = dataList.get("models");

        List<Map<String, Object>> modelsList = JSONObject.parseObject(models.toString(), type);
        if (modelsList == null || modelsList.size() == 0) {
            throw new Exception("模型列表为空");
        }
        Type ListMapType = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        for (Map<String, Object> model : modelsList) {
            if (modelName.equals(model.get("model_name"))) {
                List<Map<String, Object>> versions = JSONObject.parseObject(model.get("versions").toString(), ListMapType);
                if (versions == null || versions.size() == 0) {
                    throw new Exception("模型版本列表为空");
                }
                Object modelId = model.get("model_id");
                if (modelId != null) {
                    executeFridayMissionContext.setModelId(modelId.toString());
                    executeFridayMissionContext.setFridayModelUrl(buildFridayModelUrl(modelId.toString()));
                }
                Map<String, Object> modelInfo = versions.stream()
                        .max(Comparator.comparingInt(map -> ((Number) map.get("version")).intValue()))
                        .map(maxVersionMap -> {
                            Map<String, Object> result = new HashMap<>();
                            result.putAll(model);
                            result.putAll(maxVersionMap);
                            result.remove("versions");
                            return result;
                        })
                        .orElse(new HashMap<>());
                return modelInfo;
            }
        }
        return new HashMap<>();
    }

    private String buildFridayModelUrl(String modelId) {
        return String.format("https://friday.sankuai.com/ml/modelManagement/%s", modelId);
    }

    private String getAllModelsListUnderCurrentTenant(String misId) throws Exception {
        String modelTask = "text";
        Map<String, String> param = new HashMap<>();
        param.put("tenantId", TENANT_ID);
        param.put("modelTask", modelTask);
        String result = HttpUtil.httpGet(FRIDAY_GET_MODEL_LIST, param, getFridayHeader(misId));
        if (result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        return result;
    }

    private Map<String, String> findQueue(String jsonString) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonString);

        String targetQueue = "root.yg_serving_cluster.hadoop-daodian.bmlserving";
        // 只考虑L40-48G GPU卡
        String gpuType = "L40-48G";
        Map<String, String> result = new HashMap<>();

        // 先检查目标队列是否有可用L40-48G GPU
        for (JsonNode entry : rootNode) {
            String queue = entry.get("queue").asText();
            if (targetQueue.equals(queue)) {
                JsonNode gpus = entry.get("gpus");
                int gpuNum = gpus.get(gpuType).get("num").asInt();
                if (gpuNum > 0) {
                    result.put("queue", targetQueue);
                    result.put("gpuType", gpuType);
                    return result;
                }
                break;
            }
        }

        // 在所有队列中查找拥有最多L40-48G GPU的队列
        String queueWithMostGPU = null;
        int maxGPUNum = 0;

        for (JsonNode entry : rootNode) {
            JsonNode gpus = entry.get("gpus");
            int gpuNum = gpus.get(gpuType).get("num").asInt();

            if (gpuNum > maxGPUNum) {
                maxGPUNum = gpuNum;
                queueWithMostGPU = entry.get("queue").asText();
            }
        }

        if (maxGPUNum > 0) {
            result.put("queue", queueWithMostGPU);
            result.put("gpuType", gpuType);
            return result;
        }

        // 如果没有找到可用的L40-48G GPU，抛出异常
        throw new Exception("没有找到可用的L40-48G GPU卡，请稍后再试");
    }


    private String getGPUCountDataForAllQueuesInProjectGroup(String misId) throws Exception {
        Map<String, String> param = new HashMap<>();
        param.put("mlpProject", GROUP_NAME);
        String result = HttpUtil.httpGet(FRIDAY_GET_GPU_QUEUE, param, getFridayHeader(misId));
        if (result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        Object data = jsonObject.get("data");
        if (data == null) {
            throw new Exception("获取al-zb-ddpt项目组下，GPU队列详情失败！");
        }
        Type type = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        List<Map<String, Object>> queueInfoList = JSON.parseObject(data.toString(), type);
        if (CollectionUtils.isEmpty(queueInfoList)) {
            throw new Exception("未找到GPU队列信息");
        }
        for (Map<String, Object> map : queueInfoList) {
            String queueName = (String) map.get("queue");
            Map<String, String> params = new HashMap<>();
            params.put("queue", queueName);
            params.put("tenantId", TENANT_ID);
            String gpuInfo = HttpUtil.httpGet(FRIDAY_GET_QUEUE_GPU_INFO, params, getFridayHeader(misId));
            if (gpuInfo == null) {
                continue;
            }
            if (result.contains("统一登录中心")) {
                throw new Exception("Friday ssoId信息过期，请更新");
            }

            JSONObject gpuInfoJsonObj = JSONObject.parseObject(gpuInfo);
            Object dataMap = gpuInfoJsonObj.get("data");
            if (dataMap == null) {
                throw new Exception("获取GPU信息失败");
            }
            JSONObject dataJSON = JSONObject.parseObject(dataMap.toString());
            Object gpus = dataJSON.get("gpus");
            map.put("gpus", gpus);
        }
        log.info("GPU队列信息：{}", JSONObject.toJSON(queueInfoList));
        return JSONObject.toJSONString(queueInfoList);
    }

    @Override
    public void executeModelDeploymentTask(ExecuteFridayMissionRequest fridayRequest, ExecuteFridayMissionContext executeFridayMissionContext, McpMissionInfo mcpMissionInfoByMission) throws Exception {
        if (executeFridayMissionContext != null && executeFridayMissionContext.getModelDeployStatus() == 1) {
            Cat.logEvent("executeModelDeploymentTask", String.format("missionId:%s的模型部署任务已完成", executeFridayMissionContext.getMissionId()));
            log.info(String.format("missionId:%s的模型部署任务已完成", executeFridayMissionContext.getMissionId()));
            return;
        }
        Cat.logEvent("executeModelDeploymentTask", "startDeployModel,missionid:" + executeFridayMissionContext.getMissionId());
        Transaction t = Cat.newTransaction("executeModelDeploymentTask", "startDeployModel,missionid:" + executeFridayMissionContext.getMissionId());
        try {
            if (executeFridayMissionContext.getModelDeployStatus() == 0 && mcpMissionInfoByMission.getStatus() == 0 && !StringUtils.isBlank(executeFridayMissionContext.getModelServiceId())) {
                Boolean deploymentStatus = queryModelDeploymentStatus(executeFridayMissionContext.getModelServiceId(), executeFridayMissionContext);
                if (deploymentStatus) {
                    executeFridayMissionContext.setModelDeployStatus(1);
                    log.info("模型部署完成,missionID:{},context:{}", executeFridayMissionContext.getMissionId(), JSONObject.toJSONString(executeFridayMissionContext));
                    executeFridayMissionContext.setExecuteMessage(String.format("模型部署成功,missionId:%s,接下来要执行模型测试", executeFridayMissionContext.getMissionId()));
                    updateFridayMissionInfo(executeFridayMissionContext, fridayRequest, mcpMissionInfoByMission);
                }
            } else {
                executeFridayMissionContext.setModelDeployStatus(0);
                executeFridayMissionContext.setStatus(0);
                executeFridayMissionContext.setExecuteMessage(String.format("正在执行模型部署,missionId:%s", executeFridayMissionContext.getMissionId()));
                updateFridayMissionInfo(executeFridayMissionContext, fridayRequest, mcpMissionInfoByMission);
                //2. 服务部署
                //模型未部署过，部署模型
                Map<String, String> modelParam = fridayRequest.getModelParam();
                if (!isModelDeployed(executeFridayMissionContext.getModelDeployName(), fridayRequest.getMisId())) {
                    int maxSeqLength = Integer.parseInt(modelParam.get("max_seq_length")) / 1000 + 1;
                    String deployResult = deployLLMServiceOnFriday(executeFridayMissionContext, maxSeqLength, fridayRequest.getMisId());
                    JSONObject deployJson = JSONObject.parseObject(deployResult);
                    String modelServiceId = deployJson.getString("data");
                    log.info("missionId:{},模型部署结果:{}", mcpMissionInfoByMission.getMissionId(), deployResult);
                    if ("0".equals(deployJson.getString("code")) && !"服务已存在".equals(deployJson.getString("message"))) {
                        executeFridayMissionContext.setModelServiceId(modelServiceId);
                        executeFridayMissionContext.setFridayModelDeployUrl(buildFridayModelDeployUrl(modelServiceId));
                        updateFridayMissionInfo(executeFridayMissionContext, fridayRequest, mcpMissionInfoByMission);
                    } else {
                        // 如果不是因为服务已存在导致的部署失败，则抛出异常
                        Cat.logError(new Exception("服务部署失败: " + deployJson.getString("code") + deployJson.getString("msg") + JSONObject.toJSONString(fridayRequest)));
                        throw new Exception("服务部署失败: " + deployJson.getString("code") + deployJson.getString("msg"));
                    }
                } else {
                    executeFridayMissionContext.setModelDeployStatus(1);
                    executeFridayMissionContext.setExecuteMessage("模型已部署过,无需再次部署");
                    String modelServiceId = getFridayModelDeployInfo(executeFridayMissionContext.getModelDeployName(), fridayRequest.getMisId());
                    executeFridayMissionContext.setModelServiceId(modelServiceId);
                    executeFridayMissionContext.setFridayModelDeployUrl(buildFridayModelDeployUrl(modelServiceId));
                    updateFridayMissionInfo(executeFridayMissionContext, fridayRequest, mcpMissionInfoByMission);
                }
                t.setSuccessStatus();
            }
        } catch (Exception e) {
            executeFridayMissionContext.setModelDeployStatus(-1);
            executeFridayMissionContext.setStatus(-1);
            executeFridayMissionContext.setExecuteMessage("执行模型部署失败，missionId:" + executeFridayMissionContext.getMissionId() + "报错信息:" + e.getMessage());
            updateFridayMissionInfo(executeFridayMissionContext, fridayRequest, mcpMissionInfoByMission);
            t.setStatus(e);
            log.error("executeModelDeploymentTask error,missionId:{}", executeFridayMissionContext.getMissionId(), e);
            throw e;
        } finally {
            t.complete();
        }
    }

    private String getFridayModelDeployInfo(String modelDeployName, String misId) throws Exception {
        Map<String, String> param = new HashMap<>();
        param.put("tenantId", TENANT_ID);
        param.put("filterStr", modelDeployName);
        param.put("mine", "0");
        param.put("pageNum", "1");
        param.put("pageSize", "10");
        String result = HttpUtil.httpGet(FRIDAY_GET_MODEL_INFO, param, getFridayHeader(misId));
        if (StringUtils.isBlank(result)) {
            throw new Exception("Friday接口getFridayModelDeployInfo返回为空" + modelDeployName);
        }
        if (result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        log.info("getFridayModelDeployInfo,result:{}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        Object codeObj = jsonObject.get("code");
        if (!"0".equals(codeObj.toString())) {
            throw new Exception("Friday接口getFridayModelDeployInfo返回错误");
        }
        Map<String, Object> data = (Map<String, Object>) jsonObject.get("data");
        List<Map<String, Object>> maps = (List<Map<String, Object>>) data.get("services");
        for (Map<String, Object> map : maps) {
            if (modelDeployName.equals(map.get("serviceName"))) {
                return map.get("service_id").toString();
            }
        }
        throw new Exception("未找到模型信息");
    }

    public Boolean queryModelDeploymentStatus(String modelServiceId, ExecuteFridayMissionContext context) throws Exception {
        Cat.logEvent("QueryModelDeploymentStatus", "Begin.....missionId:" + context.getMissionId());
        Transaction t = Cat.newTransaction(FridayService.class.getSimpleName(), "QueryModelDeploymentStatus,missionId:" + context.getMissionId());
        try {
            int retryCount = 0;
            while (retryCount < Integer.parseInt(modelDeployTimeConfig.get("maxRetryCount"))) {
                String status = queryDeploymentStatusSfLargeModel(modelServiceId, context.getRequest().getMisId());
                if ("运行失败".equals(status)) {
                    throw new RuntimeException(String.format("模型部署失败,missionid:%s,modelName", context.getMissionId(), context.getModelDeployName()));
                } else if ("服务中".equals(status)) {
                    Cat.logEvent("pollQueryModelDeploymentStatus", "OK....missionId:" + context.getMissionId());
                    log.info("服务部署成功,missionId:{}", context.getMissionId());
                    return true;
                }
                log.info("服务部署中");
                retryCount++;
            }
            return false;
        } catch (Exception e) {
            t.setStatus(e);
            log.error("QueryModelDeploymentStatus error,missionId:{}，context:{}", context.getMissionId(), JSONObject.toJSONString(context), e);
            throw e;
        } finally {
            t.complete();
        }
    }

    private String buildFridayModelDeployUrl(String modelServiceId) {
        return String.format("https://friday.sankuai.com/ml/applicationCenter/create?id=%s&tab=configInfo", modelServiceId);
    }

    @Override
    public ExecuteFridayMissionContext executeModelRegistrationProcess(ExecuteFridayMissionRequest fridayRequest, ExecuteFridayMissionContext executeFridayMissionContext, McpMissionInfo mcpMissionInfoByMission) throws Exception {
        if (executeFridayMissionContext != null && executeFridayMissionContext.getModelRegStatus() == 1) {
            Cat.logEvent("executeModelRegistrationProcess", "模型注册任务已完成，missionId:" + executeFridayMissionContext.getMissionId());
            log.error("模型注册已完成,missionId:{}", executeFridayMissionContext.getMissionId());
            return executeFridayMissionContext;
        }
        Cat.logEvent("executeModelRegistrationProcess", String.format("missionId:%s", executeFridayMissionContext.getMissionId()));
        log.info("开始执行模型注册,missionId:{},context:{}", executeFridayMissionContext.getMissionId(), JSONObject.toJSONString(executeFridayMissionContext));
        Transaction t = Cat.newTransaction(FridayService.class.getSimpleName(), "executeModelRegistrationProcess" + "_" + executeFridayMissionContext.getMissionId());
        try {
            if (executeFridayMissionContext.getModelRegStatus() == 0) {
                return null;
            }
            executeFridayMissionContext.setModelRegStatus(0);
            executeFridayMissionContext.setStatus(0);
            executeFridayMissionContext.setExecuteMessage(String.format("正在执行模型注册,missionId:%s", executeFridayMissionContext.getMissionId()));
            updateFridayMissionInfo(executeFridayMissionContext, fridayRequest, mcpMissionInfoByMission);
            String trainingTaskId = executeFridayMissionContext.getTrainingId();
            String iter = findClosestIterFromFriday(TENANT_ID, trainingTaskId, fridayRequest.getTrainMethod(), fridayRequest.getMisId());
            executeFridayMissionContext.setIter(iter);
            String trainName = executeFridayMissionContext.getTrainingName();
            String modelDeployName = trainName.replace("Agent_Training_Mission_", "Agent_Model_") + "_" + iter;
            String registResult = performModelRegistration(modelDeployName, trainName, fridayRequest.getModelBaseName(), iter, trainingTaskId, fridayRequest.getMisId());
            executeFridayMissionContext.setModelRegName(modelDeployName);
            executeFridayMissionContext.setTrainingName(trainName);
            executeFridayMissionContext.setModelDeployName(modelDeployName);
            JSONObject registJson = JSONObject.parseObject(registResult);
            //注册失败，并且不是因为之前注册过
            if (!"0".equals(registJson.getString("code")) && !"模型名称重复:该模型名已有租户占用，请重新填写;".equals(registJson.getString("message"))) {
                Cat.logError(new RuntimeException(String.format("模型注册失败,input:%s", JSONObject.toJSONString(fridayRequest))));
                throw new RuntimeException(String.format(modelDeployName + "模型注册失败: " + registJson.getString("message")));
            }
            executeFridayMissionContext.setModelRegStatus(1);
            t.setSuccessStatus();
            executeFridayMissionContext.setExecuteMessage(String.format("模型注册成功,missionId:%s,接下来要执行模型部署", executeFridayMissionContext.getMissionId()));
            updateFridayMissionInfo(executeFridayMissionContext, fridayRequest, mcpMissionInfoByMission);
            return executeFridayMissionContext;
        } catch (Exception e) {
            executeFridayMissionContext.setStatus(-1);
            executeFridayMissionContext.setExecuteMessage("执行模型注册失败,missionId:" + executeFridayMissionContext.getMissionId() + "报错信息为：" + e.getMessage());
            executeFridayMissionContext.setModelRegStatus(-1);
            updateFridayMissionInfo(executeFridayMissionContext, fridayRequest, mcpMissionInfoByMission);
            t.setStatus(e);
            log.error("模型注册执行失败,missionId:{},context:{}", executeFridayMissionContext.getMissionId(), JSONObject.toJSONString(executeFridayMissionContext));
            throw e;
        } finally {
            t.complete();
        }
    }

    @Override
    public void executeModelTestProcess(ExecuteFridayMissionContext context, McpMissionInfo mcpMissionInfoByMission) throws Exception {
        if (context != null && context.getModelTestStatus() == 1) {
            Cat.logEvent("executeModelTestProcess", "模型测试任务已完成，missionId:" + context.getMissionId());
            log.error("模型测试已完成,missionId:{}", context.getMissionId());
            return;
        }
        Cat.logEvent("executeModelTestProcess", String.format("missionId:%s", context.getMissionId()));
        log.info("executeModelTestProcess,missionId:{},context:{}", context.getMissionId(), JSONObject.toJSONString(context));
        Transaction t = Cat.newTransaction("executeModelTestProcess", String.format("missionId:%s", context.getMissionId()));
        if (context.getModelTestStatus() == 0 && mcpMissionInfoByMission.getStatus() == 0) {
            log.info("正在执行模型测评，missionId:{}", mcpMissionInfoByMission.getMissionId());
            return;
        }
        ThreadPoolExecutor fridayMissionThreadPool = ThreadPoolFactory.getFridayMissionThreadPool();
        fridayMissionThreadPool.execute(() -> {
            try {
                context.setModelTestStatus(0);
                context.setExecuteMessage(String.format("正在执行模型测试,missionId:%s", context.getMissionId()));
                context.setStatus(0);
                updateFridayMissionInfo(context, context.getRequest(), mcpMissionInfoByMission);
                StringBuilder sb = new StringBuilder();
                String modelBaseName = context.getRequest().getModelBaseName();
                String chatOrText = modelBaseName.toLowerCase().contains("chat") ? "chat" : "text";
                context.setChatOrText(chatOrText);
                Map<String, String> dataMeta = s3Service.extractBucketAndObjectName(context.getRequest().getInputS3Url());
                String bucketName = dataMeta.get("bucketName");
                String objectName = dataMeta.get("objectName");
                s3Service.updateObjectHttpUTF8(bucketName, objectName);

                // 使用预处理+分批处理的方式处理S3文件
                processS3FileWithPreprocessing(context, bucketName, objectName, chatOrText, sb);
                //写入
                ByteArrayInputStream inputStream = new ByteArrayInputStream(sb.toString().getBytes());
                String result = s3Service.upload2S3(inputStream, "llm_evaluate_result_" + System.currentTimeMillis() + ".json", null);
                // 清理临时文件
                cleanupTemporaryFiles(context, result);
                JSONObject resultJson = new JSONObject();
                resultJson.put("result", result);
                context.setMissionResult(resultJson.toJSONString());
                log.info(String.format("executeModelTestProcess context:%s result:%s", JSONObject.toJSONString(context), result));
                context.setModelTestStatus(1);
                // 所有步骤成功完成，更新最终状态
                updateMisssionStatus(context);
                updateFridayMissionInfo(context, context.getRequest(), mcpMissionInfoByMission);
                Cat.logEvent("FridayMission", "任务执行成功，missionid:" + context.getMissionId());
                log.info("Friday任务执行成功，missionId:{},请求:{},结果{}", context.getMissionId(), JSONObject.toJSONString(context.getRequest()), JacksonUtil.toJsonStr(result));
                generalProcessingService.generalProcess(mcpMissionInfoByMission, context, result, context.getSessionId(), true);
                t.setSuccessStatus();
                mcpserverMissionDao.updateMissionInfo(mcpMissionInfoByMission);
            } catch (Exception e) {
                context.setExecuteMessage("执行模型测试失败，missionId:" + context.getMissionId() + "报错信息为:" + JSONObject.toJSONString(e));
                context.setModelTestStatus(-1);
                context.setStatus(-1);
                updateMisssionStatus(context);
                updateFridayMissionInfo(context, context.getRequest(), mcpMissionInfoByMission);
                t.setStatus(e);
                log.error("任务执行失败，missionId:{},请求:{},context:{}", context.getMissionId(), JSONObject.toJSONString(context.getRequest()), JSONObject.toJSONString(context), e);
                generalProcessingService.generalProcess(mcpMissionInfoByMission, context, null, context.getSessionId(), false);
                McpMissionInfo mcpMissionInfoByMissionByMissionId = mcpserverMissionDao.getMcpMissionInfoByMissionId(context.getMissionId());
                ExecuteFridayMissionContext executeFridayMissionContext = new ExecuteFridayMissionContext();
                if (mcpMissionInfoByMissionByMissionId != null) {
                    executeFridayMissionContext = JSONObject.parseObject(mcpMissionInfoByMissionByMissionId.getMissionContext(), ExecuteFridayMissionContext.class);
                }
                executeFridayMissionContext.setStatus(-1);
                log.error("executeFridayModelFactoryProcess error,request:{}", executeFridayMissionContext, e);
                if (mcpMissionInfoByMissionByMissionId == null) {
                    log.error("未找到mission信息,missionID:{},request:{}", context.getSessionId(), JSONObject.toJSONString(executeFridayMissionContext));
                }
                McpMissionInfo mcpMissionInfoByMissionByMissionId1 = buildFridayMissionInfo(executeFridayMissionContext, executeFridayMissionContext.getRequest(), mcpMissionInfoByMissionByMissionId);
                mcpserverMissionDao.updateMissionInfo(mcpMissionInfoByMissionByMissionId1);
            } finally {
                t.complete();
            }
        });
    }

    private McpMissionInfo buildFridayMissionInfo(ExecuteFridayMissionContext context, ExecuteFridayMissionRequest fridayRequest, McpMissionInfo fridayMissionInfo) {
        fridayMissionInfo.setUpdateTime(new Date());
        fridayMissionInfo.setMissionId(context.getMissionId());
        fridayMissionInfo.setStatus(context.getStatus());
        fridayMissionInfo.setMissionRequest(JSONObject.toJSONString(fridayRequest));
        fridayMissionInfo.setMissionResult(context.getMissionResult());
        fridayMissionInfo.setMissionContext(JSONObject.toJSONString(context));
        fridayMissionInfo.setMissionId(context.getMissionId());
        return fridayMissionInfo;
    }

    private void updateMisssionStatus(ExecuteFridayMissionContext context) {
        if (context.getTrainingTaskStatus() == 1
                && context.getModelDeployStatus() == 1
                && context.getModelRegStatus() == 1
                && context.getUploadDatasetStatus() == 1
                && context.getModelTestStatus() == 1) {
            context.setStatus(1);
            context.setExecuteMessage("Friday模型训练全流程执行成功,misionId:" + context.getMissionId());
        } else {
            context.setStatus(-1);
            context.setExecuteMessage("Friday模型训练全流程执行状态异常,misionId:" + context.getMissionId());
        }
    }

    /**
     * 使用预处理+分批处理的方式处理S3文件，确保数据完整性的同时避免连接超时问题
     *
     * @param context    执行上下文
     * @param bucketName S3桶名
     * @param objectName S3对象名
     * @param chatOrText 模型类型
     * @param sb         结果字符串构建器
     * @throws Exception 处理异常
     */
    private void processS3FileWithPreprocessing(ExecuteFridayMissionContext context, String bucketName,
                                                String objectName, String chatOrText, StringBuilder sb) throws Exception {
        // 第一步：预处理 - 先读取整个文件，提取所有完整的JSON行到临时文件
        List<String> jsonLines = new ArrayList<>();

        // 定义分块大小和重试参数
        final int MAX_RETRIES = 3;
        final int CHUNK_SIZE = 10 * 1024 * 1024; // 10MB
        final int READ_TIMEOUT = 30000; // 30秒

        // 获取S3对象的元数据
        com.amazonaws.services.s3.model.GetObjectMetadataRequest metadataRequest = new com.amazonaws.services.s3.model.GetObjectMetadataRequest(bucketName, objectName);
        com.amazonaws.services.s3.model.ObjectMetadata metadata = s3Service.getObjectMetadata(metadataRequest);
        long contentLength = metadata.getContentLength();
        log.info("S3文件大小: {} 字节, missionId: {}", contentLength, context.getMissionId());

        // 创建临时文件存储下载的内容
        File tempFile = File.createTempFile("s3download-", ".tmp");
        tempFile.deleteOnExit();

        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 分块下载文件
            long offset = 0;
            while (offset < contentLength) {
                long endByte = Math.min(offset + CHUNK_SIZE - 1, contentLength - 1);
                boolean chunkDownloaded = false;
                int retries = 0;

                while (!chunkDownloaded && retries < MAX_RETRIES) {
                    try {
                        // 创建带范围的请求
                        GetObjectRequest rangeRequest = new GetObjectRequest(bucketName, objectName)
                                .withRange(offset, endByte);

                        // 设置请求超时
                        com.amazonaws.ClientConfiguration clientConfig = new com.amazonaws.ClientConfiguration();
                        clientConfig.setSocketTimeout(READ_TIMEOUT);

                        try (S3Object s3ChunkObject = s3Service.getObject(rangeRequest);
                             InputStream chunkContent = s3ChunkObject.getObjectContent()) {

                            byte[] buffer = new byte[8192];
                            int bytesRead;
                            while ((bytesRead = chunkContent.read(buffer)) != -1) {
                                fos.write(buffer, 0, bytesRead);
                            }
                            fos.flush();
                            chunkDownloaded = true;
                            log.info("成功下载块 [{}-{}]/{}, missionId: {}",
                                    offset, endByte, contentLength, context.getMissionId());
                        }
                    } catch (Exception e) {
                        retries++;
                        log.warn("下载块 [{}-{}]/{} 失败, 重试 {}/{}, 错误: {}",
                                offset, endByte, contentLength, retries, MAX_RETRIES, e.getMessage());
                        if (retries >= MAX_RETRIES) {
                            throw new Exception("下载S3文件块失败，已达到最大重试次数: " + e.getMessage());
                        }
                        // 短暂等待后重试
                        Thread.sleep(1000);
                    }
                }

                offset = endByte + 1;
            }
        }

        // 从临时文件读取并解析JSON行
        try (BufferedReader reader = new BufferedReader(new FileReader(tempFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue; // 跳过空行
                }

                try {
                    // 验证是否为有效的JSON
                    ObjectMapper objectMapper = new ObjectMapper();
                    objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
                    objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
                    objectMapper.readValue(line, Map.class);

                    // 如果是有效的JSON，添加到列表
                    jsonLines.add(line);
                } catch (Exception e) {
                    log.warn("跳过无效的JSON行: {}", line);
                }
            }
        } finally {
            // 清理临时文件
            try {
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            } catch (Exception e) {
                log.warn("删除临时文件失败: {}", e.getMessage());
            }
        }

        log.info("成功读取{}条有效JSON记录，准备分批处理, missionId: {}", jsonLines.size(), context.getMissionId());

        // 第二步：分批处理JSON行，每批处理后重新建立连接
        int batchSize = 10; // 每批处理的记录数
        int totalBatches = (int) Math.ceil((double) jsonLines.size() / batchSize);

        // 用于收集异常
        Exception exceptionOccurred = null;
        // 用于收集所有批次的结果
        StringBuilder allResults = new StringBuilder();

        try {
            // 顺序处理每个批次
            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                int startIndex = batchIndex * batchSize;
                int endIndex = Math.min(startIndex + batchSize, jsonLines.size());
                StringBuilder batchResult = new StringBuilder();

                log.info("处理第{}/{}批数据, 范围: [{}, {}), missionId: {}",
                        batchIndex + 1, totalBatches, startIndex, endIndex,
                        context.getMissionId());

                // 处理当前批次的JSON行
                for (int i = startIndex; i < endIndex; i++) {
                    if (exceptionOccurred != null) {
                        break; // 如果发生异常，停止处理
                    }

                    String line = jsonLines.get(i);
                    try {
                        // 解析JSON
                        ObjectMapper objectMapper = new ObjectMapper();
                        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
                        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
                        objectMapper.configure(JsonParser.Feature.INCLUDE_SOURCE_IN_LOCATION, true);
                        Map<String, String> data = objectMapper.readValue(line, Map.class);

                        String input = "";
                        Object inputObj = data.get("input");
                        if (inputObj != null && inputObj instanceof List) {
                            Type type = new TypeToken<List<Map<String, Object>>>() {
                            }.getType();
                            List<Map<String, Object>> inputs = JSONObject.parseObject(JSONObject.toJSONString(data.get("input")), type);
                            if (CollectionUtils.isNotEmpty(inputs)) {
                                input = inputs.get(0).get("content").toString();
                            }
                        } else if (inputObj != null && inputObj instanceof String) {
                            input = inputObj.toString();
                        }
                        String target = "";
                        Object targetObj = data.get("target");
                        if (targetObj != null && targetObj instanceof Map) {
                            target = JSONObject.toJSONString(targetObj);
                        } else if (targetObj != null && targetObj instanceof String) {
                            target = targetObj.toString();
                        }
                        String rejected = "";
                        Object rejectedObj = data.get("rejected");
                        if (rejectedObj != null) {
                            rejected = rejectedObj.toString();
                        }

                        // 调用模型接口获取结果
                        Map<String, String> result = llmPredictService.singleCallLargeModel(
                                input, context.getModelDeployName(), context.getRequest().getModelParam(), chatOrText, context.getMissionId());

                        // 生成评测结果
                        Map<String, String> resultData = new HashMap<>();
                        putIfNotNull(resultData, "input", input);
                        putIfNotNull(resultData, "target", target);
                        putIfNotNull(resultData, "output", result.get("res"));
                        putIfNotNull(resultData, "usage", result.get("usage"));
                        putIfNotNull(resultData, "rejected", rejected);
                        batchResult.append(JacksonUtil.toJsonStr(resultData)).append("\n");

                        log.info("成功处理第{}条记录, 批次: {}/{}, missionId: {}",
                                i + 1, batchIndex + 1, totalBatches,
                                context.getMissionId());
                    } catch (Exception e) {
                        log.error("处理第{}条记录时异常: {}, missionId: {}",
                                i + 1, e.getMessage(), context.getMissionId(), e);
                        exceptionOccurred = e;
                        break;
                    }
                }

                // 将批次结果添加到总结果
                if (batchResult.length() > 0) {
                    allResults.append(batchResult);
                }

                // 如果有异常发生，停止处理后续批次
                if (exceptionOccurred != null) {
                    break;
                }
            }

            // 检查是否有异常发生
            if (exceptionOccurred != null) {
                throw exceptionOccurred;
            }

            // 将所有结果添加到输出
            sb.append(allResults);
        } catch (Exception e) {
            log.error("处理S3文件时发生异常, missionId: {}", context.getMissionId(), e);
            throw e;
        }

        log.info("S3文件处理完成，共处理{}条记录, missionId: {}", jsonLines.size(), context.getMissionId());
    }

    private void putIfNotNull(Map<String, String> map, String key, String value) {
        if (value != null && !value.trim().isEmpty()) {
            map.put(key, value);
        }
    }

    /**
     * 清理模型测试过程中产生的临时文件（如有）
     *
     * @param context 执行上下文
     * @param result  结果S3路径
     */
    private void cleanupTemporaryFiles(ExecuteFridayMissionContext context, String result) {
        try {
            // 获取临时目录路径
            String tempDir = System.getProperty("java.io.tmpdir");
            File dir = new File(tempDir);

            // 查找与当前任务相关的临时文件
            String missionId = context.getMissionId();
            File[] tempFiles = dir.listFiles((d, name) -> name.contains("llm_test_" + missionId));

            if (tempFiles != null && tempFiles.length > 0) {
                for (File file : tempFiles) {
                    if (file.delete()) {
                        log.info("成功删除临时文件: {}", file.getAbsolutePath());
                    } else {
                        log.warn("无法删除临时文件: {}", file.getAbsolutePath());
                    }
                }
                log.info("已清理{}个与任务{}相关的临时文件", tempFiles.length, missionId);
            } else {
                log.info("未找到与任务{}相关的临时文件", missionId);
            }
        } catch (Exception e) {
            log.error("清理临时文件时发生异常", e);
        }
        log.info("cleanupTemporaryFiles called for missionId: {}, result: {}", context.getMissionId(), result);
    }

    public String queryDeploymentStatusSfLargeModel(String appId, String misId) throws Exception {
        Map<String, String> param = new HashMap<>();
        param.put("tenantId", TENANT_ID);
        param.put("appId", appId);
        String result = HttpUtil.httpGet(FRIDAY_MODEL_STATUS_URL, param, getFridayHeader(misId));
        if (result != null && result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        Object dataJson = jsonObject.get("data");
        Map map = JSONObject.parseObject(dataJson.toString(), Map.class);
        if (map == null || !map.containsKey("status")) {
            throw new Exception("模型状态查询失败");
        }
        String status = map.get("status").toString();
        return status;
    }

    private Map<String, String> getFridayHeader(String misId) {
        // String ssoId = SsoUtil.getSsoId();
        Map<String, String> header = new HashMap<>();
        String ssoid = null;
        if (!StringUtils.isBlank(misId)) {
            Cat.logEvent("getFridayHeader", "misId");
            ssoid = "12d702aa62_ssoid=" + tairClient.get(FRIDAY_COOKIE_CACHE_FREFIX + misId);
        }
        if (StringUtils.isBlank(ssoid)) {
            ssoid = fridaySsoId;
        }
        if (ssoid == null) {
            throw new RuntimeException("ssoid获取失败，misId:" + misId);
        }
        header.put("Cookie", ssoid);
        header.put("mt-project", TENANT_ID);
        return header;
    }

    /**
     * 查询模型是否已经部署
     *
     * @param modelName 模型名称
     * @return 如果已部署返回true，否则返回false
     */
    private boolean isModelDeployed(String modelName, String misId) throws Exception {
        try {
            Map<String, String> param = new HashMap<>();
            param.put("tenantId", TENANT_ID);
            param.put("filterStr", "");
            param.put("mine", "0");
            param.put("pageNum", "1");
            param.put("pageSize", "100");

            String result = HttpUtil.httpGet(FRIDAY_DEPLOY_LIST_URL, param, getFridayHeader(misId));
            if (result != null && result.contains("统一登录中心")) {
                throw new Exception("Friday ssoId信息过期，请更新");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("查询部署列表失败: {}", result);
                throw new RuntimeException("查询部署列表失败");
            }

            // 获取服务列表
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null || data.getJSONArray("services") == null) {
                throw new RuntimeException("查询部署列表失败");
            }

            // 遍历服务列表查找模型
            List<Map<String, Object>> services = JSONObject.parseArray(data.getJSONArray("services").toJSONString())
                    .stream()
                    .map(obj -> (Map<String, Object>) obj)
                    .toList();
            for (Map<String, Object> service : services) {
                if (modelName.equals(service.get("model_name"))) {
                    log.info("模型已部署");
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("查询模型部署状态异常", e);
            throw e;
        }

    }

    /**
     * 获取训练任务的appId
     *
     * @param taskId 任务ID
     * @return appId
     */
    private String getTrainingTaskAppId(String taskId, String misId) throws Exception {
        try {
            Map<String, String> param = new HashMap<>();
            param.put("tenantId", TENANT_ID);
            param.put("taskID", taskId);

            String result = HttpUtil.httpGet(FRIDAY_METRICS_LINK_URL, param, getFridayHeader(misId));
            if (result != null && result.contains("统一登录中心")) {
                throw new Exception("Friday ssoId信息过期，请更新");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务appId失败: {}", result);
                throw new RuntimeException("获取训练任务appId失败");
            }

            // 获取data
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                throw new RuntimeException("获取训练任务appId失败");
            }

            // 获取appId
            String appId = data.getString("appID");
            if (StringUtils.isBlank(appId)) {
                throw new RuntimeException("获取appId失败");
            }

            return appId;

        } catch (Exception e) {
            log.error("获取训练任务appId异常", e);
            throw e;
        }
    }

    /**
     * 获取训练任务的mlpRunId
     *
     * @param jobIds 任务ID
     * @return mlpRunId
     */
    private String getMlpRunId(String jobIds, String misId) {
        try {
            // 构建请求参数
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("jobIds", jobIds);

            String result = HttpUtil.httpPostJson(MLP_JOB_BASIC_URL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(misId));
            if (result != null && result.contains("统一登录中心")) {
                throw new Exception("Friday ssoId信息过期，请更新");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务mlpRunId失败: {}", result);
                throw new RuntimeException("获取训练任务mlpRunId失败");
            }

            // 获取data并直接返回第一个key
            JSONObject dataObj = jsonObject.getJSONObject("data");
            if (dataObj == null || dataObj.isEmpty()) {
                throw new RuntimeException("获取训练任务mlpRunId失败");
            }

            return dataObj.keySet().iterator().next();

        } catch (Exception e) {
            log.error("获取训练任务mlpRunId异常", e);
            throw new RuntimeException("获取训练任务mlpRunId异常");
        }
    }

    /**
     * 获取训练任务的rankId
     *
     * @param jobIds 任务ID
     * @return rankId
     */
    private Integer getRankId(String jobIds, String misId) {
        try {
            // 构建请求参数
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("jobIds", jobIds);

            String result = HttpUtil.httpPostJson(MLP_JOB_RANK_URL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(misId));
            if (result != null && result.contains("统一登录中心")) {
                throw new Exception("Friday ssoId信息过期，请更新");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务rankId失败: {}", result);
                throw new RuntimeException("获取训练任务rankId失败");
            }

            // 获取data
            JSONArray data = jsonObject.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                throw new RuntimeException("获取训练任务rankId失败");
            }

            // 获取seriesRankNameInfo
            JSONArray seriesRankNameInfo = data.getJSONObject(0).getJSONArray("seriesRankNameInfo");
            if (seriesRankNameInfo == null || seriesRankNameInfo.isEmpty()) {
                throw new RuntimeException("获取rankId失败");
            }

            // 获取rankId
            Integer rankId = seriesRankNameInfo.getJSONObject(0).getInteger("rankId");
            if (rankId == null) {
                throw new RuntimeException("获取rankId失败");
            }

            return rankId;

        } catch (Exception e) {
            log.error("获取训练任务rankId异常", e);
            throw new RuntimeException("获取训练任务rankId异常");
        }
    }

    /**
     * 获取训练任务的step
     *
     * @param instanceId 实例ID
     * @param jobId      任务ID
     * @param rankId     排名ID
     * @return step
     */
    public String getIter(String instanceId, String jobId, Integer rankId, String trainMethod, String misId) throws Exception {
        try {
            // 构建请求参数
            Map<String, Object> rankIds = new HashMap<>();
            rankIds.put(jobId, Collections.singletonList(rankId));

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("sceneId", 1);
            requestBody.put("instanceId", instanceId);
            requestBody.put("rankIds", rankIds);

            String result = HttpUtil.httpPostJson(MLP_SERIES_SUMMARY_URL, JacksonUtil.toJsonStr(requestBody), getFridayHeader(misId));
            if (result != null && result.contains("统一登录中心")) {
                throw new Exception("Friday ssoId信息过期，请更新");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务step失败: {}", result);
                throw new RuntimeException("获取训练任务step失败");
            }

            // 获取data
            JSONArray data = jsonObject.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                throw new RuntimeException("获取训练任务step失败");
            }

            // 获取summary数组
            JSONArray summary = data.getJSONObject(0).getJSONArray("summary");
            if (summary == null || summary.isEmpty()) {
                throw new RuntimeException("获取summary失败");
            }

            // 根据trainMethod查找对应tag的数据，获取step
            for (int i = 0; i < summary.size(); i++) {
                JSONObject item = summary.getJSONObject(i);
                String tag = item.getString("tag");

                if ("sft".equals(trainMethod) && "lm loss validation".equals(tag)) {
                    // sft方法：获取min的step
                    String stepStr = item.getJSONObject("min").getString("step");
                    Integer step = parseStep(stepStr);
                    return String.format("iter_%07d", step);
                } else if ("dpo".equals(trainMethod) && "eval_accuracy".equals(tag)) {
                    // dpo方法：获取max的step
                    String stepStr = item.getJSONObject("max").getString("step");
                    Integer step = parseStep(stepStr);
                    return String.format("iter_%07d", step);
                }
            }
            return null;

        } catch (Exception e) {
            log.error("获取训练任务step异常", e);
            throw e;
        }
    }

    /**
     * 将step字符串转换为数字
     *
     * @param stepStr step字符串，可能是"1500"或"1.5K"或"1.5M"这样的格式
     * @return 转换后的数字
     */
    private Integer parseStep(String stepStr) {
        try {
            if (stepStr == null || stepStr.trim().isEmpty()) {
                throw new RuntimeException("step为空");
            }

            stepStr = stepStr.trim();
            // 检查是否是纯数字
            if (stepStr.matches("^\\d+$")) {
                return Integer.parseInt(stepStr);
            }

            // 检查是否符合带单位的格式 (数字+K或M)
            if (!stepStr.matches("^\\d*\\.?\\d+[KM]$")) {
                throw new RuntimeException("step格式错误，只支持纯数字、K或M单位格式");
            }

            double value = Double.parseDouble(stepStr.substring(0, stepStr.length() - 1));
            char unit = stepStr.charAt(stepStr.length() - 1);

            switch (unit) {
                case 'K':
                    return (int) (value * 1000);
                case 'M':
                    return (int) (value * 1000000);
                default:
                    throw new RuntimeException("不支持的单位格式");
            }
        } catch (Exception e) {
            log.error("解析step异常: {}", stepStr, e);
            throw new RuntimeException("解析step异常: " + e.getMessage());
        }
    }

    /**
     * 从Friday API获取训练任务详情并查找最接近的iter
     *
     * @param tenantId 租户ID
     * @param taskId   任务ID
     * @return 最接近的iter字符串，如果没找到则返回null
     */
    public String findClosestIterFromFriday(String tenantId, String taskId, String trainMethod, String misId) {
        try {
            String appId = getTrainingTaskAppId(taskId, misId);
            String runId = getMlpRunId(appId, misId);
            Integer rankId = getRankId(appId, misId);
            String targetIter = getIter(runId, appId, rankId, trainMethod, misId);
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("tenantId", tenantId);
            params.put("taskID", taskId);

            // 发送请求获取任务详情
            String result = HttpUtil.httpGet(FRIDAY_TASK_DETAIL_URL, params, getFridayHeader(misId));
            if (result != null && result.contains("统一登录中心")) {
                throw new Exception("Friday ssoId信息过期，请更新");
            }
            JSONObject jsonObject = JSONObject.parseObject(result);

            // 检查请求是否成功
            if (!"0".equals(jsonObject.getString("code"))) {
                log.error("获取训练任务详情失败: {}", result);
                throw new RuntimeException("获取训练任务详情失败");
            }

            // 获取artifacts数组
            JSONArray artifacts = jsonObject.getJSONObject("data").getJSONArray("artifacts");
            if (artifacts == null || artifacts.isEmpty()) {
                throw new RuntimeException("获取artifacts失败或artifacts为空");
            }
            if (StringUtils.isBlank(targetIter)) {
                JSONObject artifact = artifacts.getJSONObject(0);
                String step = artifact.getString("step");
                Cat.logEvent("getIter", String.format("未找到符合条件的iter,instanceId:%s,jobId:%s,rankid:%s,trainMethod:%s", runId, appId, rankId, trainMethod));
                return step;
            }

            // 从targetIter中提取数字
            int targetStep = Integer.parseInt(targetIter.substring(5));

            // 如果完全匹配，直接返回
            for (int i = 0; i < artifacts.size(); i++) {
                JSONObject artifact = artifacts.getJSONObject(i);
                String step = artifact.getString("step");
                if (targetIter.equals(step)) {
                    return step;
                }
            }

            // 没有完全匹配，查找最接近的
            String closestIter = null;
            int minDiff = Integer.MAX_VALUE;

            for (int i = 0; i < artifacts.size(); i++) {
                JSONObject artifact = artifacts.getJSONObject(i);
                String step = artifact.getString("step");
                int currentStep = Integer.parseInt(step.substring(5));

                int diff = Math.abs(currentStep - targetStep);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestIter = step;
                }
            }
            log.info("没有完全匹配的iter，获取closestIter: {}", closestIter);
            return closestIter;

        } catch (Exception e) {
            log.error("查找最接近的iter异常,tenantId:{},taskId:{},trainMethod:{} ", tenantId, taskId, trainMethod, e);
            return null;
        }
    }

    @Override
    public String uploadTrainingDatasetToFriday(CustomEvalModel customEvalModel, ExecuteFridayMissionRequest request) throws Exception {
        if (customEvalModel == null) {
            return "参数为空";
        }
        if (StringUtils.isBlank(customEvalModel.getUserS3Url())) {
            return "S3链接为空，请提供文件S3地址";
        }
        if (StringUtils.isBlank(customEvalModel.getName())) {
            customEvalModel.setName("AgentLoad" + "_" + System.currentTimeMillis());
        }
        if ("dpo".equals(request.getTrainMethod())) {
            customEvalModel.setPromptType("default");
        }
        Map<String, String> fridayHeader = getFridayHeader(request.getMisId());
        fridayHeader.put("mt-project", TENANT_ID);
        String result = HttpUtil.httpPostFormUrlencoded(FRIDAY_UPLOAD_TRAING_DATA_SET, JacksonUtil.toJsonStr(customEvalModel), fridayHeader);
        if (result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        return result;
    }

    @Override
    public void createTrainingTask(ExecuteFridayMissionContext executeFridayMissionContext, ExecuteFridayMissionRequest request, McpMissionInfo mcpMissionInfoByMission) throws Exception {
        if (executeFridayMissionContext != null && executeFridayMissionContext.getTrainingTaskStatus() == 1) {
            Cat.logEvent("createTrainingTask", String.format("训练任务已执行,missionId:%s", executeFridayMissionContext.getMissionId()));
            log.info("训练任务已执行完毕,missionId:{}", executeFridayMissionContext.getMissionId());
            return;
        }
        log.info("执行训练任务,ExecuteFridayMissionRequest:{},missionId:{},executeFridayMissionContext:{}", JSONObject.toJSONString(request), executeFridayMissionContext.getMissionId(), JSONObject.toJSON(executeFridayMissionContext));
        Cat.logEvent("createTrainingTask", String.format("执行训练任务,ExecuteFridayMissionRequest:%s, missionId:%s", JSONObject.toJSONString(request), executeFridayMissionContext.getMissionId()));
        Transaction t = Cat.newTransaction(FridayService.class.getSimpleName(), "createTrainingTask" + "_" + executeFridayMissionContext.getMissionId());
        String taskId = null;
        try {
            if (!StringUtils.isBlank(executeFridayMissionContext.getTrainingId()) && mcpMissionInfoByMission.getStatus() == 0 && (executeFridayMissionContext.getTrainingTaskStatus() == 0 || executeFridayMissionContext.getTrainingTaskStatus() == -1) && executeFridayMissionContext.getModelType() != null) {
                String status = queryTrainingTaskStatus(executeFridayMissionContext.getTrainingId(), request.getMisId());
                if (status.equals("运行成功")) {
                    executeFridayMissionContext.setTrainingTaskStatus(1);
                    executeFridayMissionContext.setExecuteMessage(String.format("模型训练成功,missionId:%s,接下来要执行模型注册", executeFridayMissionContext.getMissionId()));
                    updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
                    log.info("执行训练任务成功,missionId:{}，traningId:{}", executeFridayMissionContext.getMissionId(), executeFridayMissionContext.getTrainingId());
                }
            } else {
                String taskName = generateUniqueTrainingTaskName();
                executeFridayMissionContext.setTrainingTaskStatus(0);
                executeFridayMissionContext.setStatus(0);
                executeFridayMissionContext.setTrainingName(taskName);
                executeFridayMissionContext.setExecuteMessage(String.format("正在执行模型训练,missionId:%s", executeFridayMissionContext.getMissionId()));
                updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
                if (request == null) {
                    throw new Exception("参数为空");
                }
                ModelTraningReq modelTraningReq = new ModelTraningReq();
                String modelName = request.getModelBaseName();
                if (StringUtils.isBlank(modelName)) {
                    throw new Exception("modelName为空");
                }

                modelTraningReq.setModelName(modelName);
                modelTraningReq.setTaskName(taskName);
                executeFridayMissionContext.setTrainingName(taskName);
                String stfType = getStfType(request.getModelBaseName(), request.getMisId());
                modelTraningReq.setSftType(stfType);
                if (!CollectionUtils.isEmpty(request.getModelTrainDefaultParams())) {
                    modelTraningReq.setAdvancedParams(false);
                    Map<String, Object> modelTrainDefaultParams = request.getModelTrainDefaultParams();
                    Object devRatio = modelTrainDefaultParams.get("devRatio");
                    modelTrainDefaultParams.put("devRatio", devRatio.toString());
                    modelTraningReq.setModelParams(modelTrainDefaultParams);
                } else {
                    Map<String, Object> modelAdvancedParams = getModelAdvancedParams(request.getModelBaseName(), request.getModelTrainAdvancedParams(), request.getMisId());
                    modelTraningReq.setModelAdvancedParams(modelAdvancedParams);
                    modelTraningReq.setAdvancedParams(true);
                }
                Integer modelType = getModelType(request.getTrainMethod(), request.getModelBaseName(), request.getMisId());
                if (modelType == null) {
                    throw new Exception(String.format("模型%s训练方法%s未匹配到", request.getModelBaseName(), request.getTrainMethod()));
                }
                executeFridayMissionContext.setModelType(modelType);
                Datasets datasets = buildDatasetInfo(executeFridayMissionContext, modelType, request.getTrainMethod(), request.getMisId());
                if (datasets == null) {
                    throw new Exception(String.format("数据集%s未匹配到", executeFridayMissionContext.getDatasetId()));
                }

                modelTraningReq.setDatasetsObject(datasets);
                modelTraningReq.setTrainMethod(request.getTrainMethod());
                String result = HttpUtil.httpPostJson(FRIDAY_CREATE_TRAINING_TASK, JacksonUtil.toJsonStr(modelTraningReq), getFridayHeader(request.getMisId()));
                log.info("执行模型训练任务！result:{},missionId:{}", result, executeFridayMissionContext.getMissionId());
                if (result != null && result.contains("统一登录中心")) {
                    throw new Exception("Friday ssoId信息过期，请更新");
                }
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject.getInteger("code") != 0) {
                    throw new Exception(jsonObject.getString("message"));
                }
                taskId = jsonObject.get("data").toString();
                if (StringUtils.isBlank(taskId)) {
                    throw new Exception("taskId为空,missionId:" + executeFridayMissionContext.getMissionId());
                }
                executeFridayMissionContext.setTrainingId(taskId);
                updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
            }
        } catch (Exception e) {
            executeFridayMissionContext.setStatus(-1);
            executeFridayMissionContext.setExecuteMessage("执行创建训练任务失败,missionId:" + executeFridayMissionContext.getMissionId() + "报错信息：" + e.getMessage());
            executeFridayMissionContext.setTrainingTaskStatus(-1);
            if (executeFridayMissionContext.getTrainingId() == null && taskId != null) {
                executeFridayMissionContext.setTrainingId(taskId);
            }
            updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
            log.error("执行训练任务失败,ExecuteFridayMissionRequest:{},missionId:{},executeFridayMissionContext:{}", JSONObject.toJSONString(request), executeFridayMissionContext.getMissionId(), JSONObject.toJSON(executeFridayMissionContext), e);
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
        }

    }


    private Datasets buildDatasetInfo(ExecuteFridayMissionContext executeFridayMissionContext, Integer modelType, String trainMethod, String misId) throws Exception {

        Map<String, String> fridayHeader = getFridayHeader(misId);
        fridayHeader.put("mt-project", TENANT_ID);

        //String dataSets = HttpUtil.httpGetWithOutTime(FRIDAY_CREATE_TARINING_TASK_GET_DATASET, ImmutableMap.of("subType", "train", "modelType", String.valueOf(modelType), "promptType", promptType, "trainMethod", trainMethod), fridayHeader, 6);
        String datasetName = executeFridayMissionContext.getDatasetName();
        if (StringUtils.isBlank(datasetName)) {
            log.error("数据集名称为空，无法查询数据集信息，missionId:{}", executeFridayMissionContext.getMissionId());
            throw new Exception("数据集名称为空，无法查询数据集信息");
        }
        String dataSets = HttpUtil.httpGetWithOutTime(FRIDAY_CREATE_TRAINING_TASK_GET_DATASET_VERSION, ImmutableMap.of("subType", "train", "onlyOwner", "false", "query", datasetName, "pageNum", "1", "pageSize", "50"), fridayHeader, 6);
        if (StringUtils.isBlank(dataSets) || dataSets.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }

        JSONObject dataObj = JSONObject.parseObject(dataSets).getJSONObject("data");
        if (dataObj == null) {
            return null;
        }
        JSONArray itemArray = dataObj.getJSONArray("item");
        if (itemArray == null) {
            return null;
        }

        List<Map<String, Object>> dataSetsList = new ArrayList<>();
        for (int i = 0; i < itemArray.size(); i++) {
            JSONObject item = itemArray.getJSONObject(i);
            Map<String, Object> itemMap = new HashMap<>();
            for (String key : item.keySet()) {
                itemMap.put(key, item.get(key));
            }
            dataSetsList.add(itemMap);
        }
        Datasets dataset = getDataset(dataSetsList, executeFridayMissionContext.getDatasetId(), executeFridayMissionContext.getVersionList(), fridayHeader);
//        if ("default".equals(promptType)) {
//            dataset.setPromptType("default");
//        }
        if (dataset != null) {
            return dataset;
        }

        // 再次检查数据集名称是否为空
        datasetName = executeFridayMissionContext.getDatasetName();
        if (StringUtils.isBlank(datasetName)) {
            log.error("数据集名称为空，无法查询数据集信息，missionId:{}", executeFridayMissionContext.getMissionId());
            throw new Exception("数据集名称为空，无法查询数据集信息");
        }

        String dataSets2 = HttpUtil.httpGetWithOutTime(FRIDAY_CREATE_TRAINING_TASK_GET_DATASET_VERSION, ImmutableMap.of("subType", "train", "onlyOwner", "false", "query", datasetName, "pageNum", "1", "pageSize", "50"), fridayHeader, 6);
        //String dataSets2 = HttpUtil.httpGetWithOutTime(FRIDAY_CREATE_TARINING_TASK_GET_DATASET, ImmutableMap.of("subType", "train", "modelType", String.valueOf(2), "promptType", promptType, "trainMethod", trainMethod), fridayHeader, 6);
        if (dataSets2 != null && dataSets2.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        JSONObject dataObj2 = JSONObject.parseObject(dataSets2).getJSONObject("data");
        if (dataObj2 == null) {
            return null;
        }
        JSONArray itemArray2 = dataObj2.getJSONArray("item");
        if (itemArray2 == null) {
            return null;
        }

        List<Map<String, Object>> dataSetsListModelType2 = new ArrayList<>();
        for (int i = 0; i < itemArray2.size(); i++) {
            JSONObject item = itemArray2.getJSONObject(i);
            Map<String, Object> itemMap = new HashMap<>();
            for (String key : item.keySet()) {
                itemMap.put(key, item.get(key));
            }
            dataSetsListModelType2.add(itemMap);
        }
        if (dataSetsListModelType2 == null) {
            return null;
        }
        Datasets dataset2 = getDataset(dataSetsListModelType2, executeFridayMissionContext.getDatasetId(), executeFridayMissionContext.getVersionList(), fridayHeader);
        if (dataset2 != null) {
            return dataset2;
        }
        return null;
    }


    private Datasets getDataset(List<Map<String, Object>> list, Long datasetId, List<Integer> versionList, Map<String, String> fridayHeader) throws Exception {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        String promptType = null;
        // 第一步：从数据集列表中查找匹配ID的数据集
        Map<String, Object> targetDataset = null;
        for (Map<String, Object> datasetItem : list) {
            // 注意：这里处理的是新API返回结构
            if (datasetItem.get("id") == null) {
                throw new Exception("数据集id为空");
            }
            if (datasetId.toString().equals(datasetItem.get("id").toString())) {
                targetDataset = datasetItem;
                promptType = datasetItem.get("promptType").toString();
                break;
            }
        }

        if (targetDataset == null) {
            log.info("未找到ID为{}的数据集", datasetId);
            return null;
        }

        // 第二步：获取该数据集的版本详情
        Map<String, String> params = ImmutableMap.of(
                "id", datasetId.toString(),
                "pageNum", "1",
                "pageSize", "100"
        );
        String versionJson = HttpUtil.httpGet(FRIDAYUPLOAD_TRAING_DATA_SET_QUERY_STATUS, params, fridayHeader);

        if (versionJson == null || versionJson.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }

        JSONObject versionObject = JSONObject.parseObject(versionJson);
        if (!"0".equals(versionObject.getString("code"))) {
            log.error("获取数据集版本信息失败: {}", versionObject.getString("message"));
            return null;
        }

        // 获取版本列表
        JSONArray itemArray = ((JSONObject) versionObject.get("data")).getJSONArray("item");
        List<Map<String, Object>> versionItems = new ArrayList<>();
        for (int i = 0; i < itemArray.size(); i++) {
            JSONObject item = itemArray.getJSONObject(i);
            Map<String, Object> itemMap = new HashMap<>();
            for (String key : item.keySet()) {
                itemMap.put(key, item.get(key));
            }
            versionItems.add(itemMap);
        }
        if (CollectionUtils.isEmpty(versionItems)) {
            log.info("数据集{}没有版本信息", datasetId);
            return null;
        }

        // 第三步：匹配需要的版本并构建返回对象
        List<Datasets.DatasetInfo> datasetInfos = new ArrayList<>();
        for (Map<String, Object> versionItem : versionItems) {
            Integer version = Integer.valueOf(versionItem.get("version").toString());
            if (versionList.contains(version)) {
                Datasets.DatasetInfo datasetInfo = new Datasets.DatasetInfo();
                datasetInfo.setDatasetName((String) targetDataset.get("name"));
                datasetInfo.setDatasetVersion(versionItem.get("version").toString());
                datasetInfo.setDataNum(Integer.valueOf(versionItem.get("size").toString()));
                datasetInfo.setDatasetId(datasetId);
                datasetInfos.add(datasetInfo);
            }
        }

        if (datasetInfos.isEmpty()) {
            log.info("数据集{}没有匹配的版本", datasetId);
            return null;
        }

        Datasets datasets = new Datasets();
        datasets.setPromptType(promptType);
        datasets.setDatasetList(datasetInfos);

        return datasets;
    }

    private Integer getModelType(String trainMethod, String modelBaseName, String misId) throws Exception {
        String modelinformations = HttpUtil.httpGet(FRIDAY_CREATE_TRAINING_TASK_GET_MODEL_INFORMATION, ImmutableMap.of("trainMethod", trainMethod), getFridayHeader(misId));
        if (modelinformations != null && modelinformations.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        log.info("trainMethod:{},modelBaseName:{},getModelType:{}", trainMethod, modelBaseName, modelinformations);
        Type type = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        List<Map<String, Object>> modelinformationList = JSONObject.parseObject(JSONObject.parseObject(modelinformations).get("data").toString(), type);
        if (modelinformationList == null) {
            return null;
        }
        Integer modelType = null;
        for (Map<String, Object> modelInformationItem : modelinformationList) {
            if (modelBaseName.equals(modelInformationItem.get("name"))) {
                Object modelTypeObj = modelInformationItem.get("modelType");
                modelType = (Integer) modelTypeObj;
                break;
            }
        }
        if (modelType == null) {
            return null;
        }
        return modelType;
    }

    private Map<String, Object> getModelAdvancedParams(String modelName, Map<String, Object> requestModelAdvancedParams, String misId) throws Exception {
        String getAdvancedParamsResult = HttpUtil.httpGet(FRIDAY_CREATE_TRAINING_TASK_GET_ADVANCED_PARAMS, new HashMap<>(), getFridayHeader(misId));
        if (getAdvancedParamsResult != null && getAdvancedParamsResult.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        List<Map<String, Object>> modelAdvancedParams = (List<Map<String, Object>>) JSONObject.parseObject(getAdvancedParamsResult).get("data");
        if (modelAdvancedParams == null) {
            return null;
        }
        Map<String, Object> mergedModelAdvancedParams = new ConcurrentHashMap<>();
        for (Map<String, Object> modelAdvancedParam : modelAdvancedParams) {
            if (modelName.equals(modelAdvancedParam.get("modelType"))) {
                mergedModelAdvancedParams = (Map<String, Object>) modelAdvancedParam.get("params");
                break;
            }
        }
        boolean containSplicing = requestModelAdvancedParams.containsKey("splicing");
        boolean containSplicingWeight = requestModelAdvancedParams.containsKey("splicing-weight");
        if (!containSplicing && mergedModelAdvancedParams.containsKey("splicing")) {
            mergedModelAdvancedParams.remove("splicing");
        }
        if (!containSplicingWeight && mergedModelAdvancedParams.containsKey("splicing-weight")) {
            mergedModelAdvancedParams.remove("splicing-weight");
        }
        mergedModelAdvancedParams.putAll(requestModelAdvancedParams);
        return mergedModelAdvancedParams;
    }

    private String getStfType(String modelName, String misId) {
        try {
            String getStfTypeResult = HttpUtil.httpGet(FRIDAY_CREATE_TRAINING_TASK_GET_STF_TYPE, ImmutableMap.of("modelName", modelName), getFridayHeader(misId));
            if (getStfTypeResult != null && getStfTypeResult.contains("统一登录中心")) {
                throw new Exception("Friday ssoId信息过期，请更新");
            }
            List<String> stfTypeData = (List<String>) JSONObject.parseObject(getStfTypeResult).get("data");
            if (stfTypeData == null) {
                return "获取stfType失败";
            }
            return stfTypeData.get(0);
        } catch (Exception e) {
            
            return "fullParameter";
        }

    }

    @Override
    public String queryTrainingTaskStatus(String taskId, String misId) throws Exception {
        if (StringUtils.isBlank(taskId)) {
            return "taskId为空";
        }
        String result = HttpUtil.httpGet(FRIDAY_CREATE_TRAINING_TASK_STATUS_QUERY, ImmutableMap.of("taskID", taskId, "tenantId", TENANT_ID), getFridayHeader(misId));
        if (result != null && result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        JSONObject jsonObject = JSONObject.parseObject(result);
        JSONObject dataObj = jsonObject.getJSONObject("data");
        Object status = dataObj != null ? dataObj.get("status") : null;
        if (status == null) {
            return "查询失败";
        }
        return status.toString();
    }


    public String queryDatasetUploadStatus(Long datasetId, Integer version, String misId) throws Exception {
        if (datasetId == null) {
            return "datasetId为空";
        }
        Map<String, String> fridayHeader = getFridayHeader(misId);
        fridayHeader.put("mt-project", TENANT_ID);
        String result = HttpUtil.httpGet(FRIDAYUPLOAD_TRAING_DATA_SET_QUERY_STATUS,
                ImmutableMap.of("id", String.valueOf(datasetId), "pageNum", "1", "pageSize", "10"),
                fridayHeader);
        if (result != null && result.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }

        JSONObject jsonObject = JSONObject.parseObject(result);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray items = data.getJSONArray("item");

        if (items == null || items.isEmpty()) {
            return null;
        }
        if (version != null && version > 0) {
            for (int i = 0; i < items.size(); i++) {
                JSONObject item = items.getJSONObject(i);
                if (version.toString().equals(item.getString("version"))) {
                    return item.getString("status");
                }
            }
        }
        return null;
    }

    @Override
    public ExecuteFridayMissionContext uploadModelTraningMissionDataset(ExecuteFridayMissionRequest request, ExecuteFridayMissionContext executeFridayMissionContext, McpMissionInfo mcpMissionInfoByMission) throws Exception {
        if (executeFridayMissionContext != null && executeFridayMissionContext.getUploadDatasetStatus() == 1) {
            Cat.logEvent("uploadModelTraningMissionDataset", String.format("missionid：%s 的上传数据集已完成", executeFridayMissionContext.getMissionId()));
            log.info("missionid：{} 的上传数据集已完成", executeFridayMissionContext.getMissionId());
            return executeFridayMissionContext;
        }
        log.info("发起上传数据集任务,ExecuteFridayMissionRequest:{},missionId:{}", JSONObject.toJSONString(request), executeFridayMissionContext.getMissionId());
        Cat.logEvent("uploadModelTraningMissionDataset", String.format("missionId:%s", executeFridayMissionContext.getMissionId()));
        Transaction t = Cat.newTransaction(FridayService.class.getSimpleName(), "uploadModelTraningMissionDataset" + "_" + executeFridayMissionContext.getMissionId());
        try {
            if (mcpMissionInfoByMission.getStatus() == 0 && executeFridayMissionContext.getStatus() == 0 && executeFridayMissionContext.getUploadDatasetStatus() == 0) {
                // 确保版本列表不为空
                if (executeFridayMissionContext.getVersionList().isEmpty()) {
                    executeFridayMissionContext.getVersionList().add(1);
                }
                // 对版本列表进行排序和去重，并更新回context
                List<Integer> sortedVersions = executeFridayMissionContext.getVersionList().stream().distinct().sorted().collect(Collectors.toList());

                executeFridayMissionContext.setVersionList(sortedVersions);

                if (checkUploadDataSetVersionStatus(executeFridayMissionContext, request.getMisId())) {
                    executeFridayMissionContext.setUploadDatasetStatus(1);
                    log.info("uploadModelTraningMissionDataset OK ,request:{}，missionId:{},context:{}", JSONObject.toJSONString(request), executeFridayMissionContext.getMissionId(), JSONObject.toJSON(executeFridayMissionContext));
                    executeFridayMissionContext.setExecuteMessage(String.format("数据集上传成功,missionId:%s,接下来要执行模型训练", executeFridayMissionContext.getMissionId()));
                    updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
                }
                return executeFridayMissionContext;
            } else {
                executeFridayMissionContext.setUploadDatasetStatus(0);
                executeFridayMissionContext.setStatus(0);
                executeFridayMissionContext.setExecuteMessage(String.format("正在执行训练数据集上传,missionId:%s", executeFridayMissionContext.getMissionId()));
                updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
                if (StringUtils.isBlank(request.getDatasetName()) && StringUtils.isBlank(request.getS3Urls()) && CollectionUtils.isEmpty(request.getVersionList())) {
                    throw new Exception("数据集名称不能为空" + JSONObject.toJSONString(request));
                }
                if (!StringUtils.isBlank(request.getDatasetName()) && !StringUtils.isBlank(request.getS3Urls())) {
                    Long datasetId = getDatasetIdByName(request.getDatasetName(), request.getMisId());
                    if (datasetId == null) {
                        executeFridayMissionContext.setDatasetId(uploadDataset(executeFridayMissionContext));
                        executeFridayMissionContext.setDatasetName(request.getDatasetName());
                        uploadDataset(executeFridayMissionContext);
                        executeFridayMissionContext.setVersionList(Arrays.asList(1));
                    } else {
                        executeFridayMissionContext.setDatasetId(datasetId);
                        executeFridayMissionContext.setDatasetName(request.getDatasetName());
                        if (!upLoadDatasetVersion(datasetId, request.getS3Urls(), request.getMisId())) {
                            log.error(String.format("上传数据集失败,s3:" + request.getS3Urls() + "name:" + request.getDatasetName()));
                            throw new Exception(String.format("上传数据集失败,s3:" + request.getS3Urls() + "name:" + request.getDatasetName()));
                        }
                        Integer datasetCurrentVersion = getDatasetCurrentVersion(datasetId, request.getMisId());
                        if (datasetCurrentVersion == null) {
                            log.error(String.format("获取数据集当前版本失败,datasetId:%s", datasetId));
                            throw new Exception(String.format("获取数据集当前版本失败,datasetId:%s", datasetId));
                        }
                        executeFridayMissionContext.setVersionList(Arrays.asList(datasetCurrentVersion));
                    }
                } else if (StringUtils.isBlank(request.getDatasetName()) && !StringUtils.isBlank(request.getS3Urls())) {
                    Long datasetId = uploadDataset(executeFridayMissionContext);
                    if (datasetId == null) {
                        log.error(String.format("上传数据集失败,s3:" + request.getS3Urls()));
                        throw new Exception(String.format("上传数据集失败,s3:" + request.getS3Urls()));
                    }
                    executeFridayMissionContext.setDatasetId(datasetId);
                    executeFridayMissionContext.setVersionList(Arrays.asList(1));
                } else if (!StringUtils.isBlank(request.getDatasetName()) && StringUtils.isBlank(request.getS3Urls()) && CollectionUtils.isNotEmpty(request.getVersionList())) {
                    Long datasetId = getDatasetIdByName(request.getDatasetName(), request.getMisId());
                    executeFridayMissionContext.setDatasetId(datasetId);
                    executeFridayMissionContext.setDatasetName(request.getDatasetName());
                    updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
                    if (datasetId == null) {
                        log.error(String.format("数据集不存在, name:%s", request.getDatasetName()));
                        throw new Exception(String.format("数据集不存在, name:%s", request.getDatasetName()));
                    }
                }

            }
            updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
            return executeFridayMissionContext;
        } catch (Exception e) {
            executeFridayMissionContext.setStatus(-1);
            executeFridayMissionContext.setUploadDatasetStatus(-1);
            executeFridayMissionContext.setExecuteMessage("执行上传数据集失败,missionId:" + executeFridayMissionContext.getMissionId() + "报错信息为:" + e.getMessage());
            updateFridayMissionInfo(executeFridayMissionContext, request, mcpMissionInfoByMission);
            log.error("uploadModelTraningMissionDataset error,request:{}，missionId:{}", JSONObject.toJSONString(request), executeFridayMissionContext.getMissionId(), e);
            
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
        }

    }

    private Long uploadDataset(ExecuteFridayMissionContext executeFridayMissionContext) throws Exception {
        String datasetName = executeFridayMissionContext.getRequest().getDatasetName();
        CustomEvalModel customEvalModel = new CustomEvalModel();
        if (!StringUtils.isBlank(datasetName)) {
            if (!validateDatasetName(datasetName)) {
                log.error("数据集名称不符合要求,只允许中文、英文、数字、下划线、中划线，且长度不超过50,missionId:{}", executeFridayMissionContext.getMissionId());
                throw new Exception("数据集名称不符合要求,只允许中文、英文、数字、下划线、中划线，且长度不超过50");
            }
            customEvalModel.setName(datasetName);
            customEvalModel.setDescription(datasetName);
        } else {
            customEvalModel.setName(generateUniqueDatasetName());
            customEvalModel.setDescription(customEvalModel.getName());
        }
        customEvalModel.setUserS3Url(executeFridayMissionContext.getRequest().getS3Urls());
        executeFridayMissionContext.setDatasetName(customEvalModel.getName());
        customEvalModel.setTrainMethod(executeFridayMissionContext.getRequest().getTrainMethod());
        String result = uploadTrainingDatasetToFriday(customEvalModel, executeFridayMissionContext.getRequest());
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (!"0".equals(jsonObject.getString("code"))) {
            throw new Exception("上传数据集失败: " + jsonObject.getString("message"));
        }
        Long datasetId = jsonObject.getLong("data");
        executeFridayMissionContext.setDatasetId(datasetId);
        return datasetId;
    }

    /**
     * 检验入参格式是否符合要求
     *
     * @param input 数据集名称
     * @return 是否符合要求
     */
    public boolean validateDatasetName(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }
        // 正则表达式：只允许中文、英文、数字、下划线、中划线，且长度不超过50
        String regex = "^[a-zA-Z0-9_\\-\\u4e00-\\u9fa5]{1,50}$";
        return input.matches(regex);
    }

    private Boolean upLoadDatasetVersion(Long datasetId, String s3Urls, String misId) throws Exception {
        // 新增数据集版本
        Map<String, Object> addDatasetVersionReq = new HashMap<>();
        addDatasetVersionReq.put("id", datasetId);
        addDatasetVersionReq.put("comment", "");
        addDatasetVersionReq.put("hdfsPath", "");
        addDatasetVersionReq.put("userS3Url", s3Urls);
        Map<String, String> fridayHeader = getFridayHeader(misId);
        fridayHeader.put("mt-project", TENANT_ID);
        String uploadResult = HttpUtil.httpPostFormUrlencoded(FRIDAY_ADD_DATASET_VERSION, JacksonUtil.toJsonStr(addDatasetVersionReq), fridayHeader);
        if (uploadResult != null && uploadResult.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        JSONObject uploadResultJsonObj = JSONObject.parseObject(uploadResult);
        if (!"0".equals(uploadResultJsonObj.getString("code"))) {
            log.error("上传数据集版本失败: {}", uploadResultJsonObj.getString("message"));
            return false;
        }
        return true;
    }

    private Boolean checkUploadDataSetVersionStatus(ExecuteFridayMissionContext context, String misId) throws Exception {
        int maxRetries = Integer.parseInt(modelDatasetUploadTimeConfig.get("maxRetries"));
        int currentRetry = 0;
        int num = 1;

        // 检查版本列表是否为空
        if (context.getVersionList() == null || context.getVersionList().isEmpty()) {
            log.error("版本列表为空，无法检查数据集上传状态，missionId:{}", context.getMissionId());
            throw new Exception("版本列表为空，无法检查数据集上传状态");
        }

        while (currentRetry < maxRetries) {
            boolean allSuccess = true;
            // 检查每个版本的状态
            for (Integer versionId : context.getVersionList()) {
                String status = queryDatasetUploadStatus(context.getDatasetId(), versionId, misId);
                log.info("正在执行训练数据集上传，轮训次数:{},数据集:{} 版本:{} 当前状态:{}", num, context.getDatasetName(), versionId, status);

                if ("上传失败".equals(status)) {
                    throw new Exception(String.format("数据集:%s 版本号:%d 数据集上传失败,请检查数据", context.getDatasetName(), versionId));
                }
                if (!"上传成功".equals(status)) {
                    allSuccess = false;
                }
            }
            if (allSuccess) {
                log.info("数据集:{} 上传成功", context.getDatasetName());
                return allSuccess;
            }
            currentRetry++;
        }
        return false;

    }


    private Boolean checkDatasetVersionList(List<Integer> versionList, Long datasetId, String misId) throws Exception {
        Integer datasetCurrentVersion = getDatasetCurrentVersion(datasetId, misId);
        Integer maxVersion = Collections.max(versionList);
        if (maxVersion > datasetCurrentVersion) {
            return false;
        }
        return true;
    }

    private Integer getDatasetCurrentVersion(Long datasetId, String misId) throws Exception {
        //获取当前数据集信息
        String datasetInfo = HttpUtil.httpGet(FRIDAY_QUERY_DATASET_INFO_BY_ID, ImmutableMap.of("id", datasetId.toString()), getFridayHeader(misId));
        if (datasetInfo != null && datasetInfo.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        JSONObject datasetInfoJson = JSONObject.parseObject(datasetInfo);
        // 从数据集信息中获取当前版本号
        Integer versionNum = Optional.ofNullable(datasetInfoJson)
                .filter(json -> "0".equals(json.getString("code")))
                .map(json -> json.getJSONObject("data"))
                .map(map -> {
                    String version = map.getString("currentVersion");
                    log.info("获取到数据集当前版本号: {}", version);
                    return Integer.parseInt(version) + 1;
                })
                .orElseThrow(() -> new Exception("获取数据集版本号失败"));
        return versionNum;
    }

    // 获取数据集ID
    private Long getDatasetIdByName(String datasetName, String misId) throws Exception {
        Map<String, String> queryParams = ImmutableMap.of(
                "query", datasetName,
                "pageNum", "1",
                "pageSize", "10",
                "subType", "train",
                "onlyOwner", "false"
        );
        Map<String, String> fridayHeader = getFridayHeader(misId);
        fridayHeader.put("mt-project", TENANT_ID);
        String response = HttpUtil.httpGet(FRIDAY_QUERY_DATASET_LIST, queryParams, fridayHeader);
        if (response != null && response.contains("统一登录中心")) {
            throw new Exception("Friday ssoId信息过期，请更新");
        }
        JSONObject jsonObject = JSONObject.parseObject(response);
        if (!"0".equals(jsonObject.getString("code"))) {
            throw new Exception(String.format("获取数据集信息失败: %s", jsonObject.getString("message")));
        }
        // 获取数据集列表
        JSONObject data = jsonObject.getJSONObject("data");
        if (data == null || data.getJSONArray("item") == null) {
            return null;
        }
        List<Map> datasetList = data.getJSONArray("item").toJavaList(Map.class);
        if (CollectionUtils.isEmpty(datasetList)) {
            return null;
        }
        return Optional.ofNullable(datasetList)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(map -> map.get("id"))
                .map(id -> {
                    if (id instanceof Number) {
                        return ((Number) id).longValue();
                    }
                    return null;
                })
                .orElse(null);
    }

    /**
     * 生成唯一的训练任务名称
     * 格式：Agent_Training_Mission_{时间戳}_{机器标识}_{随机数}
     *
     * @return 唯一的训练任务名称
     */
    private String generateUniqueTrainingTaskName() {
        String timestamp = LocalDateTime.now().format(shortFormatter);
        String randomSuffix = UUID.randomUUID().toString().substring(0, 5);

        return String.format("Agent_Training_Mission_%s_%s", timestamp, randomSuffix);
    }

    /**
     * 生成唯一的数据集名称
     * 格式：dataset_{时间戳}_{机器标识}_{随机数}
     *
     * @return 唯一的数据集名称
     */
    private String generateUniqueDatasetName() {
        String timestamp = LocalDateTime.now().format(shortFormatter);
        String randomSuffix = UUID.randomUUID().toString().substring(0, 5);

        return String.format("dataset_%s_%s", timestamp, randomSuffix);
    }
}
