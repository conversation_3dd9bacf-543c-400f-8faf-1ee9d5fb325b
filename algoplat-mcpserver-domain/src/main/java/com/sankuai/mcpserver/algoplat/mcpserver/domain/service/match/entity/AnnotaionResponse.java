package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnnotaionResponse {
    private Integer code;
    private Error error;
    private Result data;

    @Data
    public static class Error {
        private String msg;
        private Timestamp timeStamp;
    }

    @Data
    public static class Result {
        private List<Annotation> annotationItems;
        private String extensionInfo;
        private Integer totalPage;

        @Data
        public static class Annotation {
            private String preAnnotationData;
            private String afterAnnotationData;
        }
    }
}
