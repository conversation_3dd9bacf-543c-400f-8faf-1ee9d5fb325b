package com.sankuai.mcpserver.algoplat.mcpserver.domain.controller;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.grocery.dola.thrift.model.response.TBaseResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.ParserExtraService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.parser.ParserToolService;
import com.sankuai.nib.data.zb.flow.platform.api.request.FlowVerifyRequestDTO;
import com.sankuai.nib.data.zb.flow.platform.api.request.FlowVersionVerifyByFileRequestDTO;
import com.sankuai.nib.data.zb.flow.platform.api.request.UploadContextRequestDTO;
import com.sankuai.nib.data.zb.flow.platform.api.response.FlowVerifyResponseDTO;
import com.sankuai.nib.data.zb.flow.platform.api.service.FlowExecutorThriftService;
import com.sankuai.zb.metadata.api.dto.flow.agent.FlowAgentDTO;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowAnalysisDTO;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowInstanceStandardDTO;
import com.sankuai.zb.metadata.api.dto.rule.CreateRuleWithVersionDTO;
import com.sankuai.zb.metadata.api.request.rule.ParseRuleVersionRequest;
import com.sankuai.zb.metadata.api.service.FlowQueryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * Parser工具HTTP接口控制器
 * 为ParserToolService中的所有@Tool注解方法提供HTTP接口
 *
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/30
 */
@Slf4j
@RestController
@RequestMapping("/api/parser-tools")
public class ParserToolController {

    @Resource
    private ParserToolService parserToolService;

    @Resource
    private FlowQueryThriftService flowQueryThriftService;

    @Resource
    private ParserExtraService parserExtraService;

    /**
     * 查询加工规则中具体逻辑
     * 对应工具: query_transform_rule_entity
     */
    @PostMapping("/query-transform-rule-entity")
    public McpServerResponse queryTransformRuleEntity(@RequestParam String transformRuleId) {
        try {
            log.info("HTTP接口调用: queryTransformRuleEntity, transformRuleId: {}", transformRuleId);
            String result = parserToolService.queryTransformRuleEntity(transformRuleId);
            return McpServerResponse.success("查询加工规则成功", result);
        } catch (Exception e) {
            log.error("查询加工规则失败, transformRuleId: {}", transformRuleId, e);
            return McpServerResponse.fail("查询加工规则失败: " + e.getMessage());
        }
    }

    /**
     * 根据methodCode查询流程配置详情
     * 对应工具: query_flow_entity
     */
    @PostMapping("/query-flow-entity")
    public McpServerResponse queryFlowEntity(@RequestParam String methodCode) {
        try {
            log.info("HTTP接口调用: queryFlowEntity, methodCode: {}", methodCode);
            String result = parserToolService.queryFlowEntity(methodCode);
            return McpServerResponse.success("查询流程配置成功", result);
        } catch (Exception e) {
            log.error("查询流程配置失败, methodCode: {}", methodCode, e);
            return McpServerResponse.fail("查询流程配置失败: " + e.getMessage());
        }
    }

    /**
     * 根据l3名称查询L3交付规则ID
     * 对应工具: query_l3_rule_id
     */
    @PostMapping("/query-l3-rule-id")
    public McpServerResponse queryL3RuleId(@RequestParam String l3Name) {
        try {
            log.info("HTTP接口调用: queryL3RuleId, l3Name: {}", l3Name);
            String result = parserToolService.queryL3RuleId(l3Name);
            return McpServerResponse.success("查询L3交付规则ID成功", result);
        } catch (Exception e) {
            log.error("查询L3交付规则ID失败, l3Name: {}", l3Name, e);
            return McpServerResponse.fail("查询L3交付规则ID失败: " + e.getMessage());
        }
    }

    /**
     * 根据解析代码查询解析模型id
     * 对应工具: query_analysis_rule_id
     */
    @PostMapping("/query-analysis-rule-id")
    public McpServerResponse queryAnalysisRuleId(@RequestParam String analysisCode) {
        try {
            log.info("HTTP接口调用: queryAnalysisRuleId, analysisCode: {}", analysisCode);
            String result = parserToolService.queryAnalysisRuleId(analysisCode);
            return McpServerResponse.success("查询解析模型ID成功", result);
        } catch (Exception e) {
            log.error("查询解析模型ID失败, analysisCode: {}", analysisCode, e);
            return McpServerResponse.fail("查询解析模型ID失败: " + e.getMessage());
        }
    }

    /**
     * 新增一个流程配置写入系统
     * 对应工具: save_new_flow_entity
     */
    @PostMapping("/save-new-flow-entity")
    public TBaseResponse<Long> saveNewFlowEntity(@RequestBody FlowInstanceStandardDTO flowInstanceStandardDTO) {
        try {
            log.info("HTTP接口调用: saveNewFlowEntity, flowInstanceStandardDTO: {}", flowInstanceStandardDTO);
            TBaseResponse<Long> longTBaseResponse = parserExtraService.saveNewFlowEntity(flowInstanceStandardDTO);
            return longTBaseResponse;
        } catch (Exception e) {
            log.error("新增流程配置失败, flowInstanceStandardDTO: {}", flowInstanceStandardDTO, e);
            return TBaseResponse.error(500, "新增流程配置失败");
        }
    }

    /**
     * 新增一个流程版本添加至系统
     * 对应工具: add_new_flow_entity_version
     */
    @PostMapping("/add-new-flow-entity-version")
    public TBaseResponse<Long> addNewFlowEntityVersion(@RequestBody FlowAnalysisDTO flowAnalysisDTO) {
        try {
            TBaseResponse<Long> longTBaseResponse = parserExtraService.addNewFlowEntityVersion(flowAnalysisDTO);
            return longTBaseResponse;
        } catch (Exception e) {
            log.error("新增流程版本失败, flowAnalysisDTO: {}", flowAnalysisDTO, e);
            return TBaseResponse.error(500, "新增流程版本失败");
        }
    }

    /**
     * 新增一个加工规则写入系统
     * 对应工具: save_rule_entity
     */
    @PostMapping("/save-rule-entity")
    public TBaseResponse<Long> saveRuleEntity(@RequestBody CreateRuleWithVersionDTO createRuleWithVersionDTO) {
        TBaseResponse<Long> longTBaseResponse = parserExtraService.saveRuleEntity(createRuleWithVersionDTO);
        return longTBaseResponse;
    }

    /**
     * 新增一个加工规则版本添加至系统
     * 对应工具: add_rule_entity_version
     */
    @PostMapping("/add-rule-entity-version")
    public TBaseResponse<Long> addRuleEntityVersion(@RequestBody  ParseRuleVersionRequest parseRuleVersionRequest) {
        try{
            TBaseResponse<Long> longTBaseResponse = parserExtraService.addRuleEntityVersion(parseRuleVersionRequest);
            return longTBaseResponse;
        } catch (Exception e) {
            log.error("新增加工规则版本失败, parseRuleVersionRequest: {}", parseRuleVersionRequest, e);
            return TBaseResponse.error(500, "新增加工规则版本失败");
        }
    }

    /**
     * 验证加工规则
     * 对应工具: rule_detail_valid
     */
    @PostMapping("/rule-detail-valid")
    public McpServerResponse ruleDetailValid(@RequestParam List<String> ruleDetails) {
        try {
            log.info("HTTP接口调用: ruleDetailValid, ruleDetails: {}", ruleDetails);
            String result = parserToolService.rule_detail_valid(ruleDetails);
            return McpServerResponse.success("验证加工规则成功", result);
        } catch (Exception e) {
            log.error("验证加工规则失败, ruleDetails: {}", ruleDetails, e);
            return McpServerResponse.fail("验证加工规则失败: " + e.getMessage());
        }
    }

    @GetMapping("/queryFlowInstanceByMethodCode")
    public McpServerResponse queryFlowInstanceByMethodCode(@RequestParam String methodCode) {
        try {
            log.info("HTTP接口调用: queryParserRule, methodCode: {}", methodCode);
            TBaseResponse<FlowAgentDTO> result = flowQueryThriftService.queryFlowInstanceByMethodCode(methodCode);
            return McpServerResponse.success("查询解析规则成功", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("查询解析规则失败, methodCode: {}", methodCode, e);
            return McpServerResponse.fail("查询解析规则失败: " + e.getMessage());
        }

    }


    /**
     * 根据URL验证流程
     * 对应工具: verify_flow_version_by_file
     */
    @PostMapping("/verifyFlowVersionByFile")
    public TBaseResponse<FlowVerifyResponseDTO> verifyFlowVersionByFile(@RequestBody FlowVersionVerifyByFileRequestDTO request) {
        try {
            log.info("HTTP接口调用: verifyFlowVersionByFile, FlowVerifyRequestDTO: {}", request);
            TBaseResponse<FlowVerifyResponseDTO> flowVerifyResponseDTOTBaseResponse = parserExtraService.verifyFlowVersionByFile(request);
            log.info("验证流程,verifyFlowVersionByFile, response: {}", flowVerifyResponseDTOTBaseResponse);
            return flowVerifyResponseDTOTBaseResponse;
        } catch (Exception e) {
            log.error("根据URL验证流程失败, FlowVerifyRequestDTO: {}", request, e);
            return TBaseResponse.error(500, "根据URL验证流程失败");
        }
    }
}
