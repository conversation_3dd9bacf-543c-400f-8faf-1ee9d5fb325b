package com.sankuai.mcpserver.algoplat.mcpserver.domain.entity;

/**
 * 通用响应类
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/8
 */
public class McpServerResponse {
    /**
     * 响应状态码，0表示成功，-1表示失败
     */
    private int code;

    /**
     * 响应消息，用于描述响应结果
     */
    private String message;

    /**
     * 响应数据
     */
    private String data;

    /**
     * 成功状态码
     */
    public static final int SUCCESS_CODE = 0;

    /**
     * 失败状态码
     */
    public static final int FAIL_CODE = -1;

    /**
     * 默认构造函数
     */
    public McpServerResponse() {
    }

    /**
     * 构造函数
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     */
    public McpServerResponse(int code, String message, String data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 创建成功响应（无数据）
     * @return 成功响应对象
     */
    public static McpServerResponse success() {
        return new McpServerResponse(SUCCESS_CODE, "操作成功", null);
    }

    /**
     * 创建成功响应（带消息）
     * @param message 成功消息
     * @return 成功响应对象
     */
    public static McpServerResponse success(String message) {
        return new McpServerResponse(SUCCESS_CODE, message, null);
    }
    /**
     * 创建成功响应（带消息和数据）
     * @param message 成功消息
     * @param data 响应数据
     * @return 成功响应对象
     */
    public static McpServerResponse success(String message, String data) {
        return new McpServerResponse(SUCCESS_CODE, message, data);
    }

    /**
     * 创建失败响应（无数据）
     * @return 失败响应对象
     */
    public static McpServerResponse fail() {
        return new McpServerResponse(FAIL_CODE, "操作失败", null);
    }

    /**
     * 创建失败响应（带消息）
     * @param message 失败消息
     * @return 失败响应对象
     */
    public static McpServerResponse fail(String message) {
        return new McpServerResponse(FAIL_CODE, message, null);
    }

    /**
     * 创建失败响应（带消息和数据）
     * @param message 失败消息
     * @param data 响应数据
     * @return 失败响应对象
     */
    public static McpServerResponse fail(String message, String data) {
        return new McpServerResponse(FAIL_CODE, message, data);
    }

    /**
     * 创建自定义响应
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     * @return 响应对象
     */
    public static McpServerResponse build(int code, String message, String data) {
        return new McpServerResponse(code, message, data);
    }

    // Getters and Setters
    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "Response{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data='" + data + '\'' +
                '}';
    }
}
