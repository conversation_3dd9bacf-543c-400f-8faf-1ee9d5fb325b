package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.util.Md5Utils;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
//import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TGetXuechengContentRequest;
//import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TGetXuechengContentResponse;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.util.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.dto.datastream.DsTextVectorQueryItemDto;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsKeywordQueryRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsScalarQueryRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsTextVectorQueryRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsTextVectorWriteRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsKeywordQueryResponse;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsScalarQueryResponse;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsTextVectorBuildResponse;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsTextVectorQueryResponse;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamQueryThriftService;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamWriteThriftService;
import com.sankuai.algoplatform.agentapi.client.request.TKnowledgeCollectRequest;
import com.sankuai.algoplatform.agentapi.client.response.TBaseReponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentLearningService;
import com.sankuai.algoplatform.matchops.api.request.mcptool.TMcpTestMissionRequest;
import com.sankuai.algoplatform.matchops.api.request.testTask.LoadTestRequest;
import com.sankuai.algoplatform.matchops.api.response.mcptool.TMcpTestMissionResponse;
import com.sankuai.algoplatform.matchops.api.service.TMcpTestMissionService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.CreateCitadelDocReq;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.TalosRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.XueChengRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.DXMessageRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.*;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.TalosQueryService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.McpToolResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.MatchToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.tool.MatchTool;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.ToolContextUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.DxPusherConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpMissionDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.cache.TairClient;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.S3UploadRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.EsService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.GitService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.factory.ThreadPoolFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.service.impl.S3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/10
 */
@Slf4j
@Service
public class DefaultToolService {
    @Resource
    private XmCitadelService xmCitadelService;

    private static final String DEFAULT_TOOL = "DefaultToolService";

    @Resource
    private IDataStreamQueryThriftService iDataStreamQueryThriftService;

    @Resource
    private IDataStreamWriteThriftService iDataStreamWriteThriftService;

    @Resource
    private GitService gitService;

    @Resource
    private EsService esService;

    @Resource
    private S3Service s3Service;

    @Resource
    private TairClient tairClient;
    @Resource
    private DxService dxService;

    @Resource
    private MatchToolService matchToolService;

    @Resource
    private McpMissionDao mcpMissionDao;

    @Resource
    private TAgentLearningService tAgentLearningService;

    @Resource
    private TalosQueryService talosQueryService;

    @Tool(name = "text_vector_query", description = "工具能力：伏羲知识向量库_向量搜索" +
            "" +
            "参数描述：" +
            "类名：DsTextVectorQueryRequest\n" +
            "描述：数据流文本向量查询请求\n" +
            "必填参数:\n" +
            "dataStreamId (Long): 数据流ID\n" +
            "query (String): 查询文本\n" +
            "topK (Integer): 返回结果数量\n" +
            "选填参数:\n" +
            "scalarSearchParam (Map<String,String>): 向量库标量检索\n" +
            "ebdModelEnumValue (Integer): embedding模型枚举值\n" +
            "ebdModel (String): 向量模型名称 包含：shibing624/text2vec-base-chinese、FlagEmbedding、bge-base-v15、bge-large-v15-pangu、bge-large-v15、bge-small-zh-v15、bge-small-zh-v15-ft-travel-recommend" +
            "出参描述：" +
            "DsTextVectorQueryResponse 数据流文本向量查询响应" +
            "参数详解：" +
            "顶层结构 (DsTextVectorQueryResponse)\n" +
            "code (Integer): 状态码 (200成功/500错误)\n" +
            "msg (String): 错误信息\n" +
            "data (List): 查询结果列表\n" +
            "查询项结构 (DsTextVectorQueryItemDto)\n" +
            "docId (Long): 文档ID\n" +
            "jsonData (String): JSON格式内容数据\n" +
            "chunkContents (List): 分块内容列表\n" +
            "maxScore (Double): 最高相关性分数\n" +
            "minScore (Double): 最低相关性分数\n" +
            "分块内容结构 (DsChunkContentDto)\n" +
            "chunk (String): chunk内容\n" +
            "ebdFieldName (String): 向量化字段\n" +
            "seq (Integer): 向量顺序号(1-n)\n" +
            "score (Double): 向量相似度得分\n" +
            "jsonData (String): 向量索引JSON数据" +
            "")
    public String textVectorQuery(@ToolParam(description = "数据流文本向量查询请求入参", required = true) DsTextVectorQueryRequest request) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "textVectorQuery");
        DsTextVectorQueryResponse dsTextVectorQueryResponse = null;
        if (request == null) {
            return "入参不能为空";
        }
        try {
            log.info("textVectorQuery request:{}", JSONObject.toJSONString(request));
            dsTextVectorQueryResponse = iDataStreamQueryThriftService.textVectorQuery(request);
            t.setSuccessStatus();
            return JSONObject.toJSONString(dsTextVectorQueryResponse);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            t.setStatus(e);
            return JSONObject.toJSONString(e);
        } finally {
            t.complete();
            log.info("text_Vector_Query,数据流文本向量查询请求入参:{}, 响应:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(dsTextVectorQueryResponse));
        }
    }


    @Tool(name = "textVectorDelete", description = "工具作用：数据流文本向量删除" +
            "参数描述：" +
            "DsTextVectorWriteRequest对象" +
            "dataStreamId Long类型 数据流ID 必填" +
            "originContent String 原始内容，平铺Json 必填" +
            "请求示例：" +
            "{\n" +
            "  \"dataStreamId\": 571,\n" +
            "  \"originContent\": \"{\\\"question\\\":\\\"hello?\\\"}\"\n" +
            "}\n" +
            "出参示例：" +
            "McpServerResponse对象" +
            "code Integer 响应状态码，0表示成功，-1表示失败" +
            "message String 请求信息说明" +
            "data String 若data的值为true则代表删除成功")
    public McpServerResponse textVectorDelete(@ToolParam(description = "数据流文本向量删除请求入参", required = true) DsTextVectorWriteRequest request) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "textVectorDelete");
        if (request == null || request.getDataStreamId() == null || request.getOriginContent() == null) {
            return McpServerResponse.fail("入参不能为空");
        }
        Boolean success = false;
        try {
            log.info("textVectorDelete request:{}", JSONObject.toJSONString(request));
            DsTextVectorBuildResponse dsTextVectorBuildResponse = iDataStreamWriteThriftService.textVectorDelete(request);
            log.info("textVectorDelete req:{}response:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(dsTextVectorBuildResponse));
            t.setSuccessStatus();
            success = dsTextVectorBuildResponse.getSuccess();
            return McpServerResponse.success("完成请求", success.toString());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            
            t.setStatus(e);
            return McpServerResponse.fail(e.getMessage());
        }
    }


    @Tool(name = "text_vector_query_only_jsonData", description = "工具能力：伏羲知识向量库_向量搜索" +
            "" +
            "参数描述：" +
            "类名：DsTextVectorQueryRequest\n" +
            "描述：数据流文本向量查询请求\n" +
            "必填参数:\n" +
            "dataStreamId (Long): 数据流ID\n" +
            "query (String): 查询文本\n" +
            "topK (Integer): 返回结果数量\n" +
            "选填参数:\n" +
            "scalarSearchParam (Map<String,String>): 向量库标量检索\n" +
            "ebdModelEnumValue (Integer): embedding模型枚举值\n" +
            "ebdModel (String): 向量模型名称" +
            "\n" +
            "出参描述：" +
            "DsTextVectorQueryResponse 数据流文本向量查询响应" +
            "参数详解：" +
            "顶层结构 (DsTextVectorQueryResponse)\n" +
            "code (Integer): 状态码 (200成功/500错误)\n" +
            "msg (String): 错误信息\n" +
            "data (List): 查询结果列表\n" +
            "查询项结构 (DsTextVectorQueryItemDto)\n" +
            "docId (Long): 文档ID\n" +
            "jsonData (String): JSON格式内容数据\n" +
            "chunkContents (List): 分块内容列表\n" +
            "maxScore (Double): 最高相关性分数\n" +
            "minScore (Double): 最低相关性分数\n" +
            "分块内容结构 (DsChunkContentDto)\n" +
            "chunk (String): chunk内容\n" +
            "ebdFieldName (String): 向量化字段\n" +
            "seq (Integer): 向量顺序号(1-n)\n" +
            "score (Double): 向量相似度得分\n" +
            "jsonData (String): 向量索引JSON数据" +
            "")
    public String textVectorQueryOnlyAnswer(@ToolParam(description = "数据流文本向量查询请求入参", required = true) DsTextVectorQueryRequest request) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "textVectorQuery");
        DsTextVectorQueryResponse dsTextVectorQueryResponse = null;
        if (request == null) {
            return "入参不能为空";
        }
        String answer = null;
        try {
            log.info("textVectorQuery request:{}", request);
            dsTextVectorQueryResponse = iDataStreamQueryThriftService.textVectorQuery(request);
            if (dsTextVectorQueryResponse == null || dsTextVectorQueryResponse.getCode() != 200) {
                return "查询失败";
            }
            List<DsTextVectorQueryItemDto> data = dsTextVectorQueryResponse.getData();
            if (data == null || data.size() == 0) {
                t.setStatus("查询结果为空");
                return "查询结果为空";
            }
            DsTextVectorQueryItemDto dsTextVectorQueryItemDto = data.get(0);
            String jsonData = dsTextVectorQueryItemDto.getJsonData();
            t.setSuccessStatus();
            return JSONObject.toJSONString(jsonData);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            
            t.setStatus(e);
            return JSONObject.toJSONString(e);
        } finally {
            t.complete();
            log.info("text_Vector_Query,数据流文本向量查询请求入参:{}, 响应:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(dsTextVectorQueryResponse));
        }
    }

    @Tool(name = "key_word_query", description = "工具能力：伏羲知识向量库_关键词检索" +
            "\n" +
            "入参：DsKeywordQueryRequest" +
            " DsKeywordQueryRequest详解:\n" +
            "必填参数:\n" +
            "dataStreamId (Long): 数据流ID，由平台侧告知业务方\n" +
            "topK (Integer): 返回结果数量\n" +
            "选填参数:\n" +
            "scalarSearchParam (Map<String,String>): 向量库标量检索，使用 htp-cms-common 包构建" +
            "出参：DsKeywordQueryResponse" +
            "\n" +
            "DsKeywordQueryResponse详解：" +
            "DsKeywordQueryResponse 结构总结\n" +
            "顶层结构 (DsKeywordQueryResponse)\n" +
            "code (Integer): 状态码 (200成功/500错误)\n" +
            "msg (String): 错误信息\n" +
            "data (List): 查询结果列表\n" +
            "查询项结构 (DsKeywordQueryItemDto)\n" +
            "docId (Long): 文档ID\n" +
            "jsonData (String): JSON格式内容数据\n" +
            "simScore (Double): 相似度分数")
    public String keywordQuery(@ToolParam(description = "关键词查询请求", required = true) DsKeywordQueryRequest request) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "key_word_Query");
        DsKeywordQueryResponse dsKeywordQueryResponse = null;
        if (request == null) {
            return "入参不能为空";
        }
        try {
            log.info("keywordQuery request:{}", request);
            if (request.getScalarSearchParam() == null) {
                request.setScalarSearchParam(new HashMap<>());
            }
            dsKeywordQueryResponse = iDataStreamQueryThriftService.keywordQuery(request);
            t.setSuccessStatus();
            return JSONObject.toJSONString(dsKeywordQueryResponse);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            
            t.setStatus(e);
            return JSONObject.toJSONString(e);
        } finally {
            t.complete();
            log.info("keywordQuery,关键词检索请求入参:{}, 响应:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(dsKeywordQueryResponse));
        }
    }

    @Tool(name = "scalar_query", description = "工具能力：伏羲知识向量库_标量检索" +
            "\n" +
            "入参：DsScalarQueryRequest" +
            "\n" +
            "DsScalarQueryRequest详解:\n" +
            "必填参数:\n" +
            "dataStreamId (Long): 数据集ID\n" +
            "limit (Integer): 查询数量\n" +
            "offset (Integer): 起始位置\n" +
            "选填参数:\n" +
            "scalarSearchParam (Map<String,String>): 向量库标量检索参数\n" +
            "recallFields (List<String>): 召回字段列表\n" +
            "ascOrder (Boolean): 升序排序" +
            "\n" +
            "出参：DsScalarQueryResponse\n" +
            "响应结构:\n" +
            "code (Integer): 状态码(200:成功/500:错误)\n" +
            "msg (String): 错误信息\n" +
            "data (List<DsScalarQueryItemDto>): 内容数据列表\n" +
            "total (Long): 总数量\n" +
            "\n" +
            "DsScalarQueryItemDto结构:\n" +
            "docId (Long): 文档ID\n" +
            "jsonData (String): json格式内容数据\n" +
            "createTime (Long): 创建时间\n" +
            "updateTime (Long): 更新时间")
    public String scalarQuery(@ToolParam(description = "标量检索请求入参", required = true) DsScalarQueryRequest request) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "scalar_Query");
        DsScalarQueryResponse dsScalarQueryResponse = null;
        if (request == null) {
            return "入参不能为空";
        }
        try {
            log.info("scalarQuery request:{}", request);
            dsScalarQueryResponse = iDataStreamQueryThriftService.scalarQuery(request);
            t.setSuccessStatus();
            return JSONObject.toJSONString(dsScalarQueryResponse);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            
            t.setStatus(e);
            return JSONObject.toJSONString(e);
        } finally {
            t.complete();
            log.info("scalarQuery,标量检索请求入参:{}, 响应:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(dsScalarQueryResponse));
        }
    }


    @Tool(name = "text_vector_build", description = "工具能力：伏羲知识向量库_文本向量新增更新\n" +
            "入参：DsTextVectorWriteRequest\n" +
            "DsTextVectorWriteRequest详解:\n" +
            "必填参数:\n" +
            "dataStreamId (Long): 数据流ID\n" +
            "originContent (String): 原始内容(JSON格式: Map<String, Object>)\n" +
            "选填参数:\n" +
            "dataPushType (Integer): 数据写入方式(0:未知, 1:定时任务提交, 2:实时提交)\n" +
            "出参：DsTextVectorBuildResponse\n" +
            "响应结构:\n" +
            "code (Integer): 状态码\n" +
            "msg (String): 响应消息\n" +
            "success (Boolean): 操作是否成功")
    public String textVectorBuild(@ToolParam(description = "数据流文本向量写入请求", required = true) DsTextVectorWriteRequest request) {

        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "text_vector_build");
        DsTextVectorBuildResponse dsTextVectorBuildResponse = null;
        if (request == null) {
            return "入参不能为空";
        }
        try {
            log.info("textVectorBuild request:{}", request);
            dsTextVectorBuildResponse = iDataStreamWriteThriftService.textVectorBuild(request);
            t.setSuccessStatus();
            return JSONObject.toJSONString(dsTextVectorBuildResponse);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            
            t.setStatus(e);
            return JSONObject.toJSONString(e);
        } finally {
            t.complete();
            log.info("text_vector_build,数据流文本向量写入请求入参:{}, 响应:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(dsTextVectorBuildResponse));
        }

    }

    @Tool(name = "is_branch_or_tag_exist", description = "检查指定仓库的分支或Tag是否存在\n" +
            "参数说明:\n" +
            "project: 仓库组名\n" +
            "repo: 仓库名\n" +
            "type: 类型(branch或tag)\n" +
            "misId: mis号，用于权限验证")
    public boolean isBranchOrTagExist(
            @ToolParam(description = "仓库组名", required = true) String project,
            @ToolParam(description = "仓库名", required = true) String repo,
            @ToolParam(description = "分支名或Tag名", required = true) String name,
            @ToolParam(description = "类型(branch或tag)", required = true) String type,
            @ToolParam(description = "mis号", required = true) String misId
    ) {

        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "is_branch_or_tag_exist");
        try {
            log.info("isBranchOrTagExist request - project:{}, repo:{}, name:{}, type:{}",
                    project, repo, name, type);


            gitService.isBranchOrTagExist(project, repo, name, type, misId);

            t.setSuccessStatus();
            return true; // 临时返回值，需要根据实际API响应判断

        } catch (Exception e) {
            log.error("isBranchOrTagExist error", e);
            
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
            log.info("isBranchOrTagExist, 仓库组名:{}, 仓库名:{}, 分支名或Tag名:{}, 类型:{}, 响应:{}", project, repo, name, type, true);
        }
    }

    @Tool(name = "get_templates", description = "获取ES集群的索引模板列表\n" +
            "参数说明:\n" +
            "clusterName: ES集群名称")
    public String getTemplates(
            @ToolParam(description = "ES集群名称", required = true) String clusterName) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "get_templates");
        try {
            log.info("getTemplates request - clusterName:{}", clusterName);

            List<TemplateInfo> templates = esService.getClusterTemplates(clusterName);

            t.setSuccessStatus();
            return JSONObject.toJSONString(templates);

        } catch (Exception e) {
            log.error("getTemplates error", e);
            
            t.setStatus(e);
            return "获取ES模板列表失败: " + e.getMessage();
        } finally {
            t.complete();
            log.info("getTemplates completed, clusterName:{}", clusterName);
        }
    }

    @Tool(name = "create_template", description = "创建ES索引模板\n" +
            "参数说明:\n" +
            "clusterName: ES集群名称\n" +
            "templateName: 模板名称\n" +
            "templateContent: 模板内容(JSON对象)，示例：\n" +
            "{\n" +
            "  \"template\": \"my_test_*\",\n" +
            "  \"settings\": {\n" +
            "    \"number_of_shards\": 1\n" +
            "  },\n" +
            "  \"mappings\": {\n" +
            "    \"_source\": {\n" +
            "      \"enabled\": false\n" +
            "    },\n" +
            "    \"properties\": {\n" +
            "      \"host_name\": {\n" +
            "        \"type\": \"keyword\"\n" +
            "      },\n" +
            "      \"created_at\": {\n" +
            "        \"type\": \"date\",\n" +
            "        \"format\": \"EEE MMM dd HH:mm:ss Z YYYY\"\n" +
            "      }\n" +
            "    }\n" +
            "  }\n" +
            "}")
    public String createTemplate(
            @ToolParam(description = "ES集群名称", required = true) String clusterName,
            @ToolParam(description = "模板名称", required = true) String templateName,
            @ToolParam(description = "模板内容(JSON对象)", required = true) JSONObject templateContent) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "create_template");
        try {
            log.info("createTemplate request - clusterName:{}, templateName:{}, content:{}",
                    clusterName, templateName, templateContent);

            boolean success = esService.createTemplate(clusterName, templateName, templateContent);

            t.setSuccessStatus();
            return success ? "模板创建成功" : "模板创建失败";

        } catch (Exception e) {
            log.error("createTemplate error", e);
            
            t.setStatus(e);
            return "创建ES模板失败: " + e.getMessage();
        } finally {
            t.complete();
            log.info("createTemplate completed, clusterName:{}, templateName:{}",
                    clusterName, templateName);
        }
    }

    @Tool(name = "update_template", description = "更新ES索引模板\n" +
            "参数说明:\n" +
            "clusterName: ES集群名称\n" +
            "templateName: 模板名称\n" +
            "templateContent: 模板内容(JSON对象)，示例：\n" +
            "{\n" +
            "  \"template\": \"my_test_*\",\n" +
            "  \"settings\": {\n" +
            "    \"number_of_shards\": 1\n" +
            "  },\n" +
            "  \"mappings\": {\n" +
            "    \"_source\": {\n" +
            "      \"enabled\": false\n" +
            "    },\n" +
            "    \"properties\": {\n" +
            "      \"host_name\": {\n" +
            "        \"type\": \"keyword\"\n" +
            "      },\n" +
            "      \"created_at\": {\n" +
            "        \"type\": \"date\",\n" +
            "        \"format\": \"EEE MMM dd HH:mm:ss Z YYYY\"\n" +
            "      }\n" +
            "    }\n" +
            "  }\n" +
            "}")
    public String updateTemplate(
            @ToolParam(description = "ES集群名称", required = true) String clusterName,
            @ToolParam(description = "模板名称", required = true) String templateName,
            @ToolParam(description = "模板内容(JSON对象)", required = true) JSONObject templateContent) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "update_template");
        try {
            log.info("updateTemplate request - clusterName:{}, templateName:{}, content:{}",
                    clusterName, templateName, templateContent);

            boolean success = esService.createTemplate(clusterName, templateName, templateContent);

            t.setSuccessStatus();
            return success ? "模板更新成功" : "模板更新失败";

        } catch (Exception e) {
            log.error("updateTemplate error", e);
            
            t.setStatus(e);
            return "更新ES模板失败: " + e.getMessage();
        } finally {
            t.complete();
            log.info("updateTemplate completed, clusterName:{}, templateName:{}",
                    clusterName, templateName);
        }
    }

    @Tool(name = "delete_template", description = "删除ES索引模板\n" +
            "参数说明:\n" +
            "clusterName: ES集群名称\n" +
            "templateName: 模板名称")
    public String deleteTemplate(
            @ToolParam(description = "ES集群名称", required = true) String clusterName,
            @ToolParam(description = "模板名称", required = true) String templateName) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "delete_template");
        try {
            log.info("deleteTemplate request - clusterName:{}, templateName:{}",
                    clusterName, templateName);

            boolean success = esService.deleteTemplate(clusterName, templateName);

            t.setSuccessStatus();
            return success ? "模板删除成功" : "模板删除失败";

        } catch (Exception e) {
            log.error("deleteTemplate error", e);
            
            t.setStatus(e);
            return "删除ES模板失败: " + e.getMessage();
        } finally {
            t.complete();
            log.info("deleteTemplate completed, clusterName:{}, templateName:{}",
                    clusterName, templateName);
        }
    }

    @Tool(name = "create_index", description = "创建ES索引\n" +
            "参数说明:\n" +
            "clusterName: ES集群名称\n" +
            "indexName: 索引名称\n" +
            "settings: 索引设置(JSON对象)，示例：\n" +
            "{\n" +
            "  \"settings\": {\n" +
            "    \"number_of_shards\": 3,\n" +
            "    \"number_of_replicas\": 2\n" +
            "  }\n" +
            "}")
    public String createIndex(
            @ToolParam(description = "ES集群名称", required = true) String clusterName,
            @ToolParam(description = "索引名称", required = true) String indexName,
            @ToolParam(description = "索引设置(JSON对象)", required = true) JSONObject settings) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "create_index");
        try {
            log.info("createIndex request - clusterName:{}, indexName:{}, settings:{}",
                    clusterName, indexName, settings);

            boolean success = esService.createIndex(clusterName, indexName, settings);

            t.setSuccessStatus();
            return success ? "索引创建成功" : "索引创建失败";

        } catch (Exception e) {
            log.error("createIndex error", e);
            
            t.setStatus(e);
            return "创建索引失败: " + e.getMessage();
        } finally {
            t.complete();
            log.info("createIndex completed, clusterName:{}, indexName:{}",
                    clusterName, indexName);
        }
    }

    @Tool(name = "read_xuecheng_doc_and_return_raw_json", description = "工具作用: 读取学城文档并返回文档的原始JSON数据" +
            "入参：\n" +
            "misId String 用户mis号\n" +
            "url String 学城文档完整链接\n" +
            "返回参数示例：\n" +
            "{\n" +
            "  \"code\": 0,\n" +
            "  \"message\": \"成功读取学城文档！\",\n" +
            "  \"data\": \"{\\\"type\\\":\\\"doc\\\",\\\"content\\\":[{\\\"type\\\":\\\"title\\\",\\\"attrs\\\":{\\\"nodeId\\\":\\\"1df2ea4217bc4cea9848d235a5012073\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"efwef\\\"}]},{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"indent\\\":0,\\\"align\\\":\\\"\\\",\\\"dataDiffId\\\":null,\\\"nodeId\\\":\\\"91732ef310c74097b742579d16989f3e\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"一、需求背景&价值\\\"}]},{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"indent\\\":0,\\\"align\\\":\\\"\\\",\\\"dataDiffId\\\":\\\"96654cb0-2771-4a20-9ff6-ba7ad29bcdec\\\",\\\"nodeId\\\":\\\"96ad2347028045b2b13bd71423b13976\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"C48渠道经过XA改造后，后续可以提供模糊销量信息。由于C48渠道历史不解析模糊销量的相关字段，所以需要通过本次迭代，新增模糊销量相关字段的解析、加工、交付逻辑。\\\"}]},{\\\"type\\\":\\\"heading\\\",\\\"attrs\\\":{\\\"id\\\":\\\"4b44b117-80dd-4acb-85fd-073b8e2debdb\\\",\\\"level\\\":3,\\\"indent\\\":0,\\\"align\\\":\\\"\\\",\\\"dataDiffId\\\":\\\"91b76f94-790f-4c95-9359-8ac85ce4c8bc\\\",\\\"nodeId\\\":\\\"76d570f9471540c7b024e686c9021fb2\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"二、需求内容\\\"}]},{\\\"type\\\":\\\"paragraph\\\",\\\"attrs\\\":{\\\"indent\\\":0,\\\"align\\\":\\\"\\\",\\\"dataDiffId\\\":\\\"30366928-1afa-4c09-965c-6ae777ae9a1e\\\",\\\"nodeId\\\":\\\"e8e04ebad5534b6193f4c9f5be33edbf\\\"},\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"1、完成门店下商品列表C48渠道的解析、加工、交付逻辑迭代。\\\"}]},{\\\"type\\\":\\\"heading\\\",\\\"attrs\\\":{\\\"id\\\":\\\"a11dde2f-751e-4fe6-ac09-41744aacdef1\\\",\\\"level\\\":3,\\\"indent\\\":0,\\\"align\\\":\\\"\\\",\\\"dataDiffId\\\":\\\"263ea0cd-c726-4c72-a0df-1ef645437bb9\\\",\\\"nodeId\\\":\\\"837025fdd69f42f2b84a9822d179bea1\\\"}}]}\"\n" +
            "}")
    public McpServerResponse getXuechengDocWithJson(@ToolParam(description = "用户misId String类型") String misId, @ToolParam(description = "学城文档完整链接 String类型") String url) {
        McpServerResponse citadelContentJson = xmCitadelService.getCitadelContentJson(misId, url);
        return citadelContentJson;
    }

    @Tool(name = "read_xuecheng_doc_and_return_markdown", description = "工具作用: 读取学城文档并返回文档的MarkDown格式内容\n" +
            "入参：\n" +
            "misId String 用户mis号\n" +
            "url String 学城文档完整链接\n" +
            "返回参数示例：\n" +
            "{\n" +
            "  \"code\": 0,\n" +
            "  \"message\": \"获取学城Markdown文档成功！\",\n" +
            "  \"data\": \"# BML2.0采集不一致产能预估\\n## 1. 问题背景\\n当前BML2.0采集系统面临以下情况：\\n- 采集不一致率：8.4%（抽检972个商品中有82个不一致）\\n\"\n" +
            "}")
    public McpServerResponse getXuechengDocWithMarkDown(@ToolParam(description = "用户misId String类型") String misId, @ToolParam(description = "学城文档完整链接 String类型") String url) {
        McpServerResponse citadelContentJson = xmCitadelService.getXuechengContentWithMarkdown(url, misId);
        return citadelContentJson;
    }

    @Tool(name = "create_xuecheng_document", description = "工具作用：创建学城文档")
    public McpServerResponse createXuechengDocument(@ToolParam(description = "工具作用：创建学城文档\n" +
            "入参：CreateCitadelDocReq对象，包含以下参数\n" +
            "misId: String类型 必填项，表示用户的唯一标识。\n" +
            "title: String类型 选填项，表示文档的标题，若为空则默认为'无标题'。\n" +
            "content: String类型 选填项，但与 contentS3Url 不能同时为空，表示文档的内容。\n" +
            "spaceId: String类型 选填项，表示文档所属的空间标识,默认是在misId对应的空间下创建文档，如果想在他人的项目空间下创建文档，请联系他人给你加上空间可管理权限。\n" +
            "parentUrl: String类型 选填项，表示文档的父级 URL，如果为空默认是在一级目录下新增文档。\n" +
            "contentS3Url: String类型 选填项，但与 content 不能同时为空，表示markdown文档内容的 S3 存储地址。\n" +
            "返回参数：\n" +
            "data中是文档的地址\n" +
            "{\n" +
            "    \"code\": 0,\n" +
            "    \"message\": \"创建学城文档成功！\",\n" +
            "    \"data\": \"https://km.it.test.sankuai.com/collabpage/4299156705\"\n" +
            "}", required = true) CreateCitadelDocReq req) {
        McpServerResponse citadelDoc = xmCitadelService.createCitadelDoc(req);
        return citadelDoc;
    }

    public McpToolResponse<String> uploadFileToS3AndGetFileUrlWithTimeLimit(MultipartFile file, Long useTime) {
        Cat.logEvent(DEFAULT_TOOL, "upload_file_to_s3");
        try {
            Map<String, Object> fileData = new HashMap<>();
            fileData.put("name", file.getOriginalFilename());
            fileData.put("content", file.getBytes());
            fileData.put("contentType", file.getContentType());
            fileData.put("size", file.getSize());
            if (CollectionUtils.isEmpty(fileData) || fileData.get("content") == null || fileData.get("name") == null) {
                return McpToolResponse.fail("文件上传失败,入参有误！");
            }
            String fileName = (String) fileData.get("name");
            byte[] content = (byte[]) fileData.get("content");
            InputStream inputStream = new ByteArrayInputStream(content);
            S3UploadRequest s3UploadRequest = new S3UploadRequest();
            s3UploadRequest.setInputStream(inputStream);
            s3UploadRequest.setOriginalFileName(fileName);
            String s3Url = s3Service.uploadFileToS3AndGetFileUrl(s3UploadRequest, useTime);
            if (StringUtils.isBlank(s3Url)) {
                return McpToolResponse.fail("文件上传失败", null);
            }
            return McpToolResponse.success(fileName + "文件已成功上传至S3", s3Url);
        } catch (Exception e) {
            
            log.error("文件上传失败,req:{}", e);
            return McpToolResponse.fail(e.getMessage());
        }
    }

    @Tool(name = "upload_file_to_s3", description = "工具作用：上传文件到S3并获取文件URL")
    public McpToolResponse<String> uploadFileToS3AndGetFileUrl(@ToolParam(description = "file S3UploadRequest 必填", required = true) S3UploadRequest file) {
        Cat.logEvent(DEFAULT_TOOL, "upload_file_to_s3");
        try {

            if (file == null || file.getInputStream() == null || StringUtils.isBlank(file.getOriginalFileName())) {
                return McpToolResponse.fail("文件上传失败,入参有误！");
            }
            String fileName = file.getOriginalFileName();
            byte[] content = file.getInputStream().readAllBytes();
            InputStream inputStream = new ByteArrayInputStream(content);
            S3UploadRequest s3UploadRequest = new S3UploadRequest();
            s3UploadRequest.setInputStream(inputStream);
            s3UploadRequest.setOriginalFileName(fileName);
            String s3Url = s3Service.uploadFileToS3AndGetFileUrl(s3UploadRequest, null);
            if (StringUtils.isBlank(s3Url)) {
                return McpToolResponse.fail("文件上传失败", null);
            }
            return McpToolResponse.success(fileName + "文件已成功上传至S3", s3Url);
        } catch (Exception e) {
            
            log.error("文件上传失败,req:{}", e);
            return McpToolResponse.fail(e.getMessage());
        }
    }

    public McpToolResponse<String> uploadFileToS3AndGetFileUrl(@ToolParam(description = "file MultipartFile类型 必填", required = true) MultipartFile file) {
        Cat.logEvent(DEFAULT_TOOL, "upload_file_to_s3");
        try {
            Map<String, Object> fileData = new HashMap<>();
            fileData.put("name", file.getOriginalFilename());
            fileData.put("content", file.getBytes());
            fileData.put("contentType", file.getContentType());
            fileData.put("size", file.getSize());
            if (CollectionUtils.isEmpty(fileData) || fileData.get("content") == null || fileData.get("name") == null) {
                return McpToolResponse.fail("文件上传失败,入参有误！");
            }
            String fileName = (String) fileData.get("name");
            byte[] content = (byte[]) fileData.get("content");
            InputStream inputStream = new ByteArrayInputStream(content);
            S3UploadRequest s3UploadRequest = new S3UploadRequest();
            s3UploadRequest.setInputStream(inputStream);
            s3UploadRequest.setOriginalFileName(fileName);
            String s3Url = s3Service.uploadFileToS3AndGetFileUrl(s3UploadRequest, null);
            if (StringUtils.isBlank(s3Url)) {
                return McpToolResponse.fail("文件上传失败", null);
            }
            return McpToolResponse.success(fileName + "文件已成功上传至S3", s3Url);
        } catch (Exception e) {
            
            log.error("文件上传失败,req:{}", e);
            return McpToolResponse.fail(e.getMessage());
        }
    }

    @Tool(name = "save_intermediate_information", description = "工具作用：agent中间信息存储")
    public McpToolResponse<String> saveIntermediateInfo(
            @ToolParam(description = "查询key String类型 必填", required = true) String queryKey,
            @ToolParam(description = "存储内容,json串 非必填", required = false) String content) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "info_save_template");
        try {
            if (StringUtils.isEmpty(queryKey)) {
                return McpToolResponse.fail("入参queryKey有误", null);
            }
            log.info("key:{},content:{}", queryKey, content);
            boolean success = tairClient.put(queryKey, content, 3L, TimeUnit.DAYS);

            if (success) {
                return McpToolResponse.success("中间信息存储成功", null);
            } else {
                return McpToolResponse.fail("中间信息存储失败", null);
            }
        } catch (Exception e) {
            log.error("中间信息存储失败", e);
            
            t.setStatus(e);
            return McpToolResponse.fail("中间信息存储失败", null);
        } finally {
            t.complete();
            log.info("save_intermediate_information completed");

        }
    }

    @Tool(name = "query_intermediate_information", description = "工具作用：agent中间信息查询")
    public McpToolResponse<String> loadIntermediateInfo(
            @ToolParam(description = "查询key String类型 必填", required = true) String queryKey) {
        Transaction t = Cat.newTransaction(DEFAULT_TOOL, "info_load_template");
        try {
            log.info("key:{}", queryKey);
            if (StringUtils.isEmpty(queryKey)) {
                return McpToolResponse.fail("入参queryKey有误", null);
            }
            String intermediateInfo = tairClient.get(queryKey);
            if (StringUtils.isEmpty(intermediateInfo)) {
                return McpToolResponse.fail("入参queryKey有误", null);
            } else {
                return McpToolResponse.success("中间信息查询成功", intermediateInfo);
            }

        } catch (Exception e) {
            log.error("中间信息查询失败", e);
            
            t.setStatus(e);
            return McpToolResponse.fail("中间信息查询失败", null);
        } finally {
            t.complete();
            log.info("query_intermediate_information completed");

        }
    }


    @Tool(name = "send_message_via_elephant", description = "工具作用:通过大象发送消息。若appkey或fromUid或token为空，默认使用BMLMcpServer公众号发送消息。若发送群组消息，则需要传递大象群ID，即groupId字段;若发送个人消息，则需要传递大象用户列表，即userList字段")
    public McpToolResponse<String> sendMessageByDaXiang(@ToolParam(description = "发送大象消息公共入参") DXMessageRequest request) {
        Transaction transaction = Cat.newTransaction(DEFAULT_TOOL, "send_message_via_elephant");
        try {
            log.info("sendMessageByDaXiang req:{}", JSONObject.toJSONString(request));
            if (request == null || StringUtils.isBlank(request.getMessage()) || (request.getGroupId() == null && CollectionUtils.isEmpty(request.getUserList()))) {
                transaction.setStatus("参数有误！");
                return McpToolResponse.fail("参数有误，请检查入参！", null);
            }
            boolean success = true;
            if (request.getGroupId() != null) {
                success = dxService.sendMsg2Group(request.getAppKey(), request.getFromUid(), request.getMessage(), request.getGroupId(), request.getToken());
            }
            if (CollectionUtils.isNotEmpty(request.getUserList())) {
                success = dxService.sendMsg2Users(request);
            }
            if (!success) {
                transaction.setStatus("消息发送失败");
                return McpToolResponse.fail("消息发送失败", null);
            }
            transaction.setSuccessStatus();
            log.info("sendMessageByDaXiang resp:{}", "消息发送成功");
            return McpToolResponse.success("消息发送成功", null);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("sendMessageByDaXiang error,req:{}", request, e);
            
            return McpToolResponse.fail(e.getMessage());
        }
    }

    @Tool(name = "mcp_server_task_submitter", description = "工具作用：任务提交")
    public McpToolResponse<Map<String, String>> mcpServerTaskSubmitter(@ToolParam(description = "任务入参", required = true) McpServerMissionRequest request, @ToolParam(description = "工具执行上下文信息,无需传递,spring框架会自动传入", required = false) ToolContext toolContext) throws Exception {
        Transaction transaction = Cat.newTransaction(DEFAULT_TOOL, "mcp_server_task_submitter");
        if (request == null) {
            return McpToolResponse.fail("任务入参不能为空！", null);
        }
        String missionId = DigestUtils.md5Hex(JacksonUtil.toJsonStr(request) + System.currentTimeMillis());
        if (request.getMissionId() == null) {
            request.setMissionId(missionId);
        }
        String checkResult = checkMcpServerMissionRequestVaild(request);
        if (!StringUtils.isBlank(checkResult)) {
            return McpToolResponse.fail(checkResult, null);
        }
        try {
            String sessionId = ToolContextUtil.querySessionIdFromToolContext(toolContext);
            String missionRequestJson = request.getMissionRequest();
            McpMissionInfo mcpMissionInfo = new McpMissionInfo();

            mcpMissionInfo.setMissionId(missionId);
            mcpMissionInfo.setType(request.getMissionType());
            mcpMissionInfo.setMissionRequest(missionRequestJson);
            mcpMissionInfo.setAddTime(new Date());
            mcpMissionInfo.setStatus(0);
            //执行任务
            executeMissionSync(request, mcpMissionInfo, missionId);

            Thread.sleep(1000);
            Map<String, String> result = new HashMap<>();
            result.put("missionId", missionId);
            result.put("sessionId", sessionId);
            result.put("isLongTermTask", "是");
            transaction.setSuccessStatus();
            return McpToolResponse.success("任务提交成功！", result);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("任务提交失败,req:{}", request, e);
            
            return McpToolResponse.fail(e.getMessage(), null);
        }
    }

    private String checkMcpServerMissionRequestVaild(McpServerMissionRequest missionRequest) {
        Integer missionType = missionRequest.getMissionType();
        if (missionType == null) {
            return "任务类型不能为空,missionId:" + missionRequest.getMissionId();
        }
        if (missionRequest.getMissionRequest() == null) {
            return "任务请求不能为空,missionId:" + missionRequest.getMissionId();
        }
        if (missionType == 3) {
            String loadTestRequestJson = missionRequest.getMissionRequest();
            TMcpTestMissionRequest tMcpTestMissionRequest = JSONObject.parseObject(loadTestRequestJson, TMcpTestMissionRequest.class);
            if (tMcpTestMissionRequest == null) {
                return "任务请求不能为空,missionId:" + missionRequest.getMissionId();
            }
            if (tMcpTestMissionRequest.getIndustryType() == null) {
                return "任务请求行业类型不能为空,missionId:" + missionRequest.getMissionId();
            } else if (tMcpTestMissionRequest.getBu() == null) {
                return "任务请求bu不能为空,missionId:" + missionRequest.getMissionId();
            } else if (tMcpTestMissionRequest.getTestType() == null) {
                return "任务请求测试类型不能为空,missionId:" + missionRequest.getMissionId();
            } else if (StringUtils.isBlank(tMcpTestMissionRequest.getModelName())) {
                return "任务请求模型名称不能为空,missionId:" + missionRequest.getMissionId();
            } else if (StringUtils.isBlank(tMcpTestMissionRequest.getModelType())) {
                return "任务请求模型类型不能为空,missionId:" + missionRequest.getMissionId();
            }else if ("tf".equals(tMcpTestMissionRequest.getModelType()) && MapUtils.isEmpty(tMcpTestMissionRequest.getExtraInfo())) {
                return "任务请求模型额外信息不能为空,missionId:" + missionRequest.getMissionId();
            } else if (StringUtils.isEmpty(tMcpTestMissionRequest.getSessionId())) {
                return "任务请求sessionId不能为空,missionId:" + missionRequest.getMissionId();
            }
            if (!"llm".equals(tMcpTestMissionRequest.getModelType())) {
                if (StringUtils.isBlank(tMcpTestMissionRequest.getModelVersion())) {
                    return "任务请求模型版本不能为空,missionId:" + missionRequest.getMissionId();
                } else if (StringUtils.isBlank(tMcpTestMissionRequest.getModelPath())) {
                    return "任务请求模型路径不能为空,missionId:" + missionRequest.getMissionId();
                }
            }
        }
        return null;
    }

    private void executeMissionSync(McpServerMissionRequest missionRequest, McpMissionInfo mcpMissionInfo, String missionId) {
        ThreadPoolExecutor taskRunThreadPool = ThreadPoolFactory.getInvokeMissionThreadPool();
        taskRunThreadPool.execute(() -> {
            String misId = null;
            try {
                Cat.logEvent("executeMissionSync", missionRequest.getMissionType().toString());
                log.info("executeMissionSync start,missionId:{}", missionId);
                Integer missionType = missionRequest.getMissionType();
                McpMissionCommonContext mcpMissionCommonContext = new McpMissionCommonContext();

                if (missionType == 3) {
                    mcpMissionCommonContext = matchToolService.executeMcpLoadTestTask(missionRequest.getMissionRequest(), missionId);
                    misId = mcpMissionCommonContext.getMisId();
                } else {
                    //暂时只适配压测任务
                    throw new Exception("任务类型有误！missionId:" + missionId + "missionType:" + missionType);
                }
                mcpMissionInfo.setStatus(mcpMissionCommonContext.getStatus());
                mcpMissionInfo.setMisId(misId);
                mcpMissionInfo.setUpdateTime(new Date());
                mcpMissionInfo.setMissionResult(mcpMissionCommonContext.getMissionResult());
                mcpMissionInfo.setMissionContext(JSONObject.toJSONString(mcpMissionCommonContext));
                mcpMissionDao.insertMcpMissionInfo(mcpMissionInfo);
                log.info("executeMissionSync end,missionId:{},mcpMissionInfo:{}", missionId, mcpMissionInfo);
            } catch (Exception e) {
                log.error("executeMissionSync error,missionId:{}", missionId, e);
                
                McpMissionInfo mcpMissionInfoByMissionId = mcpMissionDao.getMcpMissionInfoByMissionId(missionId);
                String missionContext = mcpMissionInfoByMissionId.getMissionContext();
                McpMissionCommonContext mcpMissionCommonContext = JSONObject.parseObject(missionContext, McpMissionCommonContext.class);
                mcpMissionCommonContext.setExecuteMessage(e.getMessage());
                mcpMissionCommonContext.setStatus(-1);
                mcpMissionInfoByMissionId.setMissionContext(JSONObject.toJSONString(mcpMissionCommonContext));
                mcpMissionInfoByMissionId.setStatus(-1);
                mcpMissionInfoByMissionId.setMisId(misId);
                mcpMissionInfoByMissionId.setUpdateTime(new Date());
                mcpMissionDao.updateMissionInfo(mcpMissionInfoByMissionId);
            }
        });
    }
    @Tool(name ="fetch_sub_doc_link_and_content_based_on_parent_doc_id",description = "工具作用:根据父级文档ID获取子文档链接和内容")
    public McpToolResponse<List<Map<String, Object>>> fetchSubDocLinkAndContentBasedOnParentDocId(@ToolParam(description = "学城文档获取请求入参") XueChengRequest xueChengRequest) {
        McpToolResponse<List<Map<String, Object>>> listMcpToolResponse = xmCitadelService.fetchSubDocLinkAndContentBasedOnParentDocId(xueChengRequest);
        return listMcpToolResponse;
    }
    @Tool(name = "talos_query", description = "工具作用:talos查询")
    public TalosQueryResponse<List<List<Object>>> talosQuery(@ToolParam(description = "talos查询请求入参") TalosRequest talosRequest) {
        TalosQueryResponse<List<List<Object>>> listTalosQueryResponse = talosQueryService.executeQuery(talosRequest);
        return listTalosQueryResponse;
    }

    @Tool(name = "query_mcp_mission_result_by_mission_id", description = "根据任务ID查询任务执行结果")
    public McpToolResponse queryMatchingTestToolTaskResult(@ToolParam(description = "missionId 任务id String") String missionId) {

        Transaction t = Cat.newTransaction(MatchTool.class.getSimpleName(), "query_mcp_mission_result_by_mission_id");
        if (StringUtils.isBlank(missionId)) {
            t.setStatus("任务ID为空");
            return McpToolResponse.paramsError();
        }
        Cat.logEvent("queryMatchingTestToolTaskResult", missionId);
        try {
            log.info("查询任务结果，missionId：{}", missionId);
            McpMissionInfo mcpMissionInfoByMissionId = mcpMissionDao.getMcpMissionInfoByMissionId(missionId);
            if (mcpMissionInfoByMissionId == null) {
                throw new RuntimeException("任务不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("status", mcpMissionInfoByMissionId.getStatus());
            String missionResultJson = mcpMissionInfoByMissionId.getMissionResult();
            if (!StringUtils.isBlank(missionResultJson) && isJsonMap(missionResultJson)) {
                Map map = JSONObject.parseObject(missionResultJson, Map.class);
                result.put("result", map.get("result"));
                result.put("link", map.get("link"));
            } else if (!StringUtils.isBlank(missionResultJson) && isListJson(missionResultJson) && !"[]".equals(missionResultJson)) {
                List list = JSONObject.parseObject(missionResultJson, List.class);
                JSONObject reporterJSON = (JSONObject) list.get(0);
                result.put("link", reporterJSON.getString("reporterAddr"));
            } else {
                result.put("result", missionResultJson);
            }
            t.setSuccessStatus();
            log.info("查询MCP任务结果成功：missionId：{},result{}", missionId, JSONObject.toJSONString(result));
            return McpToolResponse.success("查询成功", result);
        } catch (Exception e) {
            t.setStatus(e);
            
            log.error("查询Friday任务结果失败！missionId:{}", missionId, e);
            return McpToolResponse.fail(e.getMessage(), null);
        }
    }

    @Tool(name = "query_mcp_mission_result_by_mission_id_jsonpath", description = "根据任务ID和jsonpath查询任务执行结果")
    public McpToolResponse queryMatchingTestToolTaskResultV2(@ToolParam(description = "missionId 任务id String") String missionId, @ToolParam(description = "jsonpath 期望的取值路径 String", required = false) String jsonpath) {

        Transaction t = Cat.newTransaction(MatchTool.class.getSimpleName(), "query_mcp_mission_result_by_mission_id");
        if (StringUtils.isBlank(missionId)) {
            t.setStatus("任务ID为空");
            return McpToolResponse.paramsError();
        }
        Cat.logEvent("queryMatchingTestToolTaskResult", missionId);
        try {
            log.info("查询任务结果，missionId：{}", missionId);
            McpMissionInfo mcpMissionInfoByMissionId = mcpMissionDao.getMcpMissionInfoByMissionId(missionId);
            // 如果提供了jsonpath，则使用jsonpath提取值并更新missionResult
            if (jsonpath != null && !StringUtils.isBlank(jsonpath)) {
                String missionResult = mcpMissionInfoByMissionId.getMissionResult();
                if (!StringUtils.isBlank(missionResult)) {
                    try {
                        // 使用JSONPath提取值
                        Object extractedValue = com.jayway.jsonpath.JsonPath.read(missionResult, jsonpath);
                        String extractedStr = extractedValue != null ? extractedValue.toString() : "";

                        // 更新missionResult
                        mcpMissionInfoByMissionId.setMissionResult(extractedStr);

                        log.info("使用JSONPath提取值成功，missionId：{}, jsonpath：{}, extractedValue：{}",
                                missionId, jsonpath, extractedStr);
                    } catch (Exception e) {
                        log.error("JSONPath提取失败，missionId：{}, jsonpath：{}", missionId, jsonpath, e);
                        return McpToolResponse.fail("JSONPath提取失败: " + e.getMessage(), null);
                    }
                }
            }
            if (mcpMissionInfoByMissionId == null) {
                throw new RuntimeException("任务不存在");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("status", mcpMissionInfoByMissionId.getStatus());
            String missionResultJson = mcpMissionInfoByMissionId.getMissionResult();
            if (!StringUtils.isBlank(missionResultJson) && isJsonMap(missionResultJson)) {
                Map map = JSONObject.parseObject(missionResultJson, Map.class);
                result.put("result", map.get("result"));
                result.put("link", map.get("link"));
            } else if (!StringUtils.isBlank(missionResultJson) && isListJson(missionResultJson) && !"[]".equals(missionResultJson)) {
                List list = JSONObject.parseObject(missionResultJson, List.class);
                JSONObject reporterJSON = (JSONObject) list.get(0);
                result.put("link", reporterJSON.getString("reporterAddr"));
            } else {
                result.put("result", missionResultJson);
            }
            t.setSuccessStatus();
            log.info("查询MCP任务结果成功：missionId：{},result{}", missionId, JSONObject.toJSONString(result));
            return McpToolResponse.success("查询成功", result);
        } catch (Exception e) {
            t.setStatus(e);
            
            log.error("查询Friday任务结果失败！missionId:{}", missionId, e);
            return McpToolResponse.fail(e.getMessage(), null);
        }
    }

    private boolean isListJson(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return false;
        }
        String trimmed = jsonString.trim();
        return trimmed.startsWith("[") && trimmed.endsWith("]");
    }

    public static boolean isJsonMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return false;
        }
        String trimmed = jsonString.trim();
        return trimmed.startsWith("{") && trimmed.endsWith("}");
    }

    @Tool(name = "knowledge_collector", description = "工具作用: 将总结的经验或知识存储至知识库中\n" +
            "## 工具概述\n" +
            "**工具名称**: knowledge_collector  \n" +
            "**工具作用**: 将总结的经验或知识存储至知识库中\n" +
            "\n" +
            "## 输入参数\n" +
            "agentCode (String, 必填写，且不能为空字符串)\n" +
            "  - 代理编码标识，用于标识具体的代理实例\n" +
            "\n" +
            "knowledgeKey(Long, 必填写)\n" +
            "  - 知识键值，用于唯一标识知识条目的关键字或标识符\n" +
            "\n" +
            "knowledgeData(String, 必填写)\n" +
            "  - 知识数据内容，存储具体的知识信息，这个字段的值是一个对象tojsonString后的字符串，在传参时请传递json字符串\n" +
            "\n" +
            "collectId(Long, 必填写)\n" +
            "  - 用于生成知识的源数据ID\n" +
            "\n" +
            "## 注意将以上参数包装成一个json来发起请求，请求示例：\n" +
            "{\"agentCode\":\"zb-schedule-agent-learn\",\"knowledgeKey\":123123123,\"knowledgeData\":\"{\\\"name\\\":\\\"测试\\\",\\\"desc\\\":\\\"测试数据\\\",\\\"question\\\":\\\"hello?\\\",\\\"answer\\\":\\\"world\\\",\"docId\\\":12312312331}\",\"collectId\":123456789}"
    )
    public McpToolResponse<Long> knowledgeCollector(@ToolParam(description = "知识收集请求入参", required = true) TKnowledgeCollectRequestDto tKnowledgeCollectRequest) {
        Transaction transaction = Cat.newTransaction(DEFAULT_TOOL, "knowledge_collector");
        try {
            log.info("knowledgeCollector req:{}", JSONObject.toJSONString(tKnowledgeCollectRequest));
            if (tKnowledgeCollectRequest == null || StringUtils.isBlank(tKnowledgeCollectRequest.getAgentCode())
                    || StringUtils.isBlank(tKnowledgeCollectRequest.getKnowledgeData())
                    || tKnowledgeCollectRequest.getKnowledgeKey() == null
                    || tKnowledgeCollectRequest.getCollectId() == null) {
                transaction.setStatus("参数有误！");
                return McpToolResponse.paramsError("参数有误，请检查入参！", null);
            }
            TKnowledgeCollectRequest req = new TKnowledgeCollectRequest();
            req.setAgentCode(tKnowledgeCollectRequest.getAgentCode());
            req.setKnowledgeData(tKnowledgeCollectRequest.getKnowledgeData());
            req.setCollectId(tKnowledgeCollectRequest.getCollectId());
            req.setKnowledgeKey(tKnowledgeCollectRequest.getKnowledgeKey());
            TBaseReponse tBaseReponse = tAgentLearningService.collectKnowledges(req);
            if (tBaseReponse.getCode() != 0) {
                transaction.setStatus("知识收集失败：tKnowledgeCollectRequest" + JSONObject.toJSONString(tKnowledgeCollectRequest));
                log.error("knowledgeCollector fail,req:{}", JSONObject.toJSONString(tKnowledgeCollectRequest));
                return McpToolResponse.fail("知识收集失败:" + tBaseReponse.getMessage(), tKnowledgeCollectRequest.getKnowledgeKey());
            }
            transaction.setSuccessStatus();
            log.info("knowledgeCollector success,knowledgeKey:{}", tKnowledgeCollectRequest.getKnowledgeKey());
            return McpToolResponse.success("知识收集成功", tKnowledgeCollectRequest.getKnowledgeKey());
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("knowledgeCollector error,req:{}", tKnowledgeCollectRequest, e);
            
            return McpToolResponse.fail("知识收集失败:" + e.getMessage(), null);
        } finally {
            transaction.complete();
        }
    }
}
