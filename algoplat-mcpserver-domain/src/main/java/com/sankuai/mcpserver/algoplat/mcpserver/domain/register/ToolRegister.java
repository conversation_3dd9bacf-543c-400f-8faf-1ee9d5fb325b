package com.sankuai.mcpserver.algoplat.mcpserver.domain.register;


import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import org.springframework.ai.tool.ToolCallback;

public interface ToolRegister {

    /**
     * 注册工具
     *
     * @param context 注册上下文
     */
    ToolCallback registerTool(ToolRegisterContext context,Long toolId);

    /**
     * 注销工具
     *
     * @param toolInfo 工具信息
     */
    Long unregisterTool(ToolInfo toolInfo);

    /**
     * 获取工具类型
     *
     * @return 工具类型枚举
     */

    ToolTypeEnum getToolType();

    /**
     * 获取工具回调
     */
    ToolCallback getToolCallback(ToolRegisterContext context);

    void doUnregister(String mcpServerBeanName, String toolName);

    void unregisterToolForMcpServer(String mcpServerBeanName, ToolInfo toolInfo);
}
