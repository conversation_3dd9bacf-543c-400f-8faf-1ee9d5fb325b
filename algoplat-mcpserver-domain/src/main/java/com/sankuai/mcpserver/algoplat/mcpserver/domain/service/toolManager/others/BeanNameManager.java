package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.SpringContextUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.BusinessLineDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpServerDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import lombok.extern.slf4j.Slf4j;

import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants.ROUTER_FUNCTION;
import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants.TRANSPORT_PROVIDER;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/21
 */

@Slf4j
public class BeanNameManager {

    public static String genMcpServerBeanName(String bussinessName, String mcpServerName){
        return bussinessName + "_" + mcpServerName;
    }
    public static String genTransportProviderBeanName(String businessLine, String mcpServerName){
        return businessLine + "_" + mcpServerName + TRANSPORT_PROVIDER;
    }
    public static String genRouterFunctionBeanName(String businessLine, String mcpServerName){
        return businessLine + "_" + mcpServerName + ROUTER_FUNCTION;
    }
    public static String genSyncServerBeanName(String businessLine, String mcpServerName){
        return businessLine + "_" + mcpServerName;
    }


    public static String genPigeonToolKey(PigeonToolInfo pigeonToolInfo) {
        return pigeonToolInfo.getAppKey() + "_" + pigeonToolInfo.getInterfaceName() + "_" + pigeonToolInfo.getCell() + "_" + pigeonToolInfo.getTimeOut();
    }

    public static String genThriftProxyKey(ThriftToolInfo thriftToolInfo) {
        return thriftToolInfo.getAppKey() + "_" + thriftToolInfo.getInterfaceName() + "_" + thriftToolInfo.getCell() + "_" + thriftToolInfo.getIp() + "_" + thriftToolInfo.getPort() + "_" + thriftToolInfo.getTimeOut();
    }

    public static String getMcpServerBeanNameByToolInfo(ToolInfo toolInfo) {
        Long mcpServerId = toolInfo.getMcpServerId();
        McpServerDao mcpServerDao = (McpServerDao) SpringContextUtils.getBean("mcpServerDao");
        McpServerEntity mcpServerEntity = mcpServerDao.selectMcpServerById(mcpServerId);
        Long businessLineId = mcpServerEntity.getBusinessLineId();
        BusinessLineDao businessLineDao = (BusinessLineDao) SpringContextUtils.getBean("businessLineDao");
        BusinessLine businessLineById = businessLineDao.getBusinessLineById(businessLineId);
        return BeanNameManager.genMcpServerBeanName(businessLineById.getBusinessLine(), mcpServerEntity.getMcpServerName());
    }
}
