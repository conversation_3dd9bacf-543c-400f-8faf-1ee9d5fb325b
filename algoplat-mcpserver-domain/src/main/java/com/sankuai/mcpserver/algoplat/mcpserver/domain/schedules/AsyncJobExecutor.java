package com.sankuai.mcpserver.algoplat.mcpserver.domain.schedules;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/18 11:55
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.JobExecutorService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.AsynJobExecutorStatusEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.factory.ThreadPoolFactory;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AsyncJobExecutor {

    // 线程池用于执行异步任务
    private static final ExecutorService executor = ThreadPoolFactory.getInvokeMissionThreadPool();

    /**
     * 提交一个异步任务
     * 
     * @param jobExecutorService
     * @return
     */
    public static <T, R> Boolean submitTask(JobExecutorService jobExecutorService,
        String missionId, Function<T, R> function, T toolRequest) {

        // 设置任务初始状态为等待执行，存储到数据库
        McpMissionInfo mcpMissionInfo = jobExecutorService.withPreProcessor(missionId, (Map<String, Object>)toolRequest);

        executor.submit(() -> {
            try {
                // 执行BiFunction
                R result = function.apply(toolRequest);
                log.info("AsyncJobExecutor，function.apply，missionId：{}, toolRequest：{}, result：{}",
                        missionId, JSONObject.toJSONString(toolRequest), JSONObject.toJSONString(result));
                // 根据实际需求选择合适的转换方式
                String resultString;
                if (result == null) {
                    resultString = Strings.EMPTY;
                } else if (result instanceof String) {
                    resultString = (String) result;  // 直接转换
                    if (!isJsonMap(resultString)) {
                        resultString = JSON.toJSONString(result);
                        log.info("AsyncJobExecutor，JSON.toJSONString，missionId：{}, resultString：{}", missionId, resultString);
                    }
                } else {
                    resultString = JSON.toJSONString(result);  // JSON序列化，保持结构信息
                }
                // 执行完成，存储结果到数据库
                jobExecutorService.withPostProcessor(mcpMissionInfo, AsynJobExecutorStatusEnum.COMPLETED, resultString);

            } catch (Exception e) {
                // 执行失败，存储错误信息到数据库
                jobExecutorService.withErrorHandler(mcpMissionInfo, e);
            }
        });
        return Boolean.TRUE;
    }

    public static boolean isJsonMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return false;
        }
        String trimmed = jsonString.trim();
        return trimmed.startsWith("{") && trimmed.endsWith("}");
    }

}