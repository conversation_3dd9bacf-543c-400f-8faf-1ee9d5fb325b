package com.sankuai.mcpserver.algoplat.mcpserver.domain.constant;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/4
 */
public final class ServiceConstants {

    public static final String APP_KEY = "com.sankuai.algoplatform.mcpserver";

    public static final String JOB_NAME = "ToolInvoke";

    public static final String TENANT_ID = "434610572891277";

    public static final String FRIDAY_MODEAL_REGIST_URL = "https://friday.sankuai.com/api/friday/ml/v1/modelregistry/create";

    public static final String FRIDAY_GET_TRAINING_LIST = "https://aigc.sankuai.com/api/friday/ml/v1/modelregistry/runList";

    public static final String FRIDAY_DEPLOY_LLM_MODEL = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/createService";

    public static final String FRIDAY_GET_MODEL_LIST = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/models";

    public static final String FRIDAY_GET_GPU_QUEUE = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/getDeployResource";

    public static final String FRIDAY_GET_QUEUE_GPU_INFO = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/gpus";

    public static final String GROUP_NAME = "al-zb-ddpt";

    public static final String FRIDAY_MODEL_STATUS_URL = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/serviceInfo";

    public static final String FRIDAY_DEPLOY_LIST_URL = "https://friday.sankuai.com/api/friday/ml/v1/deploy/list";

    public static final String PARSER_BUSSLINE = "Parser";

    public static final String MATCHER_BUSSLINE = "Matcher";

    public static final String TRANSPORT_PROVIDER = "_TransportProvider";

    public static final String ROUTER_FUNCTION = "_RouterFunction";

    public static final String SERVER = "_Server";

    public static final String DEFAULT = "DEFAULT";

    public static final String FRIDAY_UPLOAD_TRAING_DATA_SET = "https://aigc.sankuai.com/api/friday/ml/v1/dm/dataset/create";

    public static final String FRIDAY_CREATE_TRAINING_TASK = "https://aigc.sankuai.com/api/friday/ml/v1/task/submit";

    public static final String FRIDAY_CREATE_TRAINING_TASK_GET_STF_TYPE = "https://friday.sankuai.com/api/friday/ml/v1/task/getSftType";

    public static final String FRIDAY_CREATE_TRAINING_TASK_GET_ADVANCED_PARAMS = "https://friday.sankuai.com/api/friday/ml/v1/task/getAdvancedParams";

    public static final String FRIDAY_CREATE_TRAINING_TASK_GET_MODEL_INFORMATION = "https://friday.sankuai.com/api/friday/ml/v1/model/type/information/list";

    public static final String FRIDAY_CREATE_TARINING_TASK_GET_DATASET = "https://aigc.sankuai.com/api/friday/ml/v1/dm/dataset/all_version";

    public static final String FRIDAY_CREATE_TRAINING_TASK_GET_DATASET_VERSION = "https://aigc.sankuai.com/api/friday/ml/v1/dm/dataset/list";

    public static final String FRIDAY_CREATE_TRAINING_TASK_STATUS_QUERY = "https://aigc.sankuai.com/api/friday/ml/v1/task/detail";

    public static final String FRIDAYUPLOAD_TRAING_DATA_SET_QUERY_STATUS = "https://aigc.sankuai.com/api/friday/ml/v1/dm/version/list";


    public static final String FRIDAY_METRICS_LINK_URL = "https://friday.sankuai.com/api/friday/ml/v1/task/metricslink";

    public static final String MLP_JOB_BASIC_URL = "https://mlp.sankuai.com/mlapi/kub/run/map/job/basic";

    public static final String MLP_JOB_RANK_URL = "https://mlp.sankuai.com/mlapi/kub/series/job/rank/default";

    public static final String MLP_SERIES_SUMMARY_URL = "https://mlp.sankuai.com/mlapi/kub/series/summary";

    public static final String FRIDAY_TASK_DETAIL_URL = "https://friday.sankuai.com/api/friday/ml/v1/task/detail";

    public static final String FRIDAY_QUERY_DATASET_LIST = "https://aigc.sankuai.com/api/friday/ml/v1/dm/dataset/list";

    public static final String FRIDAY_QUERY_DATASET_INFO_BY_ID = "https://aigc.sankuai.com/api/friday/ml/v1/dm/dataset/get";

    public static final String FRIDAY_ADD_DATASET_VERSION = "https://aigc.sankuai.com/api/friday/ml/v1/dm/version/create";

    public static final String FRIDAY_COOKIE_CACHE_FREFIX = "bml_match_agent_friday_cookie_";

    public static final String FRIDAY_RETRY_MODEL_TRAING_TASK = "https://aigc.sankuai.com/api/friday/ml/v1/task/rerun";

    public static final String FRIDAY_GET_MODEL_INFO = "https://aigc.sankuai.com/api/friday/ml/v1/deploy/list";


    public static final String OFFLINE_DOC_URL = "https://km.it.test.sankuai.com/collabpage/";

    public static final String ONLINE_DOC_URL = "https://km.sankuai.com/collabpage/";
}
