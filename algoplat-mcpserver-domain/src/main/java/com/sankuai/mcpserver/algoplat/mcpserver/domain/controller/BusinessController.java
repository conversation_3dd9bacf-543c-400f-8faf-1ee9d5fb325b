package com.sankuai.mcpserver.algoplat.mcpserver.domain.controller;

import com.dianping.cat.Cat;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.BusinessService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * 业务线控制器
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Slf4j
@RestController
@RequestMapping("/business")
public class BusinessController {

    @Resource
    private BusinessService businessService;

    /**
     * 获取所有业务线
     * @return 业务线列表
     */
    @GetMapping("/list")
    public McpServerResponse getBusinessList() {
        try {
            log.info("获取所有业务线");
            return businessService.getAllBusinessLine();
        } catch (Exception e) {
            log.error("获取业务线列表失败", e);
            return McpServerResponse.fail("获取业务线列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建业务线
     * @param businessLine 业务线信息
     * @return 创建结果
     */
    @PostMapping("/create")
    public McpServerResponse createBusinessLine(@RequestBody BusinessLine businessLine) {
        try {
            log.info("创建业务线, businessLine={}", businessLine);

            // 参数校验
            if (businessLine == null) {
                return McpServerResponse.fail("业务线信息不能为空");
            }

            if (StringUtils.isEmpty(businessLine.getBusinessLine())) {
                return McpServerResponse.fail("业务线名称不能为空");
            }

            if (StringUtils.isEmpty(businessLine.getDescription())) {
                return McpServerResponse.fail("业务线描述不能为空");
            }

            // 检查业务线名称是否已存在
            McpServerResponse checkResponse = businessService.checkBusinessLineNameIsExist(businessLine.getBusinessLine().trim());
            if (checkResponse.getCode() != 0) {
                return checkResponse;
            }

            String data = checkResponse.getData();
            if (data != null && !data.equals("null") && !data.isEmpty()) {
                return McpServerResponse.fail("业务线名称已存在，请重新输入");
            }

            // Spring中创建通信


            return businessService.createBusinessLine(businessLine);
        } catch (Exception e) {
            
            log.error("创建业务线失败, businessLine={}", businessLine, e);
            return McpServerResponse.fail("创建业务线失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取业务线
     * @param id 业务线ID
     * @return 业务线详情
     */
    @GetMapping("/getBusinessLineById")
    public McpServerResponse getBusinessLineById(@RequestParam("id") Long id) {
        try {
            log.info("根据ID获取业务线, id={}", id);

            // 参数校验
            if (id == null) {
                return McpServerResponse.fail("业务线ID不能为空");
            }

            return businessService.getBusinessLineById(id);
        } catch (Exception e) {
            
            log.error("获取业务线失败, id={}", id, e);
            return McpServerResponse.fail("获取业务线失败: " + e.getMessage());
        }
    }

    /**
     * 更新业务线
     * @param businessLine 业务线信息
     * @return 更新结果
     */
    @PostMapping("/update")
    public McpServerResponse updateBusinessLine(@RequestBody BusinessLine businessLine) {
        try {
            log.info("更新业务线, businessLine={}", businessLine);

            // 参数校验
            if (businessLine == null) {
                return McpServerResponse.fail("业务线信息不能为空");
            }

            if (businessLine.getId() == null) {
                return McpServerResponse.fail("业务线ID不能为空");
            }
            // 检查业务线是否存在
            McpServerResponse businessLineById = businessService.getBusinessLineById(businessLine.getId());
            if (businessLineById.getCode() != 0 || businessLineById.getData() == null) {
                return McpServerResponse.fail("业务线不存在或已被删除");
            }
            return businessService.updateBusinessLine(businessLine);
        } catch (Exception e) {
            
            log.error("更新业务线失败, businessLine={}", businessLine, e);
            return McpServerResponse.fail("更新业务线失败: " + e.getMessage());
        }
    }

    /**
     * 删除业务线
     * @param businessLineId 业务线ID
     * @return 删除结果
     */
    @GetMapping("/delete/{businessLineId}")
    public McpServerResponse deleteBusinessLine(@PathVariable Long businessLineId) {
        try {
            log.info("删除业务线, businessLineId={}", businessLineId);

            // 参数校验
            if (businessLineId == null) {
                return McpServerResponse.fail("业务线ID不能为空");
            }

            return businessService.deleteBusinessLine(businessLineId);
        } catch (Exception e) {
            
            log.error("删除业务线失败, businessLineId={}", businessLineId, e);
            return McpServerResponse.fail("删除业务线失败: " + e.getMessage());
        }
    }

    /**
     * 根据名称获取业务线
     * @param businessLineName 业务线名称
     * @return 业务线详情
     */
    @GetMapping("/search")
    public McpServerResponse getBusinessLineByBusiness(@RequestParam("name") String businessLineName) {
        try {
            log.info("根据名称获取业务线, businessLineName={}", businessLineName);

            // 参数校验
            if (StringUtils.isEmpty(businessLineName)) {
                return McpServerResponse.fail("业务线名称不能为空");
            }

            return businessService.getBusinessLineByBusiness(businessLineName.trim());
        } catch (Exception e) {
            
            log.error("获取业务线失败, businessLineName={}", businessLineName, e);
            return McpServerResponse.fail("获取业务线失败: " + e.getMessage());
        }
    }
}
