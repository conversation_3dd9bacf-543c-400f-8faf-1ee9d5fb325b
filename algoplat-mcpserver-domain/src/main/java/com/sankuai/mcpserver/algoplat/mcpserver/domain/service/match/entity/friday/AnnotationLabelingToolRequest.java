package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnnotationLabelingToolRequest {
    /**
     * 业务线
     * 1001-境内住宿 1002-境外住宿 1006-到餐 1007-到综 (服务零售)
     */
    @ToolParam(description = "产业类型 1001-境内住宿 1002-境外住宿 1006-到餐 1007-到综 (服务零售)", required = true)
    private Integer bizLine;

    /**
     * 场景名称
     * 正餐/轻快餐 美发/球类运动/美甲 境内直连/境外直连
     */
    @ToolParam(description = "场景名称.餐：正餐/轻快餐 到综：美发/球类运动/美甲 境内境外住宿：境内直连/境外直连", required = true)
    private String sceneName;

    /**
     * 匹配类型
     * 1-精准 2-近似 3-同品
     */
    @ToolParam(description = "场景类型 1-精准 2-近似 3-同品", required = true)
    private Integer matchType;

    /**
     * 标注类型
     * 1-单侧信息标注 2-单层匹配标注 3-多层匹配标注 4-大模型推理评价标注 5-图像标注  6-要素抽取  7-类型识别
     */
    @ToolParam(description = "标注任务类型1-单侧信息标注 2-单层匹配标注 3-多层匹配标注 4-大模型推理评价标注 5-图像标注 6-要素抽取 7-类型识别", required = true)
    private Integer annotationType;

    /**
     * 数据集列表
     */
    @ToolParam(description = "数据集名称列表", required = true)
    private List<String> dataSetName;
}
