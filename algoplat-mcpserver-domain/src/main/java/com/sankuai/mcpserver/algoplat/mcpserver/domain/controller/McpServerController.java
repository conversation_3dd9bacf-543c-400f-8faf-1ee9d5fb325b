package com.sankuai.mcpserver.algoplat.mcpserver.domain.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.impl.McpServerServiceImpl;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * McpServer控制器
 *
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Slf4j
@RestController
@RequestMapping("/server")
public class McpServerController {

    @Resource
    private McpServerService mcpServerService;

    /**
     * 获取所有McpServer
     *
     * @return 所有McpServer列表
     */
    @GetMapping("/all")
    public McpServerResponse getAllMcpServers() {
        try {
            List<McpServerEntity> mcpServers = mcpServerService.getAllMcpServers();
            return McpServerResponse.success("获取所有McpServer列表成功", JSON.toJSONString(mcpServers));
        } catch (Exception e) {
            log.error("获取所有McpServer列表失败", e);
            return McpServerResponse.fail("获取所有McpServer列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取McpServer
     *
     * @param id McpServer ID
     * @return McpServer详情
     */
    @GetMapping("/detail/{id}")
    public McpServerResponse getMcpServerById(@PathVariable Long id) {
        try {
            // 参数校验
            if (id == null) {
                return McpServerResponse.fail("McpServer ID不能为空");
            }

            // 使用新方法获取包含工具信息的McpServer
            McpServerEntity mcpServer = ((McpServerServiceImpl) mcpServerService).getMcpServerWithTools(id);
            if (mcpServer == null) {
                return McpServerResponse.fail("McpServer不存在或已被删除");
            }
            return McpServerResponse.success("获取McpServer详情成功", JSON.toJSONString(mcpServer));
        } catch (Exception e) {
            log.error("获取McpServer详情失败, id={}", id, e);
            return McpServerResponse.fail("获取McpServer详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据业务线ID获取McpServer列表
     *
     * @param businessLineId 业务线ID
     * @return McpServer列表
     */
    @GetMapping("/by-business/{businessLineId}")
    public McpServerResponse getMcpServersByBusinessLineId(@PathVariable Long businessLineId) {
        try {
            // 参数校验
            if (businessLineId == null) {
                return McpServerResponse.fail("业务线ID不能为空");
            }

            List<McpServerEntity> mcpServers = mcpServerService.getMcpServersByBusinessLineId(businessLineId);
            return McpServerResponse.success("获取业务线下McpServer列表成功", JSON.toJSONString(mcpServers));
        } catch (Exception e) {
            log.error("获取业务线下McpServer列表失败, businessLineId={}", businessLineId, e);
            return McpServerResponse.fail("获取业务线下McpServer列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据名称获取McpServer列表
     *
     * @param name McpServer名称
     * @return McpServer列表
     */
    @GetMapping("/by-name/{name}")
    public McpServerResponse getMcpServersByName(@PathVariable String name) {
        try {
            // 参数校验
            if (name == null || name.trim().isEmpty()) {
                return McpServerResponse.fail("McpServer名称不能为空");
            }

            List<McpServerEntity> mcpServers = mcpServerService.getMcpServersByName(name);
            return McpServerResponse.success("获取指定名称的McpServer列表成功", JSON.toJSONString(mcpServers));
        } catch (Exception e) {
            log.error("获取指定名称的McpServer列表失败, name={}", name, e);
            return McpServerResponse.fail("获取指定名称的McpServer列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建McpServer
     *
     * @param request 请求参数
     * @return 创建结果
     */
    @PostMapping("/create")
    public McpServerResponse createMcpServer(@RequestBody McpServerRequest request) {
        try {
            // 参数校验
            if (request == null) {
                return McpServerResponse.fail("请求参数不能为空");
            }

            if (StringUtils.isEmpty(request.getMcpServerName())) {
                return McpServerResponse.fail("McpServer名称不能为空");
            }
            // 检查名称是否已存在
            if (mcpServerService.existsMcpServerByName(request.getMcpServerName().trim())) {
                return McpServerResponse.fail("McpServer名称已存在");
            }
            // 创建实体
            McpServerEntity mcpServerEntity = new McpServerEntity();
            mcpServerEntity.setMcpServerName(request.getMcpServerName().trim());
            mcpServerEntity.setBusinessLineId(request.getBusinessLineId());
            mcpServerEntity.setDescription(request.getDescription());
            mcpServerEntity.setOwner(request.getOwner());
            // 创建McpServer
            McpServerEntity createdMcpServer = mcpServerService.createMcpServer(mcpServerEntity);
            if (createdMcpServer == null) {
                return McpServerResponse.fail("创建McpServer失败");
            }
            return McpServerResponse.success("创建McpServer成功", JSON.toJSONString(createdMcpServer));
        } catch (Exception e) {
            log.error("创建McpServer失败, request={}", request, e);
            return McpServerResponse.fail("创建McpServer失败: " + e.getMessage());
        }
    }

    /**
     * 更新McpServer
     *
     * @param request 请求参数
     * @return 更新结果
     */
    @PostMapping("/update")
    public McpServerResponse updateMcpServer(@RequestBody McpServerRequest request) {
        try {
            // 参数校验
            if (request == null) {
                return McpServerResponse.fail("请求参数不能为空");
            }

            if (request.getId() == null) {
                return McpServerResponse.fail("McpServer ID不能为空");
            }
            // 检查McpServer是否存在
            if (!mcpServerService.existsMcpServer(request.getId())) {
                return McpServerResponse.fail("McpServer不存在或已被删除");
            }
            // 创建实体
            McpServerEntity mcpServerEntity = new McpServerEntity();
            mcpServerEntity.setId(request.getId());
            mcpServerEntity.setDescription(request.getDescription());
            mcpServerEntity.setOwner(request.getOwner());
            // 更新McpServer
            McpServerEntity updatedMcpServer = mcpServerService.updateMcpServer(mcpServerEntity);
            if (updatedMcpServer == null) {
                return McpServerResponse.fail("更新McpServer失败");
            }
            return McpServerResponse.success("更新McpServer成功", JSON.toJSONString(updatedMcpServer));
        } catch (Exception e) {
            
            log.error("更新McpServer失败, request={}", request, e);
            return McpServerResponse.fail("更新McpServer失败: " + e.getMessage());
        }
    }

    /**
     * 删除McpServer
     *
     * @param id McpServer ID
     * @return 删除结果
     */
    @PostMapping("/delete/{id}")
    public McpServerResponse deleteMcpServer(@PathVariable Long id) {
        try {
            // 参数校验
            if (id == null) {
                return McpServerResponse.fail("McpServer ID不能为空");
            }
            // 删除McpServer
            boolean deleted = mcpServerService.deleteMcpServer(id);
            if (!deleted) {
                return McpServerResponse.fail("删除McpServer失败，可能不存在或已被删除");
            }
            return McpServerResponse.success("删除McpServer成功");
        } catch (Exception e) {
            log.error("删除McpServer失败, id={}", id, e);
            return McpServerResponse.fail("删除McpServer失败: " + e.getMessage());
        }
    }

    /**
     * 删除业务线下所有McpServer
     *
     * @param businessLineId 业务线ID
     * @return 删除结果
     */
    @PostMapping("/delete/business/{businessLineId}")
    public McpServerResponse deleteAllMcpServersByBusinessLineId(@PathVariable Long businessLineId) {
        try {
            // 参数校验
            if (businessLineId == null) {
                return McpServerResponse.fail("业务线ID不能为空");
            }
            // 删除业务线下所有McpServer
            int count = mcpServerService.deleteAllMcpServersByBusinessLineId(businessLineId);
            return McpServerResponse.success("删除业务线下所有McpServer成功，共删除" + count + "个McpServer");
        } catch (Exception e) {
            
            log.error("删除业务线下所有McpServer失败, businessLineId={}", businessLineId, e);
            return McpServerResponse.fail("删除业务线下所有McpServer失败: " + e.getMessage());
        }
    }

    /**
     * 批量获取McpServer
     *
     * @param request 请求参数
     * @return McpServer列表
     */
    @PostMapping("/batch/get")
    public McpServerResponse batchGetMcpServers(@RequestBody McpServerRequest request) {
        try {
            // 参数校验
            if (request == null) {
                return McpServerResponse.fail("请求参数不能为空");
            }

            if (request.getIds() == null || request.getIds().isEmpty()) {
                return McpServerResponse.fail("McpServer ID列表不能为空");
            }
            // 批量获取McpServer
            List<McpServerEntity> mcpServers = mcpServerService.batchGetMcpServers(request.getIds());
            return McpServerResponse.success("批量获取McpServer成功", JSON.toJSONString(mcpServers));
        } catch (Exception e) {
            
            log.error("批量获取McpServer失败, request={}", request, e);
            return McpServerResponse.fail("批量获取McpServer失败: " + e.getMessage());
        }
    }

    /**
     * 检查McpServer名称是否存在
     *
     * @param name McpServer名称
     * @return 检查结果
     */
    @GetMapping("/check/name/{name}")
    public McpServerResponse checkMcpServerNameExists(@PathVariable String name) {
        try {
            // 参数校验
            if (name == null || name.trim().isEmpty()) {
                return McpServerResponse.fail("McpServer名称不能为空");
            }
            // 检查名称是否存在
            boolean exists = mcpServerService.existsMcpServerByName(name);
            return McpServerResponse.success("检查McpServer名称是否存在成功", String.valueOf(exists));
        } catch (Exception e) {
            
            log.error("检查McpServer名称是否存在失败, name={}", name, e);
            return McpServerResponse.fail("检查McpServer名称是否存在失败: " + e.getMessage());
        }
    }

    @GetMapping("/getByOwner")
    public McpServerResponse getMcpServersByOwner(@RequestParam("businessLineId") Long businessLineId, @RequestParam("owner") String owner) {
        try {
            List<McpServerEntity> mcpServerEntities = mcpServerService.selectByServerIdAndOwner(businessLineId, owner);
            return McpServerResponse.success("获取McpServer列表成功", JSON.toJSONString(mcpServerEntities));
        } catch (Exception e) {
            
            log.error("获取McpServer列表失败", e);
            return McpServerResponse.fail("获取McpServer列表失败: " + e.getMessage());
        }
    }
}
