package com.sankuai.mcpserver.algoplat.mcpserver.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/17
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class XueChengRequest {

    @ToolParam(description = "用户mis号", required = true)
    private String misId;

    @ToolParam(description = "学城空间key,查空间一级子文档必传,其他情况可以不传递", required = false)
    private String spaceKey;

    @ToolParam(description = "学城空间id,查空间一级子文档必传,其他情况可以不传递", required = false)
    private String spaceId;

    @ToolParam(description = "学城父文档id,查文档内容必传,其他情况可以不传递", required = false)
    private Long contentId;

    @ToolParam(description = "学城文档标题列表", required = true)
    private List<String> contentTitlies;
}
