package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.CustomEvalModel;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.FridayModelTraningRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;

import java.io.IOException;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/18
 */

public interface FridayService {

    void executeModelTestProcess(ExecuteFridayMissionContext context, McpMissionInfo mcpMissionInfoByMission) throws Exception;

    String performModelRegistration(String modelName, String tranName, String modelClass, String iter, String trainingTaskId, String misId) throws Exception;

    String deployLLMServiceOnFriday(ExecuteFridayMissionContext executeFridayMissionContext, Integer maxSessionLen, String misId) throws Exception;

    String queryDeploymentStatusSfLargeModel(String appId, String misId) throws Exception;

    void executeModelDeploymentTask(ExecuteFridayMissionRequest fridayRequest, ExecuteFridayMissionContext executeFridayMissionContext, McpMissionInfo mcpMissionInfoByMission) throws Exception;

    ExecuteFridayMissionContext executeModelRegistrationProcess(ExecuteFridayMissionRequest fridayRequest, ExecuteFridayMissionContext executeFridayMissionContext, McpMissionInfo mcpMissionInfoByMission) throws Exception;

    String uploadTrainingDatasetToFriday(CustomEvalModel customEvalModel, ExecuteFridayMissionRequest request) throws Exception;

    void createTrainingTask(ExecuteFridayMissionContext executeFridayMissionContext, ExecuteFridayMissionRequest request, McpMissionInfo mcpMissionInfoByMission) throws Exception;

    String queryTrainingTaskStatus(String taskId, String misId) throws Exception;

    ExecuteFridayMissionContext uploadModelTraningMissionDataset(ExecuteFridayMissionRequest request, ExecuteFridayMissionContext executeFridayMissionContext, McpMissionInfo mcpMissionInfoByMission) throws Exception;

    String findClosestIterFromFriday(String tenantId, String taskId, String trainMethod, String misId) throws Exception;

    void updateFridayMissionInfo(ExecuteFridayMissionContext context, ExecuteFridayMissionRequest fridayRequest, McpMissionInfo fridayMissionInfo) throws Exception;
}
