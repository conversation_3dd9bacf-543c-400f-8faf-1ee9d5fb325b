package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.dianping.zebra.util.StringUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.AliveMcpRecord;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolInfo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/8/12
 */

@Service
@Slf4j
public class McpBeanRecordService {

    @Getter
    public Map<Long, AliveMcpRecord> aliveMcpRecords = new ConcurrentHashMap<>();

    public void addAliveMcpRecord(AliveMcpRecord aliveMcpRecord) {
        if (aliveMcpRecord == null) {
            return;
        }
        this.aliveMcpRecords.put(aliveMcpRecord.getMcpServerId(), aliveMcpRecord);
    }

    public void removeAliveMcpRecord(Long aliveMcpRecordId) {
        this.aliveMcpRecords.remove(aliveMcpRecordId);
    }


    public void updateAliveMcpRecord(Long mcpServerId,Long toolId) {
        if(mcpServerId == null || toolId == null) {
            return;
        }
        AliveMcpRecord aliveMcpRecord = aliveMcpRecords.get(mcpServerId);
        if(aliveMcpRecord == null) {
            return;
        }
        // 编辑工具的逻辑是先软删除原工具，再新增一个工具，因此这里直接判断是否包含id即可。
        if(!aliveMcpRecord.getAliveMcpToolIds().contains(toolId)) {
            aliveMcpRecord.addAliveMcpToolIds(toolId);
        } else {
            aliveMcpRecord.deleteAliveMcpToolIds(toolId);
        }
        aliveMcpRecords.put(mcpServerId, aliveMcpRecord);
    }
    public void updateAliveMcpRecordForDefaultTool(Long mcpServerId, String defaultToolName){
        if(mcpServerId == null || StringUtils.isBlank(defaultToolName)) {
            return;
        }
        AliveMcpRecord aliveMcpRecord = aliveMcpRecords.get(mcpServerId);
        if(aliveMcpRecord == null) {
            return;
        }
        if(!aliveMcpRecord.getAliveDefaultMcpToolNames().contains(defaultToolName)) {
            aliveMcpRecord.addAliveDefaultMcpToolName(defaultToolName);
        } else {
            aliveMcpRecord.deleteAliveDefaultMcpToolName(defaultToolName);
        }
        aliveMcpRecords.put(mcpServerId, aliveMcpRecord);
    }

}
