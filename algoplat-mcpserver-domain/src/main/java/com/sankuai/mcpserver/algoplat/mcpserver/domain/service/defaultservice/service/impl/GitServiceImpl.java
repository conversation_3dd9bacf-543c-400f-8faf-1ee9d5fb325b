package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.inf.auth.api.exception.AuthSecurityException;
import com.sankuai.inf.auth.api.model.AuthToken;
import com.sankuai.inf.auth.api.model.SignParam;
import com.sankuai.inf.sts.api.model.STSRequest;
import com.sankuai.inf.sts.api.service.ISTSService;
import com.sankuai.inf.sts.api.service.STSServiceFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.GitService;
import com.sankuai.oceanus.http.client.apache.OceanusHttpProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class GitServiceImpl implements GitService {

    private final RestTemplate restTemplate = new RestTemplate();
    private static final String BASE_URL = "https://dev-api.ee.test.sankuai.com/mcode/rest/api/2.0/projects/%s/repos/%s/%s";
    private static final String REMOTE_APPKEY = "com.sankuai.devtools.gateway.service";
    private static final String STS_TOKEN = "STS-TOKEN";
    private static final String SSO_ID= "f32a546874"; // SSO ClientID

    private final ISTSService stsService;
    private final OceanusHttpProcessor oceanusHttpProcessor;

    public GitServiceImpl() {
        // 初始化STS服务
        STSRequest stsRequest = STSRequest.Builder.newSTSRequest()
                .withAppKey(REMOTE_APPKEY)
                .withKeyName("SSOTicket")
                .signAction()
                .build();
        this.stsService = STSServiceFactory.create(stsRequest);
        this.oceanusHttpProcessor = new OceanusHttpProcessor();
    }

    @Override
    public boolean isBranchOrTagExist(String project, String repo, String name, String type, String misId) {
        log.info("isBranchOrTagExist request - project:{}, repo:{}, name:{}, type:{}, misId:{}",
                project, repo, name, type, misId);

        if (!isValidType(type)) {
            throw new IllegalArgumentException("type must be either 'branch' or 'tag'");
        }

        try {


            // 1. 获取STS Token
            String stsToken = getSSOToken(misId);

            // 2. 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("oceanus-remote-appkey", REMOTE_APPKEY);
            headers.set(STS_TOKEN, stsToken);

            // 3. 发送请求
            String endpoint = "branch".equalsIgnoreCase(type) ? "branches" : "tags";
            String apiUrl = String.format(BASE_URL, project, repo, endpoint);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(
                    apiUrl,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            // 4. 处理响应
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JSONObject jsonResponse = JSONObject.parseObject(response.getBody());
                JSONArray values = jsonResponse.getJSONArray("values");

                if (values != null) {
                    for (int i = 0; i < values.size(); i++) {
                        JSONObject item = values.getJSONObject(i);
                        String itemName = item.getString("id");
                        if (name.equals(itemName)) {
                            return true;
                        }
                    }
                }
            }

            return false;

        } catch (Exception e) {
            log.error("Failed to check branch/tag existence", e);
            throw new RuntimeException("Failed to check branch/tag existence", e);
        }
    }

    private String getSSOToken(String mis) throws AuthSecurityException {
        if (StringUtils.isEmpty(mis)) {
            throw new IllegalStateException("Missing");
        }

        SignParam signParam = SignParam.Builder.newSignParam()
                .withClientId(mis)
                .withExtension("ssoid", SSO_ID)
                .build();

        AuthToken token = stsService.sign(signParam);
        return token.getAt();
    }

    private boolean isValidType(String type) {
        return "branch".equalsIgnoreCase(type) || "tag".equalsIgnoreCase(type);
    }
}