package com.sankuai.mcpserver.algoplat.mcpserver.domain.controller;


import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.ToolManagerService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.web.bind.annotation.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/mcp/api/tools")
public class ToolManagerController {

    @Resource
    private ToolManagerService toolManagerService;

    @Resource
    private McpServerService mcpServerService;

    @GetMapping("/toolList")
    public McpServerResponse getAllToolCallbacks(@RequestParam(name = "mcpServerId") Long mcpServerId) {
        if (mcpServerId == null) {
            return McpServerResponse.fail("参数为空");
        }
        log.info("根据businessLineId和mcpServerId获取工具,request:{}", mcpServerId);
        List<ToolInfo> allToolsInServer = toolManagerService.getAllToolsInServer(mcpServerId);
        return McpServerResponse.success("成功", new Gson().toJson(allToolsInServer));
    }

    /**
     * 根据类型获取已注册的工具
     */
    @GetMapping("/getListByToolType")
    public McpServerResponse listTools(@RequestParam(name = "mcpServerId") Long mcpServerId, @RequestParam(name = "toolType") ToolTypeEnum toolType) {
        if (toolType == null) {
            return McpServerResponse.fail("参数为空");
        }
        try {
            log.info("根据类型获取已注册的工具:request:{}", toolType);
            McpServerResponse mcpServerResponse = toolManagerService.listToolsByType(mcpServerId, toolType);
            return mcpServerResponse;
        } catch (Exception e) {
            log.error("获取工具失败", e);
            return McpServerResponse.fail("根据类型获取已注册的工具失败" + e.getMessage());
        }
    }

    /**
     * 根据名字获取工具
     */
    @GetMapping("/getToolByName")
    public McpServerResponse getToolInfo(@RequestParam(name = "mcpServerId") Long mcpServerId, @RequestParam(name = "toolName") String toolName) {
        if (mcpServerId == null || toolName == null) {
            return McpServerResponse.fail("参数为空");
        }
        McpServerResponse toolInfo;
        try {
            log.info("根据toolName获取工具,request:{}", toolName);
            toolInfo = toolManagerService.getToolInfo(mcpServerId, toolName);
            if (toolInfo == null || toolInfo.getCode() != 0) {
                return McpServerResponse.fail("未找到该工具");
            }
            log.info("获取到工具,response:{}", toolInfo.getData());
            return toolInfo;
        } catch (Exception e) {
            log.error("获取工具失败", e);
            return McpServerResponse.fail("获取工具失败" + e.getMessage());
        }
    }

    @GetMapping("/getByOwnerName")
    public McpServerResponse getToolInfoByOwner(@RequestParam(name = "mcpServerId") Long mcpServerId, @RequestParam(name = "owner") String owner) {
        log.info("根据owner获取工具,request:{}", owner);
        if (mcpServerId == null || owner == null) {
            return McpServerResponse.fail("参数为空");
        }
        try {
            List<ToolInfo> allToolsInServer = toolManagerService.getAllToolsInServer(mcpServerId);
            if (allToolsInServer == null) {
                return McpServerResponse.fail("未找到该工具");
            }
            List<ToolInfo> result = new ArrayList<>();
            for (ToolInfo toolInfo : allToolsInServer) {
                if (toolInfo.getOwner().equals(owner) || StringUtils.equals(toolInfo.getOwner(), "system")) {
                    result.add(toolInfo);
                }
            }
            return McpServerResponse.success("成功", new Gson().toJson(result));
        } catch (Exception e) {
            log.error("获取工具失败", e);
            return McpServerResponse.fail("获取工具失败" + e.getMessage());
        }
    }

    /**
     * 注册一个新的工具
     */
    @PostMapping("/create")
    public McpServerResponse registerTool(@RequestBody Map<String, Object> toolInfoMap) {
        if (MapUtils.isEmpty(toolInfoMap) || toolInfoMap.get("mcpServerId") == null) {
            McpServerResponse.fail("参数为空");
        }
        try {
            log.info("注册工具,request:{}", JSONObject.toJSONString(toolInfoMap));
            // 注册工具
            Long toolId = toolManagerService.registerTool(Long.valueOf(toolInfoMap.get("mcpServerId").toString()), toolInfoMap);
            log.info("注册工具,response:{}", toolId.toString());
            return McpServerResponse.success("成功", toolId.toString());
        } catch (Exception e) {
            log.error("注册工具失败", e);
            return McpServerResponse.fail("注册工具失败" + e.getMessage());
        }
    }

    @PostMapping("/updateTool")
    public McpServerResponse updateTool(@RequestParam(name = "mcpServerId") Long mcpServerId, @RequestBody Map<String, Object> toolInfoMap) {
        if (MapUtils.isEmpty(toolInfoMap) || mcpServerId == null) {
            return McpServerResponse.fail("参数为空");
        }
        try {
            if ("SystemDefault".equals(toolInfoMap.get("type")) || "system".equals(toolInfoMap.get("owner"))) {
                return McpServerResponse.fail("系统定义的工具不允许修改");
            }
            log.info("更新工具,request:{}", JSONObject.toJSONString(toolInfoMap));
            Long newToolId = toolManagerService.updateTool(mcpServerId, toolInfoMap);
            if (newToolId == null) {
                return McpServerResponse.fail("更新工具失败");
            }
            log.info("更新工具,response:{}", newToolId);
            return McpServerResponse.success("成功", newToolId.toString());
        } catch (Exception e) {
            log.error("更新工具失败", e);
            return McpServerResponse.fail("更新工具失败" + e.getMessage());
        }
    }

    /**
     * 删除工具
     */
    @GetMapping("/delete")
    public McpServerResponse unregisterTool(@RequestParam(name = "mcpServerId") Long mcpServerId, @RequestParam(name = "toolName") String toolName, @RequestParam(name = "toolType") ToolTypeEnum toolType, @RequestParam(name = "owner") String owner) {
        if (mcpServerId == null || StringUtils.isBlank(toolName) || toolType == null) {
            return McpServerResponse.fail("参数异常");
        }
        if (ToolTypeEnum.ToolAnnotation.getValue().equals(toolType.getValue())) {
            return McpServerResponse.fail("系统定义的工具不允许删除");
        }
        try {
            log.info("取消注册工具,request:{}", toolName + ":" + toolType);
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("mcpServerId", mcpServerId);
            requestMap.put("name", toolName);
            requestMap.put("type", toolType);
            toolManagerService.deleteToolWithService(requestMap);
            log.info("取消注册工具成功,mcpServerId:{},toolName:{}", mcpServerId, toolName);
            return McpServerResponse.success();
        } catch (Exception e) {
            log.error("取消注册工具失败", e);
            return McpServerResponse.fail("取消注册工具失败" + e.getMessage());
        }
    }


    /**
     * 大模型生成RPC接口的描述信息
     */
    @PostMapping("/llmRequest")
    public McpServerResponse llmRequest(@RequestBody Map<String, Object> toolInfoMap) {
        if (toolInfoMap == null) {
            return McpServerResponse.fail("参数无效");
        }
        if (!toolInfoMap.containsKey("interfaceName") || !toolInfoMap.containsKey("toolParams")) {
            return McpServerResponse.fail("请提供更多的信息！");
        }
        try {
            String llmModelContext = toolManagerService.getLLMModelContext(toolInfoMap);
            if (StringUtils.isEmpty(llmModelContext)) {
                log.info("生成描述失败", toolInfoMap);
                return McpServerResponse.fail("生成描述失败");
            }
            return McpServerResponse.success("成功", llmModelContext);
        } catch (Exception e) {
            log.error("获取LLM模型上下文失败,request:{}", toolInfoMap, e);
            return McpServerResponse.fail("生成描述失败" + e.getMessage());
        }
    }

    @GetMapping("getAllDefaultTools")
    public McpServerResponse getAllDefaultTools(@RequestParam("mcpServerId") Long mcpServerId) {
        try {
            log.info("获取McpServer使用的系统默认工具,request:{}", mcpServerId);
            List<ToolInfo> toolInfos = toolManagerService.processUsedDefaultTool(mcpServerId);
            log.info("获取McpServer使用的系统默认工具,response:{}", new Gson().toJson(toolInfos));
            return McpServerResponse.success("成功", JSONObject.toJSONString(toolInfos));
        } catch (Exception e) {
            log.error("获取McpServer使用的系统默认工具失败", e);

            return McpServerResponse.fail("获取McpServer使用的系统默认工具失败");
        }
    }

    @PostMapping("/saveDefaultToolForMcpServer")
    public McpServerResponse saveDefaultToolForMcpServer(@RequestParam("mcpServerId") Long mcpServerId, @RequestParam("useDefaultToolNameList") List<String> useDefaultToolNameList) {
        try {
            McpServerEntity mcpServerEntity = mcpServerService.saveDefaultToolForMcpServer(mcpServerId, useDefaultToolNameList);
            if (mcpServerEntity == null) {
                return McpServerResponse.fail("保存默认工具失败");
            }
            return McpServerResponse.success("成功", new Gson().toJson(mcpServerEntity));
        } catch (Exception e) {
            log.error("给McpServer保存基础工具失败", e);
            return McpServerResponse.fail("保存默认工具失败");
        }
    }

    @GetMapping("/getUsedSystemDefaultToolInMcpServer")
    public McpServerResponse getUsedSystemDefaultToolInMcpServer(@RequestParam("mcpServerId") Long mcpServerId) {
        try {
            log.info("获取McpServer使用的系统默认工具,request:{}", mcpServerId);
            List<ToolInfo> toolInfos = toolManagerService.processUsedDefaultTool(mcpServerId);
            log.info("获取McpServer使用的系统默认工具,response:{}", new Gson().toJson(toolInfos));
            return McpServerResponse.success("成功", JSONObject.toJSONString(toolInfos));
        } catch (Exception e) {
            log.error("获取McpServer使用的系统默认工具失败", e);

            return McpServerResponse.fail("获取McpServer使用的系统默认工具失败");
        }
    }

}