package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.impl;

import com.dianping.cat.Cat;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.mcpserver.CustomMcpServerConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DefaultToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.McpBeanRecordService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.ToolManagerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.BeanNameManager;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.ToolAnnotationCollector;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.BusinessLineDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpServerDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.StatusEnum;
import io.modelcontextprotocol.server.McpServerFeatures;
import io.modelcontextprotocol.server.McpSyncServer;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.mcp.McpToolUtils;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * McpServer服务实现类
 *
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Slf4j
@Service
public class McpServerServiceImpl implements McpServerService, ApplicationContextAware {

    @Resource
    private McpServerDao mcpServerDao;

    @Resource
    private CustomMcpServerConfig customMcpServerConfig;

    @Resource
    private BusinessLineDao businessLineDao;

    @Autowired
    private ToolManagerService toolManagerService;

    @Resource
    private ToolAnnotationCollector toolAnnotationCollector;

    protected org.springframework.context.ApplicationContext applicationContext;

    @Resource
    private McpBeanRecordService mcpBeanRecordService;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    @Override
    public List<McpServerEntity> getAllMcpServers() {
        log.info("获取所有McpServer");
        List<McpServerEntity> mcpServers = mcpServerDao.selectAllMcpServer();
        for (McpServerEntity mcpServer : mcpServers) {
            List<ToolInfo> allToolsInServer = toolManagerService.getAllToolsInServer(mcpServer.getId());
            mcpServer.setToolInfoList(allToolsInServer);
        }
        return mcpServers;
    }

    @Override
    public McpServerEntity getMcpServerById(Long id) {
        log.info("根据ID获取McpServer, id={}", id);
        if (id == null) {
            log.warn("McpServer ID不能为空");
            return null;
        }
        // 直接从数据库获取McpServer，不加载工具信息
        McpServerEntity mcpServer = mcpServerDao.selectMcpServerById(id);
        if (mcpServer != null) {
            log.info("获取McpServer成功, id={}", id);
        } else {
            log.warn("未找到McpServer, id={}", id);
        }

        return mcpServer;
    }

    /**
     * 获取包含工具信息的McpServer
     * 这个方法用于前端API调用，需要返回完整的McpServer信息（包括工具列表）
     */
    public McpServerEntity getMcpServerWithTools(Long id) {
        McpServerEntity mcpServer = getMcpServerById(id);
        if (mcpServer != null) {
            List<ToolInfo> allToolsInServer = toolManagerService.getAllToolsInServer(id);
            mcpServer.setToolInfoList(allToolsInServer);
        }
        return mcpServer;
    }

    @Override
    public List<McpServerEntity> getMcpServersByBusinessLineId(Long businessLineId) {
        log.info("根据业务线ID获取McpServer列表, businessLineId={}", businessLineId);
        if (businessLineId == null) {
            log.warn("业务线ID不能为空");
            return new ArrayList<>();
        }
        List<McpServerEntity> mcpServers = mcpServerDao.selectMcpServerByBusinessLineId(businessLineId, StatusEnum.ACTIVE);
        log.info("获取业务线下McpServer列表完成，共{}个, businessLineId={}", mcpServers.size(), businessLineId);
        return putToolInfoForMcpServer(mcpServers);
    }

    @Override
    public List<McpServerEntity> getMcpServersByName(String name) {
        log.info("根据名称获取McpServer列表, name={}", name);
        if (name == null || name.trim().isEmpty()) {
            log.warn("McpServer名称不能为空");
            return new ArrayList<>();
        }

        List<McpServerEntity> mcpServers = mcpServerDao.selectMcpServerByName(name);
        log.info("获取指定名称的McpServer列表完成，共{}个, name={}", mcpServers.size(), name);
        return putToolInfoForMcpServer(mcpServers);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public McpServerEntity createMcpServer(McpServerEntity mcpServerEntity) {
        log.info("创建McpServer, mcpServerEntity={}", mcpServerEntity);
        if (mcpServerEntity == null) {
            log.warn("McpServer实体不能为空");
            return null;
        }
        // 检查必填字段
        if (mcpServerEntity.getMcpServerName() == null || mcpServerEntity.getMcpServerName().trim().isEmpty()) {
            log.warn("McpServer名称不能为空");
            return null;
        }
        // 检查名称是否已存在
        if (mcpServerDao.existsActiveMcpServerByName(mcpServerEntity.getMcpServerName())) {
            log.warn("McpServer名称已存在, name={}", mcpServerEntity.getMcpServerName());
            return null;
        }

        McpServerEntity createdMcpServer = mcpServerDao.createMcpServer(mcpServerEntity);
        if (createdMcpServer != null) {
            log.info("创建McpServer成功, id={}, name={}", createdMcpServer.getId(), createdMcpServer.getMcpServerName());
        } else {
            log.error("创建McpServer失败");
        }
        BusinessLine businessLineById = businessLineDao.getBusinessLineById(mcpServerEntity.getBusinessLineId());
        if (businessLineById == null) {
            log.error("业务线不存在, id={}", mcpServerEntity.getBusinessLineId());
            throw new RuntimeException("未能创建Server");
        }
        customMcpServerConfig.createMcpSyncServer(businessLineById, mcpServerEntity.getMcpServerName(),createdMcpServer.getId());
        return createdMcpServer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public McpServerEntity updateMcpServer(McpServerEntity mcpServerEntity) {
        log.info("更新McpServer, mcpServerEntity={}", mcpServerEntity);
        if (mcpServerEntity == null || mcpServerEntity.getId() == null) {
            log.warn("McpServer ID不能为空");
            return null;
        }
        McpServerEntity updatedMcpServer = mcpServerDao.updateMcpServer(mcpServerEntity);
        if (updatedMcpServer != null) {
            List<ToolInfo> allToolsInServer = toolManagerService.getAllToolsInServer(updatedMcpServer.getId());
            updatedMcpServer.setToolInfoList(allToolsInServer);
            log.info("更新McpServer成功, id={}", updatedMcpServer.getId());
        } else {
            log.error("更新McpServer失败, id={}", mcpServerEntity.getId());
        }

        return updatedMcpServer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMcpServer(Long id) {
        log.info("删除McpServer, id={}", id);
        if (id == null) {
            log.warn("McpServer ID不能为空");
            return false;
        }
        McpServerEntity mcpServerById = getMcpServerById(id);
        if (mcpServerById == null) {
            log.error("未找到McpServer, id={}", id);
            return false;
        }
        BusinessLine businessLineById = businessLineDao.getBusinessLineById(mcpServerById.getBusinessLineId());
        if (businessLineById == null) {
            log.error("业务线不存在, id={}", mcpServerById.getBusinessLineId());
            return false;
        }
        customMcpServerConfig.destroyMcpSyncServer(businessLineById.getBusinessLine(), mcpServerById.getMcpServerName(),id);
        toolManagerService.deleteToolByMcpServerEntity(mcpServerById);
        Integer result = mcpServerDao.deleteMcpServer(id);
        boolean success = result != null && result > 0;
        if (success) {
            log.info("删除McpServer成功, id={}", id);
        } else {
            log.error("删除McpServer失败, id={}", id);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAllMcpServersByBusinessLineId(Long businessLineId) {
        log.info("删除业务线下所有McpServer, businessLineId={}", businessLineId);
        if (businessLineId == null) {
            log.warn("业务线ID不能为空");
            return 0;
        }
        List<McpServerEntity> mcpServersByBusinessLineId = getMcpServersByBusinessLineId(businessLineId);
        if (mcpServersByBusinessLineId == null) {
            log.warn("业务线下没有McpServer, businessLineId={}", businessLineId);
            return 0;
        }
        int count = 0;
        BusinessLine businessLineById = businessLineDao.getBusinessLineById(businessLineId);
        if (businessLineById == null) {
            log.error("业务线不存在, id={}", businessLineId);
            throw new RuntimeException("删除McpServer失败");
        }
        for (McpServerEntity mcpServerEntity : mcpServersByBusinessLineId) {
            deleteMcpServer(mcpServerEntity.getId());
            count++;
        }
        log.info("删除业务线下所有McpServer完成，共删除{}个, businessLineId={}", count, businessLineId);
        return count;
    }

    @Override
    public List<McpServerEntity> batchGetMcpServers(List<Long> ids) {
        log.info("批量获取McpServer, ids={}", ids);
        if (ids == null || ids.isEmpty()) {
            log.warn("McpServer ID列表不能为空");
            return new ArrayList<>();
        }
        List<McpServerEntity> mcpServers = mcpServerDao.batchSelectMcpServerByIds(ids);
        log.info("批量获取McpServer完成，共{}个", mcpServers.size());
        return putToolInfoForMcpServer(mcpServers);
    }

    @Override
    public boolean existsMcpServer(Long id) {
        log.info("检查McpServer是否存在, id={}", id);
        if (id == null) {
            log.warn("McpServer ID不能为空");
            return false;
        }
        boolean exists = mcpServerDao.existsActiveMcpServer(id);
        log.info("检查McpServer是否存在结果: {}, id={}", exists, id);
        return exists;
    }

    @Override
    public boolean existsMcpServerByName(String name) {
        log.info("检查指定名称的McpServer是否存在, name={}", name);
        if (name == null || name.trim().isEmpty()) {
            log.warn("McpServer名称不能为空");
            return false;
        }
        boolean exists = mcpServerDao.existsActiveMcpServerByName(name);
        log.info("检查指定名称的McpServer是否存在结果: {}, name={}", exists, name);
        return exists;
    }

    @Override
    public List<McpServerEntity> selectByServerIdAndOwner(Long businessLineId, String owner) {
        if (businessLineId == null || owner == null) {
            return new ArrayList<>();
        }
        List<McpServerEntity> mcpServerEntities = mcpServerDao.selectByBussIdServerIdAndOwner(businessLineId, owner);
        return putToolInfoForMcpServer(mcpServerEntities);

    }

    private List<McpServerEntity> putToolInfoForMcpServer(List<McpServerEntity> mcpServerEntities) {
        // 为每个McpServer加载关联的工具信息
        if (!CollectionUtils.isEmpty(mcpServerEntities)) {
            for (McpServerEntity mcpServer : mcpServerEntities) {
                List<ToolInfo> allToolsInServer = toolManagerService.getAllToolsInServer(mcpServer.getId());
                mcpServer.setToolInfoList(allToolsInServer);
            }
        }
        return mcpServerEntities;
    }

    @Override
    public McpServerEntity saveDefaultToolForMcpServer(Long mcpServerId, List<String> useDefaultToolList) throws Exception {
        if (mcpServerId == null || useDefaultToolList == null) {
            return null;
        }
        McpServerEntity mcpServerEntity = mcpServerDao.selectMcpServerById(mcpServerId);
        if (mcpServerEntity == null) {
            log.info("can not find mcpServer:{}", mcpServerId);
            return null;
        }

        List<FunctionCallback> defaultToolFunctionCallbacks = toolAnnotationCollector.getAtToolFunctionCallbacks(DefaultToolService.class);
        List<String> systemTools = new ArrayList<>();
        for (FunctionCallback defaultToolFunctionCallback : defaultToolFunctionCallbacks) {
            systemTools.add(defaultToolFunctionCallback.getName());
        }
        systemTools = systemTools.stream().distinct().collect(Collectors.toList());

        boolean result = useDefaultToolList.stream().allMatch(systemTools::contains);
        if (!result) {
            log.info("useDefaultToolList:{} contains not systemTools:{}", useDefaultToolList, systemTools);
            return null;
        }

        // 合并现有的默认工具列表和新的默认工具列表
        List<String> existingDefaultTools = mcpServerEntity.getDefaultToolNameList();
        if (existingDefaultTools == null) {
            existingDefaultTools = new ArrayList<>();
        }

        // 创建新的合并列表，避免重复
        List<String> mergedDefaultTools = new ArrayList<>(existingDefaultTools);
        for (String newTool : useDefaultToolList) {
            if (!mergedDefaultTools.contains(newTool)) {
                mergedDefaultTools.add(newTool);
            }
        }
        mcpServerEntity.setDefaultToolNameList(mergedDefaultTools);
        McpServerEntity newMcpServerEntity = mcpServerDao.updateMcpServer(mcpServerEntity);
        Long businessLineId = newMcpServerEntity.getBusinessLineId();
        BusinessLine businessLineById = businessLineDao.getBusinessLineById(businessLineId);
        if (businessLineById == null) {
            log.error("业务线不存在, id={}", businessLineId);
            throw new RuntimeException("业务线不存在");
        }
        List<String> toolNames = toolManagerService.getUsedToolsNameInServer(mcpServerId);
        List<String> defaultToolNameList = newMcpServerEntity.getDefaultToolNameList();
        if (!CollectionUtils.isEmpty(defaultToolNameList)) {
            for (FunctionCallback defaultToolFunctionCallback : defaultToolFunctionCallbacks) {
                String name = defaultToolFunctionCallback.getName();
                if (defaultToolNameList.contains(name) && !toolNames.contains(name)) {
                    registerToolForMcpServer((ToolCallback) defaultToolFunctionCallback, businessLineById, newMcpServerEntity);
                    mcpBeanRecordService.updateAliveMcpRecordForDefaultTool(mcpServerId, name);
                }
            }
        }
        return newMcpServerEntity;
    }

    @Override
    public void deleteSystemDefaultTool(Long mcpServerId, String toolName) {
        if (mcpServerId == null || toolName == null) {
            return;
        }
        McpServerEntity mcpServerEntity = mcpServerDao.selectMcpServerById(mcpServerId);
        List<String> defaultToolNameList = mcpServerEntity.getDefaultToolNameList();
        if (defaultToolNameList == null) {
            return;
        }
        defaultToolNameList.remove(toolName);
        mcpServerEntity.setDefaultToolNameList(defaultToolNameList);
        mcpServerDao.updateMcpServer(mcpServerEntity);
    }

    public void registerToolForMcpServer(ToolCallback toolCallback, BusinessLine bussiness, McpServerEntity mcpServer) {
        try {
            String mcpServerBeanName = BeanNameManager.genMcpServerBeanName(bussiness.getBusinessLine(), mcpServer.getMcpServerName());
            McpServerEntity mcpServerById = getMcpServerById(mcpServer.getId());
            if (mcpServerById == null) {
                log.error("Can not find mcpServer named :{},id:{}", bussiness.getBusinessLine() + "_" + mcpServer.getMcpServerName(), mcpServer.getId());
                return;
            }
            McpServerFeatures.SyncToolSpecification syncToolSpecification = McpToolUtils.toSyncToolSpecification(toolCallback);
            McpSyncServer server = applicationContext.getBean(mcpServerBeanName, McpSyncServer.class);
            server.addTool(syncToolSpecification);
            log.info("Register tool {} for mcpServer {} success", toolCallback.getName(), mcpServerBeanName);
        } catch (Exception e) {
            log.error("Failed to register tool for mcpserver", toolCallback.getName(), e.getMessage(), e);
            throw e;
        }
    }

}
