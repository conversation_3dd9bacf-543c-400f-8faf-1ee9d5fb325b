package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.algoplatform.llmpredict.predict.api.dto.FridayHttpRequest;
import com.sankuai.algoplatform.llmpredict.predict.api.dto.LLMResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.constant.ServiceConstants;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.LLMService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.HttpUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.LionKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * LLM预测服务实现类
 *
 * @program: algoplat-mcpserver
 * @Author: CatPaw
 * @Date: 2023/4/21
 */
@Service
@Slf4j
public class LLMServiceImpl implements LLMService {

    /**
     * LLM预测服务客户端
     */
    @MdpPigeonClient(timeout = 60000L)
    private com.sankuai.algoplatform.llmpredict.predict.api.service.LLMService llmService;

    @Override
    public Map<String, String> singleCallLargeModel(String input, String modelName, Map<String, String> param, String chatOrBase, String missionId) {
        try {
            // 调用外部RPC接口
            FridayHttpRequest request = new FridayHttpRequest();
            request.setInvocationMethod(chatOrBase);
            request.setModel(modelName);
            request.setContext(input);
            request.setMaxTokens(Integer.parseInt(param.get("max_new_tokens")));
            request.setTopP(Double.parseDouble(param.get("top_p")));
            request.setTopK(Integer.parseInt(param.get("top_k")));
            request.setTemperature(Double.parseDouble(param.get("temperature")));
            request.setRole("user");
            Map<String, String> responseMap = llmService.singleCallLargeModel(request);
            if (responseMap == null) {
                return new HashMap<>();
            }
            String usage = JacksonUtil.toJsonStr(responseMap.get("usage"));
            String res = responseMap.get("content");
            Map<String, String> result = new HashMap<>();
            result.put("usage", usage);
            result.put("res", res);
            log.info("missionId:{},调用LLM预测服务成功,原始返回:{},返回结果:{}", missionId,JSONObject.toJSONString(responseMap), result);
            return result;
        } catch (Exception e) {
            log.error("missionId:{},调用LLM批量预测服务失败", missionId, e);
            return new HashMap<>();
        }
    }
}
