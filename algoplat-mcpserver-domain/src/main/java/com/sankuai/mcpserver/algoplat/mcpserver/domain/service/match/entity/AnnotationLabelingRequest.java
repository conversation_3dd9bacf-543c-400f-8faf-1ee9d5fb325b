package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/11
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class AnnotationLabelingRequest {
    /**
     * 业务线
     * 1001-境内住宿 1002-境外住宿 1006-到餐 1007-到综 (服务零售)
     */
    private Integer businessLine;
    /**
     * 标注类型
     * 1-单侧信息标注 2-单层匹配标注 3-多层匹配标注 4-大模型推理评价标注 5-图像标注  6-要素抽取  7-类型识别
     */
    private Integer anotationType;
    /**
     * 场景名称
     * 正餐/轻快餐 美发/球类运动/美甲 境内直连/境外直连
     */
    private String sceneName;
    /**
     * 匹配类型
     * 1-精准 2-近似 3-同品
     */
    private Integer matchType;
    /**
     * 拓展信息
     * json格式 "{\"dataSetName\":\"xxx\"}"
     */
    private String extensionInfo;
    /**
     * 分页数据
     * 最大1000
     */
    private Integer pageSize = 1000;
    /**
     * 当前分页
     */
    private Integer pageNum = 1;

}
