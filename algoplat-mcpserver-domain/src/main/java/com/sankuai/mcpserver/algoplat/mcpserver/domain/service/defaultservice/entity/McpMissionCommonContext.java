package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class McpMissionCommonContext extends McpMissionContext {

    private List<SubMissionState> subMissionStates;

    private String sessionId;

    private String originRequest;

    private String misId;

    private Map<String, String> extendInfo = new HashMap<>();

    private String parentSessionId;

    private String taskAgentSessionId;

}
