package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.meituan.talos.commons.domain.Column;
import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.data.talos.AsyncQueryResult;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.QueryRequest;
import com.sankuai.data.talos.model.Engine;
import com.sankuai.data.talos.model.QueryInfo;
import com.sankuai.data.talos.model.QueryResult;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.TalosRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.TalosQueryResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.TalosQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Talos数据查询服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TalosQueryServiceImpl implements TalosQueryService {

    @Autowired
    private AsyncTalosClient talosClient;

    private static final int MAX_ROWS = 10000;

    @Override
    public TalosQueryResponse<List<List<Object>>> executeQuery(TalosRequest talosRequest) {
        try {
            // 打开会话
            talosClient.openSession();

            // 提交查询
            String qid = submitQuery(talosRequest.getSql());

            // 等待查询完成
            waitForQueryFinished(qid);

            // 获取查询结果
            QueryResult queryResult = talosClient.getQueryResult(qid);

            // 获取列名
            List<String> columnNames = queryResult.getColumns().stream().map(Column::getName).collect(Collectors.toList());
            log.info("查询结果列名获取成功，QID: {}, 列数: {}", qid, columnNames.size());

            // 批量获取结果数据
            int currCnt = 0;
            if(talosRequest.getMaxCnt() == null || talosRequest.getMaxCnt() > MAX_ROWS){
                currCnt = MAX_ROWS;
            } else {
                currCnt = talosRequest.getMaxCnt();
            }
            List<List<Object>> rows = queryResult.fetchMany(currCnt);
            log.info("查询结果获取成功，QID: {}, 结果行数: {}", qid, rows.size());

            // 返回带有列名的响应对象
            return TalosQueryResponse.success(columnNames,rows);

        } catch (Exception e) {
            log.error("执行查询失败: {}", e.getMessage(), e);
            return TalosQueryResponse.fail("500", "执行查询失败: " + e.getMessage());
        } finally {
            try {
                talosClient.closeSession();
            } catch (Exception e) {
                log.error("关闭会话失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 提交查询
     *
     * @param sql SQL语句
     * @return 查询ID
     */
    private String submitQuery(String sql)throws Exception  {
        try {
            // 构建查询请求
            QueryRequest queryRequest = new QueryRequest.Builder()
                    .engine(Engine.Onesql)
                    .statement(sql)
                    .build();

            // 异步提交查询
            AsyncQueryResult asyncResult = talosClient.submitAsync(queryRequest);
            // 等待获取 qid
            String qid = asyncResult.getQid();
            log.info("异步查询已提交，QID: {}", qid);

            return qid;
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            throw new Exception("系统异常: " + e.getMessage(), e);
        }
    }
    /**
     * 等待查询完成
     *
     * @param qid 查询ID
     */
    private void waitForQueryFinished(String qid) throws Exception {
        try {
            log.info("等待查询完成，QID: {}", qid);
            // 等待查询完成
            int result = talosClient.waitForFinished(qid);
            if (result == 1) {
                log.info("查询成功完成，QID: {}", qid);
            } else if (result == -1) {
                // 查询失败，获取失败原因
                QueryInfo queryInfo = talosClient.getQueryInfo(qid);
                String errorMessage = queryInfo.getMessage();
                log.error("查询失败，QID: {}, 错误信息: {}", qid, errorMessage);
                throw new Exception("查询执行失败: " + errorMessage);
            } else {
                // 查询超时
                log.error("查询超时，QID: {}", qid);
                throw new Exception("查询执行超时");
            }
        } catch (TalosException e) {
            log.error("等待查询完成失败: {}", e.getMessage(), e);
            throw new Exception("等待查询完成失败: " + e.getMessage(), e);
        }
    }
}