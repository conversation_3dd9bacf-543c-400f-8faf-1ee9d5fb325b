package com.sankuai.mcpserver.algoplat.mcpserver.domain.controller;

import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.TalosRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.TalosQueryRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.TalosQueryResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.TalosQueryService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Talos查询控制器
 * 用于测试Talos查询服务
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/talos")
public class TalosQueryController {

    @Autowired
    private TalosQueryService talosQueryService;

    @PostMapping("/query")
    public String query(@RequestBody TalosRequest talosRequest) {
        TalosQueryResponse<List<List<Object>>> response = talosQueryService.executeQuery(talosRequest);
        return JacksonUtil.toJsonStrWithEmptyDefault(response);
    }
}