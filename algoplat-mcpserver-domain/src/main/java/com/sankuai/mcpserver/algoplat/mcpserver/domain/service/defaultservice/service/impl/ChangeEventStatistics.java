package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

/**
 * MCP同步过程统计信息
 *
 * <AUTHOR>
 * @date 2025/8/12
 */
@Data
@Slf4j
public class ChangeEventStatistics {

    // McpServer相关统计
    private int totalMcpServersInDb;
    private int totalMcpServersInMemory;
    private int mcpServersToCreate;
    private int mcpServersToDelete;
    private int mcpServersToCompare;
    private int mcpServersCreatedSuccessfully;
    private int mcpServersDeletedSuccessfully;
    private int mcpServerCreateFailures;
    private int mcpServerDeleteFailures;

    // 工具相关统计
    private int totalToolsToAdd;
    private int totalToolsToRemove;
    private int toolsAddedSuccessfully;
    private int toolsRemovedSuccessfully;
    private int toolAddFailures;
    private int toolRemoveFailures;

    // 默认工具相关统计
    private int mcpServersWithDefaultToolChanges;
    private int defaultToolsToAdd;
    private int defaultToolsToRemove;
    private int defaultToolsAddedSuccessfully;
    private int defaultToolsRemovedSuccessfully;
    private int defaultToolAddFailures;
    private int defaultToolRemoveFailures;

    // 详细记录
    private List<McpServerDiff> mcpServerDiffs = new ArrayList<>();
    private List<String> errorMessages = new ArrayList<>();

    /**
     * McpServer差异详情
     */
    @Data
    public static class McpServerDiff {
        private Long mcpServerId;
        private String mcpServerName;
        private String businessLine;
        private DiffType diffType;
        private boolean processSuccess;
        private String errorMessage;

        // 工具差异
        private Set<Long> dbToolIds = new HashSet<>();
        private Set<Long> memoryToolIds = new HashSet<>();
        private Set<Long> toolsToAdd = new HashSet<>();
        private Set<Long> toolsToRemove = new HashSet<>();
        private int toolsAddedCount;
        private int toolsRemovedCount;
        private int toolAddFailureCount;
        private int toolRemoveFailureCount;

        // 默认工具差异
        private List<String> dbDefaultTools = new ArrayList<>();
        private List<String> memoryDefaultTools = new ArrayList<>();
        private List<String> defaultToolsToAdd = new ArrayList<>();
        private List<String> defaultToolsToRemove = new ArrayList<>();
        private int defaultToolsAddedCount;
        private int defaultToolsRemovedCount;
        private int defaultToolAddFailureCount;
        private int defaultToolRemoveFailureCount;
    }

    public enum DiffType {
        CREATE, DELETE, UPDATE
    }

    /**
     * 记录McpServer创建成功
     */
    public void recordMcpServerCreateSuccess(Long mcpServerId) {
        mcpServersCreatedSuccessfully++;
        updateMcpServerDiffStatus(mcpServerId, true, null);
    }

    /**
     * 记录McpServer创建失败
     */
    public void recordMcpServerCreateFailure(Long mcpServerId, String errorMessage) {
        mcpServerCreateFailures++;
        updateMcpServerDiffStatus(mcpServerId, false, errorMessage);
        errorMessages.add(String.format("McpServer创建失败[%d]: %s", mcpServerId, errorMessage));
    }

    /**
     * 记录McpServer删除成功
     */
    public void recordMcpServerDeleteSuccess(Long mcpServerId) {
        mcpServersDeletedSuccessfully++;
        updateMcpServerDiffStatus(mcpServerId, true, null);
    }

    /**
     * 记录McpServer删除失败
     */
    public void recordMcpServerDeleteFailure(Long mcpServerId, String errorMessage) {
        mcpServerDeleteFailures++;
        updateMcpServerDiffStatus(mcpServerId, false, errorMessage);
        errorMessages.add(String.format("McpServer删除失败[%d]: %s", mcpServerId, errorMessage));
    }

    /**
     * 记录McpServer更新成功
     */
    public void recordMcpServerUpdateSuccess(Long mcpServerId) {
        updateMcpServerDiffStatus(mcpServerId, true, null);
    }

    /**
     * 记录McpServer更新失败
     */
    public void recordMcpServerUpdateFailure(Long mcpServerId, String errorMessage) {
        updateMcpServerDiffStatus(mcpServerId, false, errorMessage);
        errorMessages.add(String.format("McpServer更新失败[%d]: %s", mcpServerId, errorMessage));
    }

    /**
     * 记录工具注册成功
     */
    public void recordToolAddSuccess(Long mcpServerId, Long toolId) {
        toolsAddedSuccessfully++;
        updateMcpServerDiffToolStatus(mcpServerId, toolId, true, false);
    }

    /**
     * 记录工具注册失败
     */
    public void recordToolAddFailure(Long mcpServerId, Long toolId, String errorMessage) {
        toolAddFailures++;
        updateMcpServerDiffToolStatus(mcpServerId, toolId, false, false);
        errorMessages.add(String.format("工具注册失败[mcpServerId=%d, toolId=%d]: %s", mcpServerId, toolId, errorMessage));
    }

    /**
     * 记录工具注销成功
     */
    public void recordToolRemoveSuccess(Long mcpServerId, Long toolId) {
        toolsRemovedSuccessfully++;
        updateMcpServerDiffToolStatus(mcpServerId, toolId, true, true);
    }

    /**
     * 记录工具注销失败
     */
    public void recordToolRemoveFailure(Long mcpServerId, Long toolId, String errorMessage) {
        toolRemoveFailures++;
        updateMcpServerDiffToolStatus(mcpServerId, toolId, false, true);
        errorMessages.add(String.format("工具注销失败[mcpServerId=%d, toolId=%d]: %s", mcpServerId, toolId, errorMessage));
    }

    /**
     * 记录默认工具添加成功
     */
    public void recordDefaultToolAddSuccess(Long mcpServerId, String toolName) {
        defaultToolsAddedSuccessfully++;
        updateMcpServerDiffDefaultToolStatus(mcpServerId, toolName, true, false);
    }

    /**
     * 记录默认工具添加失败
     */
    public void recordDefaultToolAddFailure(Long mcpServerId, String toolName, String errorMessage) {
        defaultToolAddFailures++;
        updateMcpServerDiffDefaultToolStatus(mcpServerId, toolName, false, false);
        errorMessages.add(String.format("默认工具添加失败[mcpServerId=%d, toolName=%s]: %s", mcpServerId, toolName, errorMessage));
    }

    /**
     * 记录默认工具移除成功
     */
    public void recordDefaultToolRemoveSuccess(Long mcpServerId, String toolName) {
        defaultToolsRemovedSuccessfully++;
        updateMcpServerDiffDefaultToolStatus(mcpServerId, toolName, true, true);
    }

    /**
     * 记录默认工具移除失败
     */
    public void recordDefaultToolRemoveFailure(Long mcpServerId, String toolName, String errorMessage) {
        defaultToolRemoveFailures++;
        updateMcpServerDiffDefaultToolStatus(mcpServerId, toolName, false, true);
        errorMessages.add(String.format("默认工具移除失败[mcpServerId=%d, toolName=%s]: %s", mcpServerId, toolName, errorMessage));
    }

    /**
     * 添加McpServer差异记录
     */
    public McpServerDiff addMcpServerDiff(Long mcpServerId, String mcpServerName, String businessLine, DiffType diffType) {
        McpServerDiff diff = new McpServerDiff();
        diff.setMcpServerId(mcpServerId);
        diff.setMcpServerName(mcpServerName);
        diff.setBusinessLine(businessLine);
        diff.setDiffType(diffType);
        mcpServerDiffs.add(diff);
        return diff;
    }

    /**
     * 设置工具差异信息
     */
    public void setToolDiff(Long mcpServerId, Set<Long> dbToolIds, Set<Long> memoryToolIds,
                            Set<Long> toolsToAdd, Set<Long> toolsToRemove) {
        McpServerDiff diff = findMcpServerDiff(mcpServerId);
        if (diff != null) {
            diff.setDbToolIds(new HashSet<>(dbToolIds));
            diff.setMemoryToolIds(new HashSet<>(memoryToolIds));
            diff.setToolsToAdd(new HashSet<>(toolsToAdd));
            diff.setToolsToRemove(new HashSet<>(toolsToRemove));
        }
        totalToolsToAdd += toolsToAdd.size();
        totalToolsToRemove += toolsToRemove.size();
    }

    /**
     * 设置默认工具差异信息
     */
    public void setDefaultToolDiff(Long mcpServerId, List<String> dbDefaultTools, List<String> memoryDefaultTools,
                                   List<String> defaultToolsToAdd, List<String> defaultToolsToRemove) {
        McpServerDiff diff = findMcpServerDiff(mcpServerId);
        if (diff != null) {
            diff.setDbDefaultTools(new ArrayList<>(dbDefaultTools));
            diff.setMemoryDefaultTools(new ArrayList<>(memoryDefaultTools));
            diff.setDefaultToolsToAdd(new ArrayList<>(defaultToolsToAdd));
            diff.setDefaultToolsToRemove(new ArrayList<>(defaultToolsToRemove));
        }
        if (!defaultToolsToAdd.isEmpty() || !defaultToolsToRemove.isEmpty()) {
            mcpServersWithDefaultToolChanges++;
        }
        this.defaultToolsToAdd += defaultToolsToAdd.size();
        this.defaultToolsToRemove += defaultToolsToRemove.size();
    }

    private void updateMcpServerDiffStatus(Long mcpServerId, boolean success, String errorMessage) {
        McpServerDiff diff = findMcpServerDiff(mcpServerId);
        if (diff != null) {
            diff.setProcessSuccess(success);
            diff.setErrorMessage(errorMessage);
        }
    }

    private void updateMcpServerDiffToolStatus(Long mcpServerId, Long toolId, boolean success, boolean isRemove) {
        McpServerDiff diff = findMcpServerDiff(mcpServerId);
        if (diff != null) {
            if (isRemove) {
                if (success) {
                    diff.setToolsRemovedCount(diff.getToolsRemovedCount() + 1);
                } else {
                    diff.setToolRemoveFailureCount(diff.getToolRemoveFailureCount() + 1);
                }
            } else {
                if (success) {
                    diff.setToolsAddedCount(diff.getToolsAddedCount() + 1);
                } else {
                    diff.setToolAddFailureCount(diff.getToolAddFailureCount() + 1);
                }
            }
        }
    }

    private void updateMcpServerDiffDefaultToolStatus(Long mcpServerId, String toolName, boolean success, boolean isRemove) {
        McpServerDiff diff = findMcpServerDiff(mcpServerId);
        if (diff != null) {
            if (isRemove) {
                if (success) {
                    diff.setDefaultToolsRemovedCount(diff.getDefaultToolsRemovedCount() + 1);
                } else {
                    diff.setDefaultToolRemoveFailureCount(diff.getDefaultToolRemoveFailureCount() + 1);
                }
            } else {
                if (success) {
                    diff.setDefaultToolsAddedCount(diff.getDefaultToolsAddedCount() + 1);
                } else {
                    diff.setDefaultToolAddFailureCount(diff.getDefaultToolAddFailureCount() + 1);
                }
            }
        }
    }

    public McpServerDiff findMcpServerDiff(Long mcpServerId) {
        return mcpServerDiffs.stream()
                .filter(diff -> Objects.equals(diff.getMcpServerId(), mcpServerId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 打印统计摘要
     */
    public void logForMcpEventSync()  {
        try{
            log.info("=== MCP同步统计摘要，当前节点:{} ===", InetAddress.getLocalHost().getHostAddress());
            log.info("McpServer统计: 数据库总数={}, 内存总数={}, 需创建={}, 需删除={}, 需对比={}",
                    totalMcpServersInDb, totalMcpServersInMemory, mcpServersToCreate, mcpServersToDelete, mcpServersToCompare);
            log.info("McpServer处理结果: 创建成功={}, 创建失败={}, 删除成功={}, 删除失败={}",
                    mcpServersCreatedSuccessfully, mcpServerCreateFailures, mcpServersDeletedSuccessfully, mcpServerDeleteFailures);
            log.info("工具统计: 需添加={}, 需移除={}, 添加成功={}, 添加失败={}, 移除成功={}, 移除失败={}",
                    totalToolsToAdd, totalToolsToRemove, toolsAddedSuccessfully, toolAddFailures, toolsRemovedSuccessfully, toolRemoveFailures);
            log.info("默认工具统计: 有变更的McpServer数={}, 需添加={}, 需移除={}, 添加成功={}, 添加失败={}, 移除成功={}, 移除失败={}",
                    mcpServersWithDefaultToolChanges, defaultToolsToAdd, defaultToolsToRemove,
                    defaultToolsAddedSuccessfully, defaultToolAddFailures, defaultToolsRemovedSuccessfully, defaultToolRemoveFailures);

            if (!errorMessages.isEmpty()) {
                log.error("同步过程中的错误信息:");
                errorMessages.forEach(log::error);
            }
        } catch (UnknownHostException e) {
            log.error("获取本地主机地址失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 打印详细统计信息
     */
    public void logPrintDetailedStatistics() {
        logForMcpEventSync();

        if (!mcpServerDiffs.isEmpty()) {
            log.info("=== MCP同步详细信息 ===");
            for (McpServerDiff diff : mcpServerDiffs) {
                log.info("McpServer[{}] {} - {}: 处理{}",
                        diff.getMcpServerId(), diff.getMcpServerName(), diff.getDiffType(),
                        diff.isProcessSuccess() ? "成功" : "失败");

                if (!diff.getToolsToAdd().isEmpty() || !diff.getToolsToRemove().isEmpty()) {
                    log.info("  工具变更: DB工具数={}, 内存工具数={}, 需添加={}, 需移除={}, 添加成功={}, 移除成功={}",
                            diff.getDbToolIds().size(), diff.getMemoryToolIds().size(),
                            diff.getToolsToAdd().size(), diff.getToolsToRemove().size(),
                            diff.getToolsAddedCount(), diff.getToolsRemovedCount());
                }

                if (!diff.getDefaultToolsToAdd().isEmpty() || !diff.getDefaultToolsToRemove().isEmpty()) {
                    log.info("  默认工具变更: DB默认工具={}, 内存默认工具={}, 需添加={}, 需移除={}, 添加成功={}, 移除成功={}",
                            diff.getDbDefaultTools(), diff.getMemoryDefaultTools(),
                            diff.getDefaultToolsToAdd(), diff.getDefaultToolsToRemove(),
                            diff.getDefaultToolsAddedCount(), diff.getDefaultToolsRemovedCount());
                }

                if (diff.getErrorMessage() != null) {
                    log.error("  错误信息: {}", diff.getErrorMessage());
                }
            }
        }
    }
}

