package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service;

import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.TalosRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.TalosQueryResponse;

import java.util.List;

/**
 * Talos数据查询服务接口
 * 提供基于TalosTwo SDK的Hive表数据查询功能
 * 
 * <AUTHOR>
 */
public interface TalosQueryService {


    TalosQueryResponse<List<List<Object>>> executeQuery(TalosRequest talosRequest);


}