package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExecuteFridayMissionRequest implements Serializable {

    private String missionId;

    /**
     * 模型基座名称
     */
    private String modelBaseName;

    /**
     * 模型训练方法，sft/dpo
     */
    private String trainMethod;

    /***
     * 数据集名称
     */
    private String datasetName;

    /**
     * 数据集s3链接
     */
    private String s3Urls;

    /***
     * 评测集s3链接
     */
    private String inputS3Url;

    /**
     * 数据集版本列表
     */
    private List<Integer> versionList = new ArrayList<>();

    /**
     * 评测参数
     */
    private Map<String,String> modelParam = new HashMap<>();

    /**
     * 模型训练高级参数
     */
    private Map<String,Object> modelTrainAdvancedParams =new HashMap<>();

    /**
     * 模型训练默认参数
     */
    private Map<String,Object> modelTrainDefaultParams =new HashMap<>();


    private String misId;

}
