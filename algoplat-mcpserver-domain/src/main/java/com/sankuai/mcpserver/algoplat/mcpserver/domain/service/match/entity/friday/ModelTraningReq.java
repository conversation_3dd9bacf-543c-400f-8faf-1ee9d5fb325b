package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/6
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ModelTraningReq {
    private Boolean advancedParams = false;
    public String artifactsVersionId = "";
    private Datasets datasetsObject;
    private String description = "";
    private Map<String, Object> modelAdvancedParams;
    private Map<String, Object> modelParams;
    private Map<String, Object> modelClassInfo = new HashMap<>();
    private String modelClassType = "text";
    private String modelName;
    private String sftType;
    private String taskName;
    private String tenantId = "434610572891277";
    private String trainMethod;
    private String trainModelSource ="base";
    private Integer trainType = 1;
    private Boolean useUserResource = false;
    private String userResource;


}
