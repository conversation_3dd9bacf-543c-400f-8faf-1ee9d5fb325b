package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Datasets {
    private List<DatasetInfo> datasetList;
    private Integer estimatedTrainCost = 0;
    private String promptType;
    private Integer tokenCount = 0;

    @Data
    public static class DatasetInfo {
        private Integer dataNum;
        private Long datasetId;
        private String datasetName;
        private String datasetVersion;
    }
}
