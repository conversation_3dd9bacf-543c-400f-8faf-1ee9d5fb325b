package com.sankuai.mcpserver.algoplat.mcpserver.domain.entity;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

@Data
public class CreateCitadelDocReq {
    @ToolParam(description = "用户mis号", required = true)
    private String misId;

    @ToolParam(description = "文档标题", required = false)
    private String title;

    @ToolParam(description = "文档内容，与contentS3Url字段二选一", required = false)
    private String content;

    @ToolParam(description = "文档所属空间id", required = false)
    private String spaceId;

    @ToolParam(description = "父文档url", required = false)
    private String parentUrl;

    @ToolParam(description = "文档内容S3链接", required = false)
    private String contentS3Url;
}
