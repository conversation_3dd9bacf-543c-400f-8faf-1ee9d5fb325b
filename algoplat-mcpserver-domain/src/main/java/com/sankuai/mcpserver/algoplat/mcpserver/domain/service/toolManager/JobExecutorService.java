package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.AsynJobExecutorStatusEnum;
import java.util.Map;

public interface JobExecutorService {


    /**
     * 预处理器：创建任务记录并存储到数据库
     * @param missionId
     * @param requestParams
     * @return
     */
    McpMissionInfo withPreProcessor(String missionId, Map<String, Object> requestParams);

    /**
     * 后处理器：存储执行结果到数据库
     * @param mcpMissionInfo 任务信息
     * @param result 执行结果
     */
    Boolean withPostProcessor(McpMissionInfo mcpMissionInfo, AsynJobExecutorStatusEnum statusEnum, String result);

    /**
     * 错误处理器：存储错误信息到数据库
     * @param mcpMissionInfo 任务信息
     * @param e 异常信息
     */
    void withErrorHandler(McpMissionInfo mcpMissionInfo, Exception e);

    /**
     * 获取任务结果（从数据库查询）
     * @param taskId 任务ID
     * @return 任务信息，包含执行结果
     */
    McpMissionInfo getTaskResult(String taskId);


    /**
     * 检查任务是否已完成（从数据库查询）
     * @param missionId 任务ID
     * @return 如果任务完成或失败则返回true，否则返回false
     */
    boolean isTaskFinished(String missionId);

    /**
     * 创建agent任务（执行中）
     * @param missionId missionId
     * @param taskAgentSessionId 任务agent的sessionId
     * @param requestParams 请求参数
     * @return 任务信息
     */
    McpMissionInfo createAgentTask(String missionId, String taskAgentSessionId,Map<String, Object> requestParams);
}
