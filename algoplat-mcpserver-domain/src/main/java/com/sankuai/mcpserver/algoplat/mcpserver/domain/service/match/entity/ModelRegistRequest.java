package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/18
 */
@Builder
@Data
public class ModelRegistRequest {

    /**
     * 模型名称
     */
    private String name;

    /**
     * 版本备注
     */
    private String description;

    /**
     * 任务类型
     * 默认为文本生成:text
     */
    private String modelClassType;

    /**
     * 模型基座名称:如LongCat-Medium-8K-Base
     */
    private String modelClass;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 模型版本列表
     */
    private List<Object> artifatcsVersionList;

    /**
     * 上一版本号
     */
    private Integer latestVersion;

    /**
     * 版本标识
     */
    private Long artifatcsVersionId;

    /**
     * 模型路径，选填
     */
    private String modelPath = "";


    /**
     * 模型预制名称,选填
     */
    private String modelPrefabricatedName = "";


    /**
     * 部署模型名称,选填。
     */
    private String modelName = "";

}
