package com.sankuai.mcpserver.algoplat.mcpserver.domain.configs;

import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DefaultToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.tool.MatchTool;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.parser.ParserToolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/9
 */
@Slf4j
@Configuration
public class DefaultToolConfig {

    @Bean
    public ToolCallbackProvider ParserToolServiceTool(ParserToolService parserToolService) {
        log.info("ParserToolServiceTool init");
        return MethodToolCallbackProvider.builder()
                .toolObjects(parserToolService)
                .build();
    }
    @Bean
    public ToolCallbackProvider DefaultToolServiceTool(DefaultToolService defaultToolService) {
        log.info("DefaultToolServiceTool init");
        return MethodToolCallbackProvider.builder()
                .toolObjects(defaultToolService)
                .build();
    }

    @Bean
    public ToolCallbackProvider MatcherToolServiceTool(MatchTool matchTool) {
        log.info("MatchToolServiceTool init");
        return MethodToolCallbackProvider.builder()
                .toolObjects(matchTool)
                .build();
    }
}
