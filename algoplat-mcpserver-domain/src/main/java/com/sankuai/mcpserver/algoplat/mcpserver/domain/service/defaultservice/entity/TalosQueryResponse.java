package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Talos查询响应结果封装类
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TalosQueryResponse<T> {
    /**
     * 响应码，成功为"200"
     */
    private String code;
    /**
     * 响应消息
     */
    private String message;
    /**
     * 列信息
     */
    private List<String> column;
    /**
     * 响应数据
     */
    private T data;

    /**
     * 创建成功响应
     *
     * @param data 响应数据
     * @param column 列信息
     * @return 成功响应对象
     */
    public static <T> TalosQueryResponse<T> success(List<String> column,T data) {
        return TalosQueryResponse.<T>builder()
                .code("200")
                .message("调用成功")
                .column(column)
                .data(data)
                .build();
    }

    /**
     * 创建失败响应
     *
     * @param code 错误码
     * @param message 错误消息
     * @return 失败响应对象
     */
    public static <T> TalosQueryResponse<T> fail(String code, String message) {
        return TalosQueryResponse.<T>builder()
                .code(code)
                .message(message)
                .build();
    }
}