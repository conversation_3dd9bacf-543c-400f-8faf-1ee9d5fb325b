package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.McpMissionCommonContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.JobExecutorService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpMissionDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.AsynJobExecutorStatusEnum;
import java.util.Date;
import java.util.Map;
import javax.annotation.Resource;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.MissionTypeEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/18 16:58
 */
@Service("asynJobExecutorService")
public class AsynJobExecutorServiceImpl implements JobExecutorService {
  @Resource
  private McpMissionDao mcpMissionDao;

  /**
   * 预处理器：创建任务记录并存储到数据库
   */
  @Override
  public McpMissionInfo withPreProcessor(String missionId, Map<String, Object> requestParams) {
    McpMissionCommonContext commonContext = new McpMissionCommonContext();
    commonContext.setSessionId(MapUtils.getString(requestParams, "sessionId"));

    McpMissionInfo mcpMissionInfo = new McpMissionInfo()
        .addMissionId(missionId)
        .addAddTime(new Date())
        .addMisId(getmisId(requestParams))
        .addMissionContext(JSON.toJSONString(commonContext))
        .addStatus(AsynJobExecutorStatusEnum.PENDING.getStatus())
        .addMissionRequest(JSON.toJSONString(requestParams))
        .addType(MissionTypeEnum.THRIFT.getCode());

    mcpMissionDao.insertMcpMissionInfo(mcpMissionInfo);
    return mcpMissionInfo;
  }


  /**
   * 后处理器：存储执行结果到数据库
   */
  @Override
  public Boolean withPostProcessor(McpMissionInfo mcpMissionInfo, AsynJobExecutorStatusEnum statusEnum, String result) {
      if (mcpMissionInfo == null || statusEnum == null) {
          return false;
      }

      McpMissionInfo updateMissionInfo = new McpMissionInfo();
      updateMissionInfo.addId(mcpMissionInfo.getId()).addUpdateTime(new Date()).addStatus(statusEnum.getStatus());

      if (StringUtils.isNotBlank(result)) {
          updateMissionInfo.addMissionResult(result);
      }

      mcpMissionDao.updateMissionInfo(updateMissionInfo);
      return true;
  }

  /**
   * 错误处理器：存储错误信息到数据库
   */
  @Override
  public void withErrorHandler(McpMissionInfo mcpMissionInfo, Exception e) {
    if (mcpMissionInfo == null) {
      return;
    }

    McpMissionCommonContext mcpMissionCommonContext = new McpMissionCommonContext();
    mcpMissionCommonContext.setExecuteMessage(e.getMessage());

    mcpMissionInfo.addUpdateTime(new Date())
        .addStatus(AsynJobExecutorStatusEnum.FAILED.getStatus())
        .addMissionResult(JSONObject.toJSONString(mcpMissionCommonContext));

    mcpMissionDao.updateMissionInfo(mcpMissionInfo);
  }

  /**
   * 获取任务结果（从数据库查询）
   * @param taskId 任务ID
   * @return 任务信息，包含执行结果
   */
  public McpMissionInfo getTaskResult(String taskId) {
    return mcpMissionDao.getMcpMissionInfoByMissionId(taskId);
  }

  /**
   * 获取任务状态（从数据库查询）
   * @param missionId 任务ID
   * @return 任务状态
   */
  public AsynJobExecutorStatusEnum getTaskStatus(String missionId) {
    if (StringUtils.isBlank(missionId)) {
      return AsynJobExecutorStatusEnum.UNEXIST;
    }

    McpMissionInfo missionInfo = mcpMissionDao.getMcpMissionInfoByMissionId(missionId);
    if (missionInfo == null) {
      return AsynJobExecutorStatusEnum.UNEXIST;
    }

    return AsynJobExecutorStatusEnum.getByStatus(missionInfo.getStatus());
  }

  /**
   * 检查任务是否已完成（从数据库查询）
   * @param missionId 任务ID
   * @return 如果任务完成或失败则返回true，否则返回false
   */
  public boolean isTaskFinished(String missionId) {
    AsynJobExecutorStatusEnum status = getTaskStatus(missionId);
    return status == AsynJobExecutorStatusEnum.COMPLETED || status == AsynJobExecutorStatusEnum.FAILED;
  }

  /**
   * 获取misId
   * @param requestParams
   * @return
   */
  private String getmisId(Map<String, Object> requestParams) {
    if (MapUtils.isEmpty(requestParams)) {
      return null;
    }

    Map<String, Object> req = JSON.parseObject(MapUtils.getString(requestParams, "req",""), new TypeReference<>(){});
    if (MapUtils.isNotEmpty(req)) {
      req = JSON.parseObject(MapUtils.getString(req, "req", ""), new TypeReference<>(){});
      return MapUtils.getString(req, "misId");
    }

    return null;
  }

  @Override
  public McpMissionInfo createAgentTask(String missionId, String taskAgentSessionId, Map<String, Object> requestParams) {
    McpMissionCommonContext commonContext = new McpMissionCommonContext();
    String parentSessionId = MapUtils.getString(requestParams, "parentSessionId");
    if (StringUtils.isNotBlank(parentSessionId)) {
      commonContext.setParentSessionId(parentSessionId);
    } else {
      String request = MapUtils.getString(requestParams, "request");
      if (StringUtils.isNotBlank(request)) {
        Map<String, Object> requestMap = JSON.parseObject(request, new TypeReference<>() {
        });
        if (MapUtils.isNotEmpty(requestMap)) {
          parentSessionId = MapUtils.getString(requestMap, "parentSessionId");
          commonContext.setParentSessionId(parentSessionId);
        }
      }
    }

    commonContext.setTaskAgentSessionId(taskAgentSessionId);

    McpMissionInfo mcpMissionInfo = new McpMissionInfo()
            .addMissionId(missionId)
            .addAddTime(new Date())
            .addMisId(getmisId(requestParams))
            .addMissionContext(JSON.toJSONString(commonContext))
            .addStatus(AsynJobExecutorStatusEnum.RUNNING.getStatus())
            .addMissionRequest(JSON.toJSONString(requestParams))
            .addType(MissionTypeEnum.AUTO_AGENT.getCode());

    mcpMissionDao.insertMcpMissionInfo(mcpMissionInfo);
    return mcpMissionInfo;
  }
}
