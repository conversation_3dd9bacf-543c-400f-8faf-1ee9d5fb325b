package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Description 创建数据集的请求入参
 * @Date 2025/5/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomEvalModel {
    /**
     * 训练集名称
     */
    private String name;

    /**
     * 模型类型
     */
    private Integer modelType = 2;

    /**
     * 评估描述
     */
    private String description = "";

    /**
     * S3存储URL
     */
    private String userS3Url;

    /**
     * 子类型
     */
    private String subType = "train";


    private String trainMethod = "sft";

    /**
     * 提示类型
     */
    private String promptType = "custom";

}