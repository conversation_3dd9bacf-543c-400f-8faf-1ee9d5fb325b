package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.dianping.cat.Cat;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.GetCollaborationMarkdownBySsoReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetCollaborationMarkdownBySsoResp;
import com.sankuai.xm.openplatform.api.service.open.CollaborationContentReq;
import com.sankuai.xm.openplatform.api.service.open.CollaborationContentResp;
import com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
public class XmCitadelProxy {

    @Autowired
    private XmOpenKmServiceI.Iface xmOpenKmService;

    @Autowired
    private CitadelService citadelService;



    /**
     * 获取学城文档的Markdown内容
     * @param accessToken 访问令牌
     * @param userToken 用户令牌
     * @param docId 文档ID
     * @param ultraNodeTypes 需要高阶处理的节点类型
     * @param extraNodeTypes 需要额外处理的节点类型
     * @param needNodeId 是否需要文档内容块的唯一标识
     * @return 文档的Markdown内容
     */
    public String getCitadelMarkdown(String accessToken, String userToken, Long docId, Set<String> ultraNodeTypes, Set<String> extraNodeTypes, Boolean needNodeId) {
        GetCollaborationMarkdownBySsoReq request = new GetCollaborationMarkdownBySsoReq();
        GetCollaborationMarkdownBySsoReq.Request req = new GetCollaborationMarkdownBySsoReq.Request();
        req.setContentId(docId);
        req.setUltraNodeTypes(ultraNodeTypes);
        req.setExtraNodeTypes(extraNodeTypes);
        req.setNeedNodeId(needNodeId);
        request.setRequest(req);
        try {
            GetCollaborationMarkdownBySsoResp resp = citadelService.getCollaborationMarkdownBySso(accessToken, userToken, request);
            if (resp != null && resp.getStatus() != null && resp.getStatus().getCode()==0 && resp.getData().getSuccess()) {
                return resp.getData().getContent(); // Returns the document markdown content
            } else {
                throw new RuntimeException("Failed to read Xue Cheng document markdown: " + (resp != null && resp.getStatus() != null ? resp.getStatus().getMsg() : "Unknown error"));
            }
        } catch (Exception e) {
            throw new RuntimeException("Error reading Xue Cheng document markdown", e);
        }
    }

    public String createXueChengDocument(String accessToken, long operatorEmpId, String title, String content, String spaceId, String parentId) {
        CollaborationContentReq req = new CollaborationContentReq();
        req.setOperatorEmpId(operatorEmpId);
        req.setTitle(title);
        req.setContent(content);
        req.setSpaceId(spaceId);
        req.setParentId(parentId);
        try {
            CollaborationContentResp resp = xmOpenKmService.addCollaborationContent(accessToken, req);
            if (resp != null && resp.getStatus() != null && resp.getStatus().getCode()==0) {
                return resp.getInfo();
            } else {
                RuntimeException runtimeException = new RuntimeException("Failed to create Xue Cheng document: " + (resp != null && resp.getStatus() != null ? resp.getStatus().getMsg() : "Unknown error"));
                Cat.logError(runtimeException);
                throw runtimeException;
            }
        } catch (Exception e) {
            
            throw new RuntimeException("Error creating Xue Cheng document", e);
        }
    }
}
