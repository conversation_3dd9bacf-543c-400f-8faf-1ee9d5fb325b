package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.zebra.util.StringUtils;
import com.sankuai.grocery.dola.thrift.model.response.TBaseResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.McpMissionResult;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.McpToolResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpMissionDao;
import com.sankuai.zb.metadata.api.dto.flow.standard.AnalysisModel;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowAnalysisDTO;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowInstanceStandardDTO;
import com.sankuai.zb.metadata.api.dto.rule.ParseRuleVersionDTO;
import com.sankuai.zb.metadata.api.dto.rule.ParseRuleWithVersionDTO;
import com.sankuai.zb.metadata.api.request.rule.ParseRuleVersionRequest;
import com.sankuai.zb.metadata.api.service.FlowManagerThriftService;
import com.sankuai.zb.metadata.api.service.ParseRuleThriftService;
import com.sankuai.zb.metadata.api.service.ParseRuleVersionThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/11
 */
@Slf4j
@Service
public class ThriftToHttpService {

    @Resource
    private McpMissionDao mcpMissionDao;
    @Resource
    private FlowManagerThriftService flowManagerThriftService;

    @Resource
    private ParseRuleThriftService parseRuleThriftService;

    @Resource
    private ParseRuleVersionThriftService parseRuleVersionThriftService;


    /**
     * 新增流程版本-返回版本号
     */
    public TBaseResponse addFlowAnalysisRule(FlowAnalysisDTO flowAnalysisDTO) {
        Transaction t = Cat.newTransaction("ThriftToHttpService", "addFlowAnalysisRule");
        TBaseResponse<Long> longTBaseResponse = null;
        try {
            log.info("addFlowAnalysisRule,flowId:{},mjScheduleFlag:{},desc:{},modelList:{}", flowAnalysisDTO.getFlowId(), flowAnalysisDTO.getMjScheduleFlag(), flowAnalysisDTO.getDesc(), JSONObject.toJSON(flowAnalysisDTO));
            if (flowAnalysisDTO == null || flowAnalysisDTO.getFlowId() == null || flowAnalysisDTO.getDesc() == null || flowAnalysisDTO.getModelList() == null) {
                t.setStatus("param is null");
                return null;
            }
            if (flowAnalysisDTO.getMjScheduleFlag() == null || flowAnalysisDTO.getMjScheduleFlag() == 0) {
                flowAnalysisDTO.setMjScheduleFlag(1);
            }
            if (flowAnalysisDTO.getUpdatedBy() == null) {
                flowAnalysisDTO.setUpdatedBy("Agent");
            }
            longTBaseResponse = flowManagerThriftService.addFlowAnalysisRule(flowAnalysisDTO);
            if (longTBaseResponse.getCode() != 0) {
                t.setStatus("flowManagerThriftService.addFlowAnalysisRule is null");
                return null;
            }
            t.setSuccessStatus();
            return longTBaseResponse;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            
            return null;
        } finally {
            log.info("addFlowAnalysisRule end,flowId:{},mjScheduleFlag:{},desc:{},modelList:{},outPut:{}", flowAnalysisDTO.getFlowId(), flowAnalysisDTO.getMjScheduleFlag(), flowAnalysisDTO.getDesc(), JSONObject.toJSON(flowAnalysisDTO), JSONObject.toJSON(longTBaseResponse));
            t.complete();
        }
    }

    /**
     * 查询流程中指定版本的配置信息
     */
    public TBaseResponse queryFlowInstanceByVersion(Object flowId, Object version) {
        Transaction t = Cat.newTransaction("ThriftToHttpService", "queryFlowInstanceByVersion");
        TBaseResponse<FlowAnalysisDTO> flowInstanceDTO = null;
        try {

            log.info("queryFlowInstanceByVersion,flowId:{},version:{}", flowId, version);
            if (flowId == null || version == null) {
                t.setStatus("param is null");
                return null;
            }
            flowInstanceDTO = flowManagerThriftService.queryFlowInstanceByVersion(Long.valueOf(flowId.toString()), Long.valueOf(version.toString()));
            if (flowInstanceDTO == null || flowInstanceDTO.getCode() != 0) {
                t.setStatus("flowManagerThriftService.queryFlowInstanceByVersion is null");
                return null;
            }
            log.info("queryFlowInstanceByVersion,flowId:{},version", flowId, version);
            t.setSuccessStatus();
            return flowInstanceDTO;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            
            return null;
        } finally {
            log.info("queryFlowInstanceByVersion end,flowId:{},version:{},outPut:{}", flowId, version, JSONObject.toJSON(flowInstanceDTO));
            t.complete();
        }
    }

    /**
     * 查询规则ID指定版本的规则信息
     */
    public TBaseResponse getRuleWithVersion(Object ruleId, Object version) {
        Transaction t = Cat.newTransaction("ThriftToHttpService", "getRuleWithVersion");
        TBaseResponse<List<FlowAnalysisDTO>> flowInstanceDTO = null;
        try {
            log.info("getRuleWithVersion,flowId:{},version:{}", ruleId, version);
            if (ruleId == null || version == null) {
                t.setStatus("param is null");
                return null;
            }
            TBaseResponse<ParseRuleWithVersionDTO> ruleWithVersion = parseRuleThriftService.getRuleWithVersion(Long.valueOf(ruleId.toString()), Integer.valueOf(version.toString()));
            int code = ruleWithVersion.getCode();
            if (code != 0) {
                t.setStatus("getRuleWithVersion is null");
                return null;
            }
            t.setSuccessStatus();
            log.info("getRuleWithVersion,ruleId:{},version:{}", ruleId, version);
            return ruleWithVersion;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            
            return null;
        } finally {
            log.info("getRuleWithVersion end,ruleId:{},version:{},outPut:{}", ruleId, version, JSONObject.toJSON(flowInstanceDTO));
            t.complete();
        }
    }

    /**
     * 新增加工规则版本-返回新的版本号
     */
    public TBaseResponse addParseRuleVersionAndReturn(ParseRuleVersionDTO ruleVersionDTO) {
        Transaction t = Cat.newTransaction("ThriftToHttpService", "addParseRuleVersionAndReturn");
        TBaseResponse<Long> longTBaseResponse = null;
        try {
            log.info("addParseRuleVersionAndReturn,ruleId:{},ruleDetails:{},filter_groovy:{}", ruleVersionDTO.getRuleId(), JSONObject.toJSON(ruleVersionDTO), ruleVersionDTO.getFilter());
            if (ruleVersionDTO.getRuleId() == null || ruleVersionDTO.getRuleVersionDetail() == null) {
                t.setStatus("param is null");
                return null;
            }
            ParseRuleVersionRequest parseRuleVersionRequest = new ParseRuleVersionRequest();
            if (com.alibaba.excel.util.StringUtils.isBlank(ruleVersionDTO.getFilter())) {
                ruleVersionDTO.setFilter("");
            }
            if (com.alibaba.excel.util.StringUtils.isBlank(ruleVersionDTO.getDesc())) {
                ruleVersionDTO.setDesc("wanglu67_agent");
            }
            if (ruleVersionDTO.getType() == null) {
                ruleVersionDTO.setType(2);
            }
            if (ruleVersionDTO.getOperatorMis() == null) {
                ruleVersionDTO.setOperatorMis("Agent");
            }
            parseRuleVersionRequest.setRuleVersionDTO(ruleVersionDTO);
            longTBaseResponse = parseRuleVersionThriftService.addParseRuleVersionAndReturn(parseRuleVersionRequest);
            if (longTBaseResponse.getCode() != 0) {
                t.setStatus("addParseRuleVersionAndReturn.addParseRuleVersionAndReturn is null");
                return null;
            }
            t.setSuccessStatus();
            return longTBaseResponse;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            
            return null;
        } finally {
            log.info("addParseRuleVersionAndReturn end,ruleId:{},ruleDetails:{},filter_groovy:{},outPut:{}", ruleVersionDTO.getRuleId(), JSONObject.toJSON(ruleVersionDTO.getRuleVersionDetail()), ruleVersionDTO.getFilter(), JSONObject.toJSON(longTBaseResponse));
            t.complete();
        }
    }

    public McpServerResponse saveNewFlowEntity(String flowName, String interfaceCode, Integer mjScheduleFlag, String desc, List<String> modelList) {
        Transaction t = Cat.newTransaction("saveNewFlowEntity", "save_new_flow_entity");
        String outPut = null;
        try {
            log.info("save_new_flow_entity,flowName:{},methodCode:{},mjScheduleFlag:{},desc:{},modelList:{}", flowName, interfaceCode, mjScheduleFlag, desc, JSONObject.toJSON(modelList));
            int flowType = 1;
            String channelCode = "172";
            if (mjScheduleFlag == null || mjScheduleFlag == 0) {
                mjScheduleFlag = 1;
            }
            List<AnalysisModel> collect = modelList.stream().map(map -> {
                return JSONObject.parseObject(map, AnalysisModel.class);
            }).collect(Collectors.toList());
            FlowInstanceStandardDTO flowInstanceStandardDTO = new FlowInstanceStandardDTO();
            flowInstanceStandardDTO.setModelList(collect);
            flowInstanceStandardDTO.setFlowName(flowName);
            flowInstanceStandardDTO.setDesc(desc);
            flowInstanceStandardDTO.setFlowType(flowType);
            flowInstanceStandardDTO.setChannelCode(channelCode);
            flowInstanceStandardDTO.setMjScheduleFlag(mjScheduleFlag);
            flowInstanceStandardDTO.setMethodCode(interfaceCode);
            flowInstanceStandardDTO.setUpdatedBy("Agent");
            TBaseResponse<Long> longTBaseResponse = flowManagerThriftService.flowInstanceQuicklySave(flowInstanceStandardDTO);
            McpServerResponse mcpServerResponse = new McpServerResponse();

            if (longTBaseResponse.getCode() != 0) {
                t.setStatus("flowManagerThriftService.flowInstanceQuicklySave error");
                mcpServerResponse.setCode(longTBaseResponse.getCode());
                mcpServerResponse.setMessage(longTBaseResponse.getMsg());
                mcpServerResponse.setData(JSONObject.toJSONString(longTBaseResponse.getData()));
                return mcpServerResponse;
            }
            outPut = JSONObject.toJSONString(longTBaseResponse);
            mcpServerResponse.setCode(0);
            mcpServerResponse.setMessage("success");
            mcpServerResponse.setData(outPut);
            t.setSuccessStatus();
            log.info("save_new_flow_entity end,flowName:{},methodCode:{},mjScheduleFlag:{},desc:{},modelList:{},outPut:{}", flowName, interfaceCode, mjScheduleFlag, desc, JSONObject.toJSON(modelList), outPut);
            return mcpServerResponse;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            log.error("save_new_flow_entity error,flowName:{},methodCode:{},mjScheduleFlag:{},desc:{},modelList:{}", flowName, interfaceCode, mjScheduleFlag, desc, JSONObject.toJSON(modelList), e);
        } finally {
            t.complete();
        }
        return null;
    }
    public McpToolResponse<McpMissionResult> queryFridayMissionStatus(String missionId) {
        McpMissionInfo mcpMissionInfoByMission = mcpMissionDao.getMcpMissionInfoByMissionId(missionId);
        if (mcpMissionInfoByMission == null ) {
            Cat.logEvent("FridayMissionNotExist", missionId);
            return McpToolResponse.fail("Friday任务不存在!",null);
        }
        if(mcpMissionInfoByMission.getStatus() == -2){
            Cat.logEvent("FridayMissionHaveNotAlready", missionId);
            return McpToolResponse.success("Friday任务还未开始执行，请稍后查询！",null);
        }
        ExecuteFridayMissionContext executeFridayMissionContext = JSONObject.parseObject(mcpMissionInfoByMission.getMissionContext(), ExecuteFridayMissionContext.class);
        String message = executeFridayMissionContext.getExecuteMessage();
        if(mcpMissionInfoByMission.getStatus() == 1 && mcpMissionInfoByMission.getMissionResult() != null){
            message = "成功";
        }
        McpMissionResult mcpMissionResult = new McpMissionResult();
        mcpMissionResult.setMissionId(missionId);
        String missionResult = executeFridayMissionContext.getMissionResult();
        if(StringUtils.isNotBlank(missionResult)){
            Map map = JSONObject.parseObject(missionResult, Map.class);
            mcpMissionResult.setMissionResult(map.get("result").toString());
        }
        mcpMissionResult.setContextInfo(buildMissionContextInfo(executeFridayMissionContext));
        mcpMissionResult.setStatus(mcpMissionInfoByMission.getStatus());
        mcpMissionResult.setRequest(mcpMissionInfoByMission.getMissionRequest());
        return McpToolResponse.success(message, mcpMissionResult);
    }

    public Map<String,Object> buildMissionContextInfo(ExecuteFridayMissionContext context) {
        ExecuteFridayMissionContext executeFridayMissionContext = new ExecuteFridayMissionContext();
        BeanUtils.copyProperties(context, executeFridayMissionContext);
        executeFridayMissionContext.setRequest(null);
        return JacksonUtil.toBeanWithNullDefault(JSONObject.toJSONString(executeFridayMissionContext), Map.class);
    }

}
