package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.parser;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.reflect.TypeToken;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.sankuai.grocery.dola.enums.frequent.ResponseCode;
import com.sankuai.grocery.dola.thrift.model.response.TBaseList;
import com.sankuai.grocery.dola.thrift.model.response.TBaseResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.ToolManagerService;
import com.sankuai.nib.data.zb.parse.platform.api.dto.ParseRuleValidRequestDTO;
import com.sankuai.nib.data.zb.parse.platform.api.dto.ParseRuleValidResponseDTO;
import com.sankuai.nib.data.zb.parse.platform.api.service.ParseRuleValidService;
import com.sankuai.zb.metadata.api.dto.deliver.DeliverConfigTableDTO;
import com.sankuai.zb.metadata.api.dto.flow.standard.AnalysisModel;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowAnalysisDTO;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowInstanceStandardDTO;
import com.sankuai.zb.metadata.api.dto.rule.*;
import com.sankuai.zb.metadata.api.request.deliver.DeliverConfigTableListRequest;
import com.sankuai.zb.metadata.api.request.flow.FlowInstanceQueryRequestDTO;
import com.sankuai.zb.metadata.api.request.rule.ParseRuleVersionRequest;
import com.sankuai.zb.metadata.api.request.task.TaskFlowQueryRequestDTO;
import com.sankuai.zb.metadata.api.response.BaseResponse;
import com.sankuai.zb.metadata.api.response.task.TaskFlowResponseDTO;
import com.sankuai.zb.metadata.api.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/9
 */
@Slf4j
@Service
public class ParserToolService {

    @Resource
    private ParseRuleThriftService parseRuleThriftService;

    @Resource
    private ParseRuleVersionThriftService parseRuleVersionThriftService;

    @Resource
    private TaskFlowThriftService taskFlowThriftService;

    @Resource
    private FlowManagerThriftService flowManagerThriftService;

    @Resource
    private DeliverConfigThriftService deliverConfigThriftService;

    @Resource
    private ParseRuleValidService parseRuleValidService;


    private final static String MCP_SERVER_TOOL = "McpServerTool";

    @Tool(name = "query_transform_rule_entity", description = "查询加工规则中具体逻辑，可以用来查询通用加工规则，也可以用来查询已有加工规则\n" +
            "    1. TransformRuleId: 规则id，必填")
    public String queryTransformRuleEntity(String TransformRuleId) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "query_transform_rule_entity");
        String outPut = null;
        try {
            log.info("query_transform_rule_entity,TransformRuleId:{}", TransformRuleId);
            Long ruleId = Long.valueOf(TransformRuleId.toString());
            TBaseResponse<ParseRuleCompleteDTO> completeRule = parseRuleThriftService.getCompleteRule(ruleId);
            outPut = JSON.toJSONString(completeRule);
            t.setSuccessStatus();
            return outPut;
        } catch (Exception e) {
            t.setStatus(e.getMessage());
            log.error("query_transform_rule_entity error,TransformRuleId:{}", TransformRuleId, e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("query_transform_rule_entity end,TransformRuleId:{},outPut:{}", TransformRuleId, outPut);
            t.complete();
        }

    }

    @Tool(name = "query_flow_entity", description = "根据methodCode查询流程配置详情\n" +
            "    1. methodCode: 必填")
    public String queryFlowEntity(String methodCode) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "query_flow_entity");
        String outPut = null;
        try {
            log.info("query_flow_entity,methodCode:{}", methodCode);
            TBaseResponse<FlowAnalysisDTO> flowAnalysisDTOTBaseResponse = taskFlowThriftService.getFlowAnalysisByMethodCode(methodCode);
            outPut = JSON.toJSONString(flowAnalysisDTOTBaseResponse);
            return outPut;
        } catch (Exception e) {
            t.setStatus(e.getMessage());
            log.error("query_flow_entity error,methodCode:{}", methodCode, e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("query_flow_entity，,methodCode:{},result:{}", methodCode, outPut);
            t.complete();
        }
    }

    @Tool(name = "query_l3_rule_id", description = "根据l3名称查询L3交付规则ID\n" +
            "    1. l3_name: 必填")
    public String queryL3RuleId(String l3_name) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "query_l3_rule_id");
        String outPut = null;
        try {
            log.info("query_l3_rule_id,l3_name:{}", l3_name);
            DeliverConfigTableListRequest deliverConfigTableListRequest = new DeliverConfigTableListRequest();
            deliverConfigTableListRequest.setRuleName(l3_name);
            deliverConfigTableListRequest.setPageNo(1);
            deliverConfigTableListRequest.setPageSize(10);
            TBaseResponse<TBaseList<DeliverConfigTableDTO>> tBaseListTBaseResponse = deliverConfigThriftService.configTableList(deliverConfigTableListRequest);
            TBaseList<DeliverConfigTableDTO> data = tBaseListTBaseResponse.getData();
            outPut = JSONObject.toJSONString(data);
            t.setSuccessStatus();
            return outPut;
        } catch (Exception e) {
            t.setStatus(e.getMessage());
            log.error("query_l3_rule_id error,l3_name:{}", l3_name, e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("query_l3_rule_id end,l3_name:{},outPut:{}", l3_name, outPut);
            t.complete();
        }
    }

    @Tool(name = "query_analysis_rule_id", description = "根据解析代码查询解析模型id\n" +
            "    1. analysis_code: 必填")
    public String queryAnalysisRuleId(String analysis_code) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "query_analysis_rule_id");
        String outPut = null;
        try {
            log.info("query_analysis_rule_id,analysis_code:{}", analysis_code);
            TBaseResponse<ParseRuleDTO> parseRuleDTOTBaseResponse = parseRuleThriftService.queryRuleByCode(analysis_code);
            outPut = JSON.toJSONString(parseRuleDTOTBaseResponse);
            t.setSuccessStatus();
            return outPut;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            log.error("query_analysis_rule_id error,analysis_code:{}", analysis_code, e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("query_analysis_rule_id end,analysis_code:{},outPut:{}", analysis_code, outPut);
            t.complete();
        }
    }

    @Tool(name = "save_new_flow_entity", description = "新增一个流程配置写入系统，需要以下必填参数：\n" +
            "    1. flowName: 流程名称（如'商品数据采集流程'），string类型，必填\n" +
            "    2. interfaceCode: 接口，string类型，必填\n" +
            "    3. mjScheduleFlag: 是否魔镜调度（0：否；1：是），int类型，必填\n" +
            "    4. desc: 流程版本名称（如'v1.0'），string类型，必填\n" +
            "    5. modelList: 流程交付模型列表，string类型，必填，包含如下字段的json数组的字符串,如[\"{\\\"moduleRuleId\\\":17,\\\"l3RuleId\\\":1,\\\"emptyDealFlag\\\":1,\\\"transferCrawlFlag\\\":1,\\\"detailRules\\\":[{\\\"analysisRuleIds\\\":\\\"1036\\\",\\\"mergeType\\\":0,\\\"transformRuleIds\\\":\\\"1029\\\"}]}\"]：\n" +
            "       - moduleRuleId: 交付模型规则ID，long类型，必填\n" +
            "       - l3RuleId: L3交付规则ID，long类型，必填\n" +
            "       - emptyDealFlag: 是否有空deal判断（0：否；1：是），int类型，必填\n" +
            "       - transferCrawlFlag: 是否有转采逻辑（0：否；1：是），int类型，必填\n" +
            "       - detailRules: 解析格式列表，必填，需包含：\n" +
            "         * analysisRuleIds: 解析规则列表（多个规则用','拼接），string类型，必填\n" +
            "         * mergeType: 合并类型（固定为0），int类型，必填\n" +
            "         * transformRuleIds: 加工规则列表，包括通用加工规则+需求加工规则（多个规则用','拼接），string类型，必填\n" +
            "       - extendParams: 扩展参数（按需填写），int类型，非必填")
    public String saveNewFlowEntity(String flowName, String interfaceCode, Integer mjScheduleFlag, String desc, List<String> modelList) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "save_new_flow_entity");
        String outPut = null;
        try {
            log.info("save_new_flow_entity,flowName:{},methodCode:{},mjScheduleFlag:{},desc:{},modelList:{}", flowName, interfaceCode, mjScheduleFlag, desc, JSONObject.toJSON(modelList));
            int flowType = 1;
            String channelCode = "172";
            if (mjScheduleFlag == null || mjScheduleFlag == 0) {
                mjScheduleFlag = 1;
            }
            List<AnalysisModel> collect = modelList.stream().map(map -> {
                return JSONObject.parseObject(map, AnalysisModel.class);
            }).collect(Collectors.toList());
            FlowInstanceStandardDTO flowInstanceStandardDTO = new FlowInstanceStandardDTO();
            flowInstanceStandardDTO.setModelList(collect);
            flowInstanceStandardDTO.setFlowName(flowName);
            flowInstanceStandardDTO.setDesc(desc);
            flowInstanceStandardDTO.setFlowType(flowType);
            flowInstanceStandardDTO.setChannelCode(channelCode);
            flowInstanceStandardDTO.setMjScheduleFlag(mjScheduleFlag);
            flowInstanceStandardDTO.setMethodCode(interfaceCode);
            flowInstanceStandardDTO.setUpdatedBy("Agent");
            TBaseResponse<Long> longTBaseResponse = flowManagerThriftService.flowInstanceQuicklySave(flowInstanceStandardDTO);
            outPut = JSONObject.toJSONString(longTBaseResponse);
            t.setSuccessStatus();
            return outPut;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            log.error("save_new_flow_entity error,flowName:{},methodCode:{},mjScheduleFlag:{},desc:{},modelList:{}", flowName, interfaceCode, mjScheduleFlag, desc, JSONObject.toJSON(modelList), e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("save_new_flow_entity end,flowName:{},methodCode:{},mjScheduleFlag:{},desc:{},modelList:{},outPut:{}", flowName, interfaceCode, mjScheduleFlag, desc, JSONObject.toJSON(modelList), outPut);
            t.complete();
        }
    }

    @Tool(name = "add_new_flow_entity_version", description = "新增一个流程版本添加至系统，返回新增版本号，需要以下必填参数：\n" +
            "    1. flowId: 流程名称（如'商品数据采集流程'），int类型，必填\n" +
            "    2. mjScheduleFlag: 是否魔镜调度(0：否；1：是），int类型，必填\n" +
            "    3. desc: 流程版本名称（如'v1.0'），string类型，必填\n" +
            "    4. modelList: 流程交付模型列表，string类型，必填，包含如下字段的json数组的字符串,如[\"{\\\"moduleRuleId\\\":17,\\\"l3RuleId\\\":1,\\\"emptyDealFlag\\\":1,\\\"transferCrawlFlag\\\":1,\\\"detailRules\\\":[{\\\"analysisRuleIds\\\":\\\"1036\\\",\\\"mergeType\\\":0,\\\"transformRuleIds\\\":\\\"1029\\\"}]}\"]：\n" +
            "       - moduleRuleId: 交付模型规则ID，long类型，必填\n" +
            "       - l3RuleId: L3交付规则ID，long类型，必填\n" +
            "       - emptyDealFlag: 是否有空deal判断（0：否；1：是），int类型，必填\n" +
            "       - transferCrawlFlag: 是否有转采逻辑（0：否；1：是），int类型，必填\n" +
            "       - detailRules: 解析格式列表，必填，需包含：\n" +
            "         * analysisRuleIds: 解析规则列表（多个规则用','拼接），string类型，必填\n" +
            "         * mergeType: 合并类型（固定为0），int类型，必填\n" +
            "         * transformRuleIds: 加工规则列表（多个规则用','拼接），string类型，必填\n" +
            "       - extendParams: 扩展参数（按需填写），int类型，非必填")
    public String addNewFlowEntityVersion(Integer flowId, Integer mjScheduleFlag, String desc, List<String> modelList) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "add_new_flow_entity_version");
        String outPut = null;
        try {
            log.info("add_new_flow_entity_version,flowId:{},mjScheduleFlag:{},desc:{},modelList:{}", flowId, mjScheduleFlag, desc, JSONObject.toJSON(modelList));
            FlowAnalysisDTO flowAnalysisDTO = new FlowAnalysisDTO();
            flowAnalysisDTO.setFlowId(flowId.longValue());
            flowAnalysisDTO.setDesc(desc);
            if (mjScheduleFlag == null || mjScheduleFlag == 0) {
                mjScheduleFlag = 1;
            }
            flowAnalysisDTO.setMjScheduleFlag(mjScheduleFlag);
            List<AnalysisModel> collect = modelList.stream().map(map -> {
                return JSONObject.parseObject(map, AnalysisModel.class);
            }).collect(Collectors.toList());
            flowAnalysisDTO.setModelList(collect);
            flowAnalysisDTO.setUpdatedBy("Agent");
            TBaseResponse<Long> longTBaseResponse = flowManagerThriftService.addFlowAnalysisRule(flowAnalysisDTO);
            outPut = JSONObject.toJSONString(longTBaseResponse);
            t.setSuccessStatus();
            return outPut;
        } catch (Exception e) {
            t.setStatus(e.getMessage());
            
            log.error("add_new_flow_entity_version error,flowId:{},mjScheduleFlag:{},desc:{},modelList:{}", flowId, mjScheduleFlag, desc, JSONObject.toJSON(modelList), e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("add_new_flow_entity_version end,flowId:{},mjScheduleFlag:{},desc:{},modelList:{},outPut:{}", flowId, mjScheduleFlag, desc, JSONObject.toJSON(modelList), outPut);
            t.complete();
        }
    }

    @Tool(name = "save_rule_entity", description = "增一个加工规则写入系统，并返回需求transformRuleId\n" +
            "    1. ruleName: 自定义规则名称（如'价格转换规则'），String类型，必填\n" +
            "    2. interfaceCode: 接口，必填\n" +
            "    3. ruleDetails: 规则明细列表，List<str>类型，必填。包含如下字段的json数组的字符串， 如\"{\\\"fieldCode\\\":\\\"\\\",\\\"ruleDetail\\\":\\\"\\\"}\"：\n" +
            "       - fieldCode: 字段名（如'price'），String类型，必填\n" +
            "       - ruleDetail: groovy表达式（如' v==-1?'），String类型，必填\n" +
            "       ")
    public String saveRuleEntity(String ruleName, String interfaceCode, List<String> ruleDetails) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "save_rule_entity");
        String outPut = null;
        try {
            log.info("save_rule_entity,ruleName:{},interfaceCode:{},ruleDetails:{}", ruleName, interfaceCode, JSONObject.toJSON(ruleDetails));
            ParseRuleDTO parseRuleDTO = new ParseRuleDTO();
            parseRuleDTO.setName(ruleName);
            parseRuleDTO.setCode(interfaceCode);
            List<ParseRuleDetailDTO> collect = ruleDetails.stream().map(map -> {
                return JSONObject.parseObject(map, ParseRuleDetailDTO.class);
            }).collect(Collectors.toList());
            CreateRuleWithVersionDTO createRuleWithVersionDTO = new CreateRuleWithVersionDTO();
            createRuleWithVersionDTO.setRuleName(ruleName);
            createRuleWithVersionDTO.setInterfaceCode(interfaceCode);
            createRuleWithVersionDTO.setRuleDetail(collect);
            createRuleWithVersionDTO.setRuleType(2);
            createRuleWithVersionDTO.setPlatformCode("501");
            createRuleWithVersionDTO.setRuleCode("agent.v1.transform." + System.currentTimeMillis());
            TBaseResponse<Long> longTBaseResponse = parseRuleThriftService.quickAddRuleWithVersion(createRuleWithVersionDTO);
            outPut = JSONObject.toJSONString(longTBaseResponse);
            t.setSuccessStatus();
            return outPut;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            log.error("save_rule_entity error,ruleName:{},interfaceCode:{},ruleDetails:{}", ruleName, interfaceCode, JSONObject.toJSON(ruleDetails), e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("save_rule_entity end,ruleName:{},interfaceCode:{},ruleDetails:{},outPut:{}", ruleName, interfaceCode, JSONObject.toJSON(ruleDetails), outPut);
            t.complete();
        }
    }

    @Tool(name = "add_rule_entity_version", description = "新增一个加工规则版本添加至系统，需要以下必填参数：\n" +
            "    1. ruleId: 自定义规则名称（如'价格转换规则'），String类型，必填\n" +
            "    2. desc: 新增版本描述，，string类型，必填\n" +
            "    2. ruleDetails: 规则明细列表，List<str>类型，必填。包含如下字段的json数组的字符串， 如\"{\\\"fieldCode\\\":\\\"\\\",\\\"ruleDetail\\\":\\\"\\\"}\"：\n" +
            "       - fieldCode: 字段名（如'price'），String类型，必填\n" +
            "       - ruleDetail: groovy表达式（如' v==-1?'），String类型，必填\n" +
            "    3. filter_groovy: 过滤条件groovy表达式，String类型，必填，默认为''")
    public String addRuleEntityVersion(Integer ruleId, String desc, List<String> ruleDetails, String filter_groovy) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "add_rule_entity_version");
        String outPut = null;
        try {
            log.info("add_rule_entity_version,ruleId:{},ruleDetails:{},filter_groovy:{}", ruleId, JSONObject.toJSON(ruleDetails), filter_groovy);
            ParseRuleVersionRequest parseRuleVersionRequest = new ParseRuleVersionRequest();
            ParseRuleVersionDTO ruleVersionDTO = new ParseRuleVersionDTO();
            ruleVersionDTO.setRuleId(ruleId.longValue());
            ruleVersionDTO.setFilter(filter_groovy == null ? "" : filter_groovy);
            if (desc == null) {
                ruleVersionDTO.setDesc("wanglu67_agent");
            } else {
                ruleVersionDTO.setDesc(desc);
            }
            ruleVersionDTO.setOperatorMis("Agent");
            ruleVersionDTO.setType(2);
            List<ParseRuleDetailDTO> collect = ruleDetails.stream().map(map -> {
                return JSONObject.parseObject(map, ParseRuleDetailDTO.class);
            }).collect(Collectors.toList());
            ruleVersionDTO.setRuleVersionDetail(collect);
            parseRuleVersionRequest.setRuleVersionDTO(ruleVersionDTO);
            TBaseResponse<Long> longTBaseResponse = parseRuleVersionThriftService.addParseRuleVersionAndReturn(parseRuleVersionRequest);
            if (longTBaseResponse == null || longTBaseResponse.getCode()!=0){
                outPut = JSONObject.toJSONString(longTBaseResponse);
            }
            int code = longTBaseResponse.getCode();
            String key =null;
            if(code == 0){
                key ="成功";
            }else {
                key = "失败";
            }
            outPut = JSONObject.toJSONString(key) + ":" + JSONObject.toJSONString(longTBaseResponse.getMsg());
            t.setSuccessStatus();
            return outPut;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            log.error("add_rule_entity_version error,ruleId:{},ruleDetails:{},filter_groovy:{}", ruleId, JSONObject.toJSON(ruleDetails), filter_groovy, e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("add_rule_entity_version end,ruleId:{},ruleDetails:{},filter_groovy:{},outPut:{}", ruleId, JSONObject.toJSON(ruleDetails), filter_groovy, outPut);
            t.complete();
        }

    }

    @Tool(name = "rule_detail_valid", description = "验证加工规则。\n" +
            "    ruleDetails: 规则明细列表，List<str>类型，必填。包含如下字段的json数组的字符串， 如\"{\\\"fieldCode\\\":\\\"\\\",\\\"ruleDetail\\\":\\\"\\\"}\"：\n" +
            "       - fieldCode: 字段名（如'price'），String类型，必填\n" +
            "       - ruleDetail: groovy表达式（如' v==-1?'），String类型，必填")
    public String rule_detail_valid(List<String> ruleDetails) {
        Transaction t = Cat.newTransaction(MCP_SERVER_TOOL, "rule_detail_valid");
        String outPut = null;
        try {
            log.info("rule_detail_valid,ruleDetails:{}", JSONObject.toJSON(ruleDetails));
            List<Object> result = new ArrayList<>();
            for (String ruleDetail : ruleDetails) {
                Map map = JSONObject.parseObject(ruleDetail, Map.class);
                String fieldCode = map.get("fieldCode") == null ? "" : map.get("fieldCode").toString();
                String ruleDetailJson = map.get("ruleDetail") == null ? "" : map.get("ruleDetail").toString();
                ParseRuleValidRequestDTO parseRuleValidRequestDTO = new ParseRuleValidRequestDTO();
                parseRuleValidRequestDTO.setFieldName(fieldCode);
                parseRuleValidRequestDTO.setScriptCode(ruleDetailJson);
                ParseRuleValidResponseDTO parseRuleValidResponseDTO = parseRuleValidService.scriptRuleVerification(parseRuleValidRequestDTO);
                Integer code = parseRuleValidResponseDTO.getCode();
                String message = null;
                if(code == 0){
                    message= "成功";
                } else{
                    message=parseRuleValidResponseDTO.getMessage();
                }
                String data = parseRuleValidResponseDTO.getData();
                result.add(message + ":" + data);
            }
            outPut = JSONObject.toJSONString(result);
            t.setSuccessStatus();
            return outPut;
        } catch (Exception e) {
            
            t.setStatus(e.getMessage());
            log.error("rule_detail_valid error,ruleDetails:{}", JSONObject.toJSON(ruleDetails), e);
            return JSONObject.toJSONString(e);
        } finally {
            log.info("rule_detail_valid end,ruleDetails:{},outPut:{}", JSONObject.toJSON(ruleDetails), outPut);
            t.complete();
        }
    }

}
