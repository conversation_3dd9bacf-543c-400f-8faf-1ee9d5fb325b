package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.impl;

import com.dianping.cat.Cat;
import com.google.gson.Gson;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.BusinessService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.BusinessLineDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 业务线服务实现类
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Slf4j
@Service
public class BusinessServiceImpl implements BusinessService {

    @Resource
    private BusinessLineDao businessLineDao;

    @Resource
    private McpServerService mcpServerService;

    private static final Gson gson = new Gson();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public McpServerResponse createBusinessLine(BusinessLine businessLine) {
        try {
            log.info("创建业务线, businessLine={}", businessLine);

            if (businessLine == null) {
                return McpServerResponse.fail("业务线信息不能为空");
            }

            if (businessLine.getBusinessLine() == null || businessLine.getBusinessLine().trim().isEmpty()) {
                return McpServerResponse.fail("业务线名称不能为空");
            }

            // 检查业务线名称是否已存在
            List<BusinessLine> existingLines = businessLineDao.getBussinessByName(businessLine.getBusinessLine().trim());
            if (existingLines != null && !existingLines.isEmpty()) {
                return McpServerResponse.fail("业务线名称已存在");
            }

            BusinessLine businessLineDateBase = businessLineDao.insertBusinessLine(businessLine);
            if (businessLineDateBase == null) {
                return McpServerResponse.fail("创建业务线失败");
            }

            log.info("创建业务线成功, id={}, name={}", businessLineDateBase.getId(), businessLineDateBase.getBusinessLine());
            return McpServerResponse.success("创建业务线成功", gson.toJson(businessLineDateBase));
        } catch (Exception e) {
            log.error("创建业务线失败", e);
            
            return McpServerResponse.fail("创建业务线失败: " + e.getMessage());
        }
    }

    @Override
    public McpServerResponse getAllBusinessLine() {
        try {
            log.info("获取所有业务线");

            List<BusinessLine> allBusinessLine = businessLineDao.getAllBusinessLine();
            if (allBusinessLine == null) {
                return McpServerResponse.fail("获取业务线列表失败");
            }

            log.info("获取所有业务线成功, 共{}个", allBusinessLine.size());
            return McpServerResponse.success("获取业务线列表成功", gson.toJson(allBusinessLine));
        } catch (Exception e) {
            log.error("获取所有业务线失败", e);
            
            return McpServerResponse.fail("获取业务线列表失败: " + e.getMessage());
        }
    }

    @Override
    public McpServerResponse getBusinessLineById(Long businessLineId) {
        try {
            log.info("根据ID获取业务线, businessLineId={}", businessLineId);

            if (businessLineId == null) {
                return McpServerResponse.fail("业务线ID不能为空");
            }

            BusinessLine businessLine = businessLineDao.getBusinessLineById(businessLineId);
            if (businessLine == null) {
                return McpServerResponse.fail("业务线不存在或已被删除");
            }

            log.info("根据ID获取业务线成功, businessLineId={}", businessLineId);
            return McpServerResponse.success("获取业务线成功", gson.toJson(businessLine));
        } catch (Exception e) {
            log.error("根据ID获取业务线失败, businessLineId={}", businessLineId, e);
            return McpServerResponse.fail("获取业务线失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public McpServerResponse updateBusinessLine(BusinessLine businessLine) {
        try {
            log.info("更新业务线, businessLine={}", businessLine);

            if (businessLine == null) {
                return McpServerResponse.fail("业务线信息不能为空");
            }

            Long id = businessLine.getId();
            if (id == null) {
                return McpServerResponse.fail("业务线ID不能为空");
            }

            // 检查业务线是否存在
            BusinessLine existingLine = businessLineDao.getBusinessLineById(id);
            if (existingLine == null) {
                return McpServerResponse.fail("业务线不存在或已被删除");
            }
            BusinessLine newBusinessLine = businessLineDao.updateBusinessLine(businessLine);
            if (newBusinessLine == null) {
                return McpServerResponse.fail("更新业务线失败");
            }

            log.info("更新业务线成功, id={}", newBusinessLine.getId());
            return McpServerResponse.success("更新业务线成功", gson.toJson(newBusinessLine));
        } catch (Exception e) {
            log.error("更新业务线失败, businessLine={}", businessLine, e);
            
            return McpServerResponse.fail("更新业务线失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public McpServerResponse deleteBusinessLine(Long businessLineId) {
        try {
            log.info("删除业务线, businessLineId={}", businessLineId);

            if (businessLineId == null) {
                return McpServerResponse.fail("业务线ID不能为空");
            }

            // 检查业务线是否存在
            BusinessLine businessLine = businessLineDao.getBusinessLineById(businessLineId);
            if (businessLine == null) {
                return McpServerResponse.fail("业务线不存在或已被删除");
            }

            // 1. 删除业务线下的所有McpServer和tool（级联删除关联的Tool）
            int mcpServerCount = mcpServerService.deleteAllMcpServersByBusinessLineId(businessLineId);

            // 2. 删除业务线
            boolean deleted = businessLineDao.deleteBusinessLine(businessLine);
            if (!deleted) {
                return McpServerResponse.fail("删除业务线失败");
            }

            log.info("删除业务线成功, businessLineId={}", businessLineId);
            return McpServerResponse.success("删除业务线成功，同时删除了" + mcpServerCount + "个McpServer");
        } catch (Exception e) {
            log.error("删除业务线失败, businessLineId={}", businessLineId, e);
            
            return McpServerResponse.fail("删除业务线失败: " + e.getMessage());
        }
    }

    @Override
    public McpServerResponse checkBusinessLineNameIsExist(String businessLineName) {
        try {
            log.info("检查业务线名称是否存在, businessLineName={}", businessLineName);

            if (businessLineName == null || businessLineName.trim().isEmpty()) {
                return McpServerResponse.fail("业务线名称不能为空");
            }

            List<BusinessLine> bussinessByName = businessLineDao.getBussinessByName(businessLineName.trim());
            boolean exists = bussinessByName != null && !bussinessByName.isEmpty();

            log.info("检查业务线名称是否存在结果: {}, businessLineName={}", exists, businessLineName);
            if (exists) {
                return McpServerResponse.success("业务线名称已存在", gson.toJson(bussinessByName));
            } else {
                return McpServerResponse.success("业务线名称不存在", null);
            }
        } catch (Exception e) {
            log.error("检查业务线名称是否存在失败, businessLineName={}", businessLineName, e);
            
            return McpServerResponse.fail("检查业务线名称是否存在失败: " + e.getMessage());
        }
    }

    @Override
    public McpServerResponse getBusinessLineByBusiness(String businessLineName) {
        try {
            log.info("根据名称获取业务线, businessLineName={}", businessLineName);

            if (businessLineName == null || businessLineName.trim().isEmpty()) {
                return McpServerResponse.fail("业务线名称不能为空");
            }

            List<BusinessLine> bussinessByName = businessLineDao.getBussinessByName(businessLineName.trim());
            if (bussinessByName == null || bussinessByName.isEmpty()) {
                return McpServerResponse.fail("业务线不存在");
            }

            log.info("根据名称获取业务线成功, businessLineName={}", businessLineName);
            return McpServerResponse.success("获取业务线成功", gson.toJson(bussinessByName.get(0)));
        } catch (Exception e) {
            log.error("根据名称获取业务线失败, businessLineName={}", businessLineName, e);
            
            return McpServerResponse.fail("获取业务线失败: " + e.getMessage());
        }
    }
}
