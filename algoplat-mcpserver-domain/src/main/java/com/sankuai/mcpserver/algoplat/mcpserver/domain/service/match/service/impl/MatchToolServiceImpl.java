package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.service.TAgentChatService;
import com.sankuai.algoplatform.matchops.api.request.mcptool.TMcpTestMissionRequest;
import com.sankuai.algoplatform.matchops.api.response.mcptool.TMcpTestMissionResponse;
import com.sankuai.algoplatform.matchops.api.service.TMcpTestMissionService;
import com.sankuai.guiguzi.annotation.thrift.AlgorithmAnnotationTService;
import com.sankuai.guiguzi.annotation.thrift.dto.annotation.AlgorithmAnnotationMatchDataDTO;
import com.sankuai.guiguzi.annotation.thrift.dto.annotation.AlgorithmAnnotationMatchDataResultDTO;
import com.sankuai.guiguzi.annotation.thrift.nosso.*;
import com.sankuai.guiguzi.annotation.thrift.request.annotation.QueryAlgorithmAnnotationMatchDataRequest;
import com.sankuai.guiguzi.annotation.thrift.response.BaseTResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.McpMissionCommonContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.SubMissionState;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DxService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.AnnotationLabelingToolRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.FridayService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.MatchToolService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpMissionDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.service.impl.S3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.mortbay.log.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;

import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants.AGENT_MIS_ID;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/11
 */
@Slf4j
@Service
public class MatchToolServiceImpl implements MatchToolService {

    @Autowired
    private FridayService fridayService;

    @Autowired
    private McpMissionDao mcpMissionDao;

    @Autowired
    private DxService dxService;

    @Autowired
    private S3Service s3Service;

    @Resource
    private TMcpTestMissionService tMcpTestMissionService;

    @Resource
    private AlgorithmAnnotationTService algorithmAnnotationTService;

    @Override
    public String searchAnnotationDataUsingLabelingTool(String dataSetName, AnnotationLabelingToolRequest annotationLabelingToolRequest) throws Exception {
        try {
            // 创建临时文件用于存储jsonl格式数据
            File tempFile = File.createTempFile(dataSetName, ".txt");
            String localFilePath = "/tmp/" + dataSetName + "_debug.txt";
            File localFile = new File(localFilePath);

            QueryAlgorithmAnnotationMatchDataRequest request = convertToQueryAlgorithmAnnotationMatchDataRequest(annotationLabelingToolRequest, dataSetName);
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile))) {
                // 获取第一页数据，以获取总页数和扩展信息
                BaseTResponse<AlgorithmAnnotationMatchDataResultDTO> firstResponse = algorithmAnnotationTService.queryAlgorithmAnnotationMatchData(request);
                if (firstResponse == null || firstResponse.getCode() != 200 || firstResponse.getData() == null) {
                    throw new Exception("获取标注数据失败，返回结果为空");
                }

                // 获取总页数
                AlgorithmAnnotationMatchDataResultDTO data = firstResponse.getData();
                Integer total = data.getTotalPage();
                Integer pageSize = request.getPageSize(); // 修正：使用pageSize而不是page
                if (total == null || total <= 0) {
                    throw new Exception("获取标注数据失败，总数据量异常");
                }

                // 直接使用返回的总页数，不再重新计算
                int totalPage = total;
                log.info("开始获取标注数据，总页数：{}，每页大小：{}", totalPage, pageSize);

                // 写入扩展信息作为第一行
                // 从第一条数据中获取扩展信息
                String extensionInfo = data.getExtensionInfo();

                Map<String, Object> extensionMap = new HashMap<>();
                extensionMap.put("extensionInfo", extensionInfo);
                writer.write(JSONObject.toJSONString(extensionMap));
                writer.newLine();

                List<Map<String, String>> infos = new ArrayList<>();

                // 处理第一页数据并添加到infos列表
                List<AlgorithmAnnotationMatchDataDTO> annotationItems = data.getAnnotationItems();
                log.info("第1页数据获取结果：{}", annotationItems != null ? annotationItems.size() : 0);
                if (annotationItems != null && !annotationItems.isEmpty()) {
                    for (AlgorithmAnnotationMatchDataDTO item : annotationItems) {
                        Map<String, String> jsonObject = new HashMap<>();
                        jsonObject.put("preAnnotationData", item.getPreAnnotationData());
                        jsonObject.put("afterAnnotationData", item.getAfterAnnotationData());
                        infos.add(jsonObject);
                    }
                }

                // 循环获取剩余页的数据
                for (int pageNum = 2; pageNum <= totalPage; pageNum++) {
                    request.setPage(pageNum);
                    BaseTResponse<AlgorithmAnnotationMatchDataResultDTO> response = algorithmAnnotationTService.queryAlgorithmAnnotationMatchData(request);
                    List<AlgorithmAnnotationMatchDataDTO> pageItems = response != null && response.getData() != null ?
                            response.getData().getAnnotationItems() : null;
                    log.info("第{}页数据获取结果：{}", pageNum, pageItems != null ? pageItems.size() : 0);
                    if (response != null && response.getData() != null && !CollectionUtils.isEmpty(pageItems)) {
                        for (AlgorithmAnnotationMatchDataDTO item : pageItems) {
                            Map<String, String> jsonObject = new HashMap<>();
                            jsonObject.put("preAnnotationData", item.getPreAnnotationData());
                            jsonObject.put("afterAnnotationData", item.getAfterAnnotationData());
                            infos.add(jsonObject);
                        }
                    }
                    log.info("已处理页数：{}/{}", pageNum, totalPage);
                }

                // 写入所有数据
                for (Map<String, String> info : infos) {
                    writer.write(JSONObject.toJSONString(info));
                    writer.newLine();
                }

                log.info("获取标注数据完成，共处理{}条记录", infos.size());
                // 确保文件内容已写入磁盘
                writer.flush();
                log.info("临时文件大小：{} 字节", tempFile.length());
                String s3dataSetName = dataSetName + ".txt";
                String url = s3Service.uploadFileToS3AndGetFileUrl(tempFile, s3dataSetName, null);
                log.info("文件上传成功，URL：{}", url);
                return url;
            } catch (IOException e) {
                log.error("写入标注数据到临时文件失败", e);
                
                throw new Exception("写入标注数据到临时文件失败: " + e.getMessage());
            } finally {
                // 删除临时文件
                if (!tempFile.delete()) {
                    log.warn("临时文件删除失败: {}", tempFile.getAbsolutePath());
                } else {
                    log.info("删除文件成功！{}", tempFile.getAbsolutePath());
                }
            }
        } catch (Exception e) {
            log.error("处理标注数据失败", e);
            
            throw e;
        }
    }

    @Override
    public McpMissionCommonContext executeMcpLoadTestTask(String tMcpTestMissionRequestJson, String missionId) throws Exception {

        SubMissionState subMissionState = new SubMissionState();
        McpMissionCommonContext mcpMissionCommonContext = new McpMissionCommonContext();
        try {
            if (StringUtils.isBlank(tMcpTestMissionRequestJson)) {
                throw new IllegalArgumentException("请求参数为空");
            }
            TMcpTestMissionRequest tMcpTestMissionRequest = JSONObject.parseObject(tMcpTestMissionRequestJson, TMcpTestMissionRequest.class);
            if (tMcpTestMissionRequest == null) {
                throw new IllegalArgumentException("tMcpTestMissionRequest参数为空");
            }
            Map<String, String> extra = new HashMap<>();
            extra.put("missionId", missionId);
            extra.put("sessionId", tMcpTestMissionRequest.getSessionId());
            tMcpTestMissionRequest.getExtraInfo().putAll(extra);
            subMissionState.setStatus(0);
            subMissionState.setSubMissionName("模型压测任务");
            subMissionState.setMessage("任务执行中");
            subMissionState.setExtraInfo(tMcpTestMissionRequest.getExtraInfo());

            mcpMissionCommonContext.setOriginRequest(tMcpTestMissionRequestJson);
            mcpMissionCommonContext.setSubMissionStates(Lists.newArrayList(subMissionState));
            mcpMissionCommonContext.setStatus(0);
            mcpMissionCommonContext.setExtendInfo(tMcpTestMissionRequest.getExtraInfo());
            mcpMissionCommonContext.setMissionId(missionId);
            mcpMissionCommonContext.setExecuteMessage("任务执行中");
            mcpMissionCommonContext.setSessionId(tMcpTestMissionRequest.getSessionId());
            mcpMissionCommonContext.setMisId(tMcpTestMissionRequest.getMisId());


            TMcpTestMissionResponse tMcpTestMissionResponse = tMcpTestMissionService.submitMcpLoadTestTask(tMcpTestMissionRequest);
            if (tMcpTestMissionResponse.getCode() != 0) {
                throw new Exception("任务执行失败,异常提示:" + tMcpTestMissionResponse.getMessage());
            }
            Map<String, String> data = tMcpTestMissionResponse.getData();
            if (MapUtils.isEmpty(data) || data.get("taskId") == null) {
                throw new Exception("任务执行失败,异常提示:返回结果异常");
            }
            Long taskId = Long.valueOf(data.get("taskId"));
            Map<String, String> extraInfo = tMcpTestMissionResponse.getExtra();
            if (extraInfo != null && extraInfo.get("sessionId") != null) {
                String sessionId = extraInfo.get("sessionId");
                subMissionState.setSubSessionId(sessionId);
            }
            subMissionState.setSubMissionId(taskId);

            return mcpMissionCommonContext;
        } catch (Exception e) {
            log.error("执行模型压测任务失败, missionId:{}, 错误信息:{}", missionId, e.getMessage(), e);
            

            subMissionState.setStatus(-1);
            subMissionState.setEndTime(new Date());
            subMissionState.setMessage(e.getMessage());

            // 确保subMissionState被添加到mcpMissionCommonContext中
            if (mcpMissionCommonContext.getSubMissionStates() == null) {
                mcpMissionCommonContext.setSubMissionStates(Lists.newArrayList(subMissionState));
            } else if (!mcpMissionCommonContext.getSubMissionStates().contains(subMissionState)) {
                mcpMissionCommonContext.getSubMissionStates().add(subMissionState);
            }

            mcpMissionCommonContext.setStatus(-1);
            mcpMissionCommonContext.setExecuteMessage(e.getMessage());
            return mcpMissionCommonContext;
        }
    }

    /**
     * 将AnnotationLabelingToolRequest转换为QueryAlgorithmAnnotationMatchDataRequest
     */
    private QueryAlgorithmAnnotationMatchDataRequest convertToQueryAlgorithmAnnotationMatchDataRequest(AnnotationLabelingToolRequest request, String name) {
        QueryAlgorithmAnnotationMatchDataRequest queryRequest = new QueryAlgorithmAnnotationMatchDataRequest();
        queryRequest.setBizLine(request.getBizLine());
        queryRequest.setMatchType(request.getMatchType());
        queryRequest.setAnnotationType(request.getAnnotationType());
        queryRequest.setSceneName(request.getSceneName());
        queryRequest.setExtensionInfo(JSONObject.toJSONString(ImmutableMap.of("dataSetName", name)));
        // 设置默认分页参数
        queryRequest.setPage(1);
        queryRequest.setPageSize(1000);
        return queryRequest;
    }

    private void updateMisssionStatus(ExecuteFridayMissionContext context) {
        if (context.getTrainingTaskStatus() == 1
                && context.getModelDeployStatus() == 1
                && context.getModelRegStatus() == 1
                && context.getUploadDatasetStatus() == 1
                && context.getModelTestStatus() == 1) {
            context.setStatus(1);
            context.setExecuteMessage("Friday模型训练全流程执行成功,misionId:" + context.getMissionId());
        } else {
            context.setStatus(-1);
            context.setExecuteMessage("Friday模型训练全流程执行状态异常,misionId:" + context.getMissionId());
        }
    }
}
