package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.DXMessageRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.DxPusherConfig;
import com.sankuai.xm.pubapi.thrift.GroupPushMessageWithUids;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static com.dianping.squirrel.asyncdeps.io.lettuce.core.pubsub.PubSubOutput.Type.message;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/20
 */
@Slf4j
@Service
public class DxService {


    @Resource
    private PushMessageServiceI.Iface pushMessageService;

    @Resource
    private UserInfoService userInfoService;

    public static final String MESSAGE_TYPE = "text";

    public static final String EXTENSION = null;

    @Autowired
    private DxPusherConfig.PusherInfoFactory pusherInfoFactory;

    @Resource
    private DxPusherConfig dxPusherConfig;


    /**
     * 推送消息给多个用户
     *
     * @param misIds  用户mis列表
     * @param message 推送消息
     * @return 是否推送成功
     */

    public boolean sendMsg2Users(String message, List<String> misIds, PusherInfo pusherInfo) {
        log.info("DxPusherService.sendMsgByMisIds message:{}, misIds:{}", message, misIds);
        try {
            Map<String, Long> uidByMisId = userInfoService.getUidByMisId(new HashSet<>(misIds));
            if (MapUtils.isEmpty(uidByMisId) || CollectionUtils.isEmpty(uidByMisId.values())) {
                return false;
            }
            List<Long> toUids = new ArrayList<>(uidByMisId.values());
            if (pusherInfo == null) {
                pusherInfo = dxPusherConfig.createDefaultPusherInfo();
            }
            String resp = pushMessageService.pushExtensionMessageWithUids(System.currentTimeMillis(),
                    MESSAGE_TYPE, buildJsonMessage(message), EXTENSION, toUids, pusherInfo);
            log.info("DxPusherService.sendMsgByMisIds resp:{}", JSON.toJSONString(resp));
            return handlePushResult(resp);
        } catch (TException e) {
            log.error("DxPusherService.sendMsgByMisIds err. message:{}, misIds:{}", message, JSON.toJSONString(misIds), e);
            return false;
        }
    }

    public boolean sendMsg2Users(DXMessageRequest request) {
        List<String> userList = request.getUserList();
        String message = request.getMessage();
        String appKey = request.getAppKey();
        Long fromUid = request.getFromUid();
        String token = request.getToken();
        if (CollectionUtils.isEmpty(userList)) {
            return false;
        }
        PusherInfo pusherInfo = null;
        if (StringUtils.isEmpty(appKey) || fromUid == null || StringUtils.isEmpty(token)) {
            pusherInfo = dxPusherConfig.createDefaultPusherInfo();
        } else {
            pusherInfo = pusherInfoFactory.create(appKey, fromUid,token);
        }
        return sendMsg2Users(message, userList, pusherInfo);
    }


    /**
     * 推送消息给群组
     *
     * @param groupId 群组ID
     * @param message 推送消息
     * @return 是否推送成功
     */
    public boolean sendMsg2Group(String appKey, Long fromUid, String message, Long groupId,String token) {
        try {
            log.info("DxPusherService.sendMsgByRoomId message:{}，groupId:{}", message, groupId);
            PusherInfo pusherInfo = null;
            if (StringUtils.isEmpty(appKey) || fromUid == null || StringUtils.isEmpty(token)) {
                pusherInfo = dxPusherConfig.createDefaultPusherInfo();
            } else {
                pusherInfo = pusherInfoFactory.create(appKey, fromUid,token);
            }
            GroupPushMessageWithUids param = new GroupPushMessageWithUids();
            param.setCts(System.currentTimeMillis());
            param.setMessageType(MESSAGE_TYPE);
            param.setMessageBodyJson(buildJsonMessage(message));
            param.setGid(groupId);
            param.setPusherInfo(pusherInfo);
            String resp = pushMessageService.pushToRoomWithUids(param);
            Map map = JSON.parseObject(resp, Map.class);
            if (map.get("rescode") != null && Integer.valueOf(map.get("rescode").toString()) == 1000) {
                Cat.logError(new Exception("大象群组消息推送失败" + map.get("data").toString()));
                return false;
            }
            log.info("DxPusherService.sendMsgByRoomId resp:{}", JSON.toJSONString(resp));
            return handlePushResult(resp);
        } catch (TException e) {
            log.error("DxPusherService.sendMsgByRoomId err. message:{}, groupId:{}", message, groupId, e);
        }
        return false;
    }

    public boolean sendMsgToRoomWithUids(GroupPushMessageWithUids param) {

        try {
            log.info("pushMessageService.pushToRoomWithUids param: {}", JSON.toJSONString(param));
            String resp = pushMessageService.pushToRoomWithUids(param);
            log.info("pushMessageService.pushToRoomWithUids resp: {}", resp);
            return handlePushResult(resp);
        } catch (TException e) {
            log.error("DxPusherService.sendMsgToRoomWithUids err. param={}", JSON.toJSONString(param), e);
            return false;
        }
    }

    private String buildJsonMessage(String message) {
        JSONObject msgJson = new JSONObject();
        msgJson.put(MESSAGE_TYPE, message);
        return msgJson.toString();
    }

    private boolean handlePushResult(String resp) {
        JSONObject jsonObject = JSON.parseObject(resp);
        int resCode = jsonObject.getInteger("rescode");
        JSONObject data = jsonObject.getJSONObject("data");

        if (resCode == 0 && data != null &&
                (!CollectionUtils.isEmpty(data.getJSONArray("mids"))
                        || data.getLong("mid") != null && data.getLong("mid") > 0)) {
            return true;
        }
        return false;
    }

}
