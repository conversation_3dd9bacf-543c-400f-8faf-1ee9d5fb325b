package com.sankuai.mcpserver.algoplat.mcpserver.domain.schedules;

import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.ChangeEventService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.TestTaskScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/29
 */

@Slf4j
@CraneConfiguration
public class scheduleJob {
    @Resource
    private TestTaskScheduleService testTaskScheduleService;

    @Resource
    private ChangeEventService changeEventService;

    @Crane("TestTool_TaskSchedule")
    public void testTaskSchedule() {
        testTaskScheduleService.testTaskSchedule();
    }

    @Crane("ScheduleJobTool_executorAsynJob")
    public void executorAsynJob() {
        testTaskScheduleService.executorAsynJob();

        testTaskScheduleService.executorAsyncAgentJob();
    }

    @Crane("Friday_TaskSchedule")
    public void fridayTaskSchedule() {
        testTaskScheduleService.fridayTaskSchedule();
    }

    @Crane("Mcp_Change_Sync_Job")
    public void updateMcpBeanForChangeEvent() {
        changeEventService.updateMcpBeanForChangeEvent();
    }


}
