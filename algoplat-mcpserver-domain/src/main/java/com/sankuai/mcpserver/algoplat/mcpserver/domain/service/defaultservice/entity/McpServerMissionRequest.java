package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class McpServerMissionRequest {

    @ToolParam(description = "任务类型")
    private Integer missionType;

    @ToolParam(description = "任务请求")
    private String missionRequest;

    @ToolParam(description = "任务ID")
    private String missionId;


}
