package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants.AGENT_MIS_ID;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.util.StringUtils;
import com.dianping.lion.client.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.response.chat.TChatResponse;
import com.sankuai.algoplatform.agentapi.client.response.session.TSessionResponse;
import com.sankuai.algoplatform.agentapi.client.service.TAgentChatService;
import com.sankuai.algoplatform.agentapi.client.service.TAgentSessionService;
import com.sankuai.algoplatform.matchops.api.request.mcptool.TestSubTaskContext;
import com.sankuai.algoplatform.matchops.api.response.mcptool.TMcpTestMissionResponse;
import com.sankuai.algoplatform.matchops.api.service.TMcpTestMissionService;
import com.sankuai.inf.kms.pangolin.api.cat.Cat;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.McpMissionCommonContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.SubMissionState;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.matchtesttool.ExecuteTestToolMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.JobExecutorService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.FridayService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.impl.GeneralProcessingService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpMissionDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.AsynJobExecutorStatusEnum;

import java.lang.reflect.Type;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Resource;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.MissionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/29
 */
@Service
@Slf4j
public class TestTaskScheduleService {

    @Resource
    private TMcpTestMissionService tMcpTestMissionService;

    @Resource
    private McpMissionDao mcpserverMissionDao;

    @Resource
    private DxService dxService;

    @Resource
    private TAgentChatService tAgentChatService;

    @Resource
    private TAgentSessionService tAgentSessionService;

    @Resource(name = "asynJobExecutorService")
    private JobExecutorService jobExecutorService;

    @Resource
    private FridayService fridayService;

    @Resource
    private GeneralProcessingService generalProcessingService;

    private final static Integer FRIDAY_TOOL_MISSION_TYPE = 1;
    private final static Integer TEST_TOOL_MISSION_TYPE = 2;
    private final static Integer COMPOSITE_MISSION_TYPE = 3;
    private final static List<Integer> MISSION_TYPES = Lists.newArrayList(TEST_TOOL_MISSION_TYPE, COMPOSITE_MISSION_TYPE);
    private final static List<Integer> PENDING_STATUSES = Lists.newArrayList(0, -2);
    private final static List<Integer> FAILURE_STATUSES = Lists.newArrayList(3, -1, 98, 99);
    private final static Integer SUB_SUCCESS_STATUS = 2;
    private final static Integer SUB_INIT_STATUS = 0;
    private final static Integer SUB_RUNNING_STATUS = 1;
    private final static Integer SUB_UNKNOWN_STATUS = -1;
    private final static Integer MISSION_SUCCESS = 1;
    private final static Integer MISSION_FAILURE = -1;

    public void testTaskSchedule() {
        // 查询类型为2和3的待处理任务
        List<McpMissionInfo> pendingMissions = new ArrayList<>();
        for (Integer missionType : MISSION_TYPES) {
            List<McpMissionInfo> missionInfos = mcpserverMissionDao.getMcpMissionInfoByStatusAndType(missionType, PENDING_STATUSES);
            if (CollectionUtils.isEmpty(missionInfos)) {
                continue;
            }
            pendingMissions.addAll(missionInfos);
        }
        for (McpMissionInfo missionInfo : pendingMissions) {
            try {
                Integer missionType = missionInfo.getType();
                if (missionType == TEST_TOOL_MISSION_TYPE) {
                    processTestToolMission(missionInfo);
                } else if (missionType == COMPOSITE_MISSION_TYPE) {
                    processCompositeMission(missionInfo);
                }
            } catch (Exception e) {
                log.error("处理任务失败, missionId: {}, error: {}", missionInfo.getMissionId(), e.getMessage(), e);

            }
        }
    }

    public void fridayTaskSchedule() {
        List<McpMissionInfo> missionInfos = mcpserverMissionDao.getMcpMissionInfoByStatusAndType(FRIDAY_TOOL_MISSION_TYPE, PENDING_STATUSES);
        for (McpMissionInfo missionInfo : missionInfos) {
            try {
                processFridayMission(missionInfo);
            } catch (Exception e) {
                log.error("处理Friday任务失败, missionId: {}, error: {}", missionInfo.getMissionId(), e.getMessage(), e);

            }
        }
    }

    private void processFridayMission(McpMissionInfo mcpMissionInfoByMission) {
        try {
            String missionContext = mcpMissionInfoByMission.getMissionContext();
            ExecuteFridayMissionContext context = JSONObject.parseObject(missionContext, ExecuteFridayMissionContext.class);
            ExecuteFridayMissionRequest fridayRequest = context.getRequest();
            if (Objects.isNull(context) || StringUtils.isBlank(context.getSessionId())) {
                return;
            }
            String sessionId = context.getSessionId();
            Integer status = mcpMissionInfoByMission.getStatus();
            try {

                if (status == MISSION_SUCCESS) {
                    String missionResult = mcpMissionInfoByMission.getMissionResult();
                    dxService.sendMsg2Users("Friday任务执行成功!missionId为:" + mcpMissionInfoByMission.getMissionId() + "结果为:" + missionResult, Lists.newArrayList(mcpMissionInfoByMission.getMisId(), "zhouzehao02"), null);
                    reportResultToAgent(sessionId);
                } else if (status == MISSION_FAILURE) {
                    dxService.sendMsg2Users("Friday任务执行失败!missionId为:" + mcpMissionInfoByMission.getMissionId() + "失败信息为:" + context.getExecuteMessage(), Lists.newArrayList(mcpMissionInfoByMission.getMisId(), "zhouzehao02"), null);
                    reportResultToAgent(sessionId);
                } else if (0 == mcpMissionInfoByMission.getStatus() && checkIfOverTenDays(mcpMissionInfoByMission)) {
                    context.setStatus(-1);
                    context.setExecuteMessage("相关任务执行超时,任务执行超时!");
                    updateMissionStatus(mcpMissionInfoByMission, context, String.format("相关任务执行超时,任务%s执行超时!", context.getMissionId()), false, null);
                } else if (status == 0 && (context.getStatus() == 0 || context.getStatus() == -2)) {
                    mcpserverMissionDao.updateMissionInfo(mcpMissionInfoByMission);
                    //执行数据集上传任务（或检查状态）
                    fridayService.uploadModelTraningMissionDataset(fridayRequest, context, mcpMissionInfoByMission);

                    //执行模型训练
                    if (context.getUploadDatasetStatus() != 1) {
                        return;
                    }
                    fridayService.createTrainingTask(context, fridayRequest, mcpMissionInfoByMission);

                    //模型注册
                    if (context != null && context.getTrainingTaskStatus() != 1) {
                        return;
                    }
                    fridayService.executeModelRegistrationProcess(fridayRequest, context, mcpMissionInfoByMission);

                    //模型部署
                    if (context.getModelRegStatus() != 1) {
                        return;
                    }
                    fridayService.executeModelDeploymentTask(fridayRequest, context, mcpMissionInfoByMission);

                    //模型测试
                    if (context.getModelDeployStatus() != 1) {
                        return;
                    }
                    fridayService.executeModelTestProcess(context, mcpMissionInfoByMission);
                }
            } catch (Exception e) {
                log.error("任务执行失败，missionId:{},请求:{},context:{}", context.getMissionId(), JSONObject.toJSONString(fridayRequest), JSONObject.toJSONString(context), e);
                generalProcessingService.generalProcess(mcpMissionInfoByMission, context, null, context.getSessionId(), false);
                McpMissionInfo mcpMissionInfoByMissionByMissionId = mcpserverMissionDao.getMcpMissionInfoByMissionId(context.getMissionId());
                ExecuteFridayMissionContext executeFridayMissionContext = new ExecuteFridayMissionContext();
                if (mcpMissionInfoByMissionByMissionId != null) {
                    executeFridayMissionContext = JSONObject.parseObject(mcpMissionInfoByMissionByMissionId.getMissionContext(), ExecuteFridayMissionContext.class);
                }
                executeFridayMissionContext.setStatus(-1);
                log.error("executeFridayModelFactoryProcess error,request:{}", executeFridayMissionContext, e);

                if (mcpMissionInfoByMissionByMissionId == null) {
                    log.error("未找到mission信息,missionID:{},request:{}", context.getSessionId(), JSONObject.toJSONString(executeFridayMissionContext));
                }
                McpMissionInfo mcpMissionInfoByMissionByMissionId1 = buildFridayMissionInfo(executeFridayMissionContext, executeFridayMissionContext.getRequest(), mcpMissionInfoByMissionByMissionId);
                mcpserverMissionDao.updateMissionInfo(mcpMissionInfoByMissionByMissionId1);
            }
        } catch (Exception e) {
            log.error("Friday任务执行失败,missionId:{}", mcpMissionInfoByMission.getMissionId(), e);

        }
    }

    public void executorAsynJob() {
        List<McpMissionInfo> missionInfos = mcpserverMissionDao.getMcpMissionInfoByStatusAndType(4, Arrays.asList(1));
        if (CollectionUtils.isEmpty(missionInfos)) {
            return;
        }

        for (McpMissionInfo missionInfo : missionInfos) {
            processAsynInvokeInterfaceMission(missionInfo);
        }
    }

    private void processAsynInvokeInterfaceMission(McpMissionInfo missionInfo) {
        if (Objects.isNull(missionInfo)) {
            return;
        }

        if (jobExecutorService.isTaskFinished(missionInfo.getMissionId())) {
            String sessionId = MapUtils.getString(JSON.parseObject(missionInfo.getMissionContext(), new TypeReference<>() {
            }), "sessionId");
            reportResultToAgent(sessionId);
            jobExecutorService.withPostProcessor(missionInfo, AsynJobExecutorStatusEnum.PULLED, null);
            log.info("reportResultToAgent.sessionId: {}", sessionId);
        }
    }

    /**
     * 自动唤醒Agent长任务唤醒
     */
    public void executorAsyncAgentJob() {
        List<McpMissionInfo> missionInfoList = mcpserverMissionDao.getMcpMissionInfoByStatusAndType(MissionTypeEnum.AUTO_AGENT.getCode(), Collections.singletonList(AsynJobExecutorStatusEnum.RUNNING.getStatus()));
        if (CollectionUtils.isEmpty(missionInfoList)) {
            return;
        }

        for (McpMissionInfo missionInfo : missionInfoList) {
            try {
                processAsyncAgentInvokeInterfaceMission(missionInfo);
            } catch (Exception e) {
                log.error("自动唤醒Agent长任务唤醒异常，executorAsyncAgentJob error，missionInfo:{}", JSON.toJSONString(missionInfo), e);
            }
        }
    }

    private void processAsyncAgentInvokeInterfaceMission(McpMissionInfo missionInfo) {
        if (Objects.isNull(missionInfo)) {
            return;
        }

        String missionContext = missionInfo.getMissionContext();
        if (StringUtils.isBlank(missionContext)) {
            return;
        }
        McpMissionCommonContext mcpMissionCommonContext = JSON.parseObject(missionContext, McpMissionCommonContext.class);
        String taskAgentSessionId = mcpMissionCommonContext.getTaskAgentSessionId();
        TSessionResponse bySessionId = tAgentSessionService.getBySessionId(taskAgentSessionId);
        log.info("根据sessionId查询agent状态 taskAgentSessionId：{},TSessionResponse：{}", taskAgentSessionId, JSON.toJSONString(bySessionId));
        if (bySessionId != null && bySessionId.getCode().equals(200) && bySessionId.getData() != null) {
            Integer status = bySessionId.getData().getStatus();
            if (status.equals(1)) {
                String parentSessionId = mcpMissionCommonContext.getParentSessionId();
                reportResultToAgent(parentSessionId);
                jobExecutorService.withPostProcessor(missionInfo, AsynJobExecutorStatusEnum.PULLED, null);
                log.info("reportResultToAgent.parentSessionId: {}", parentSessionId);
            }
        }
    }


    private McpMissionInfo buildFridayMissionInfo(ExecuteFridayMissionContext context, ExecuteFridayMissionRequest fridayRequest, McpMissionInfo fridayMissionInfo) {
        fridayMissionInfo.setUpdateTime(new Date());
        fridayMissionInfo.setMissionId(context.getMissionId());
        fridayMissionInfo.setStatus(context.getStatus());
        fridayMissionInfo.setMissionRequest(JSONObject.toJSONString(fridayRequest));
        fridayMissionInfo.setMissionResult(context.getMissionResult());
        fridayMissionInfo.setMissionContext(JSONObject.toJSONString(context));
        fridayMissionInfo.setMissionId(context.getMissionId());
        return fridayMissionInfo;
    }

    private void processTestToolMission(McpMissionInfo missionInfo) {
        try {
            String missionContext = missionInfo.getMissionContext();
            ExecuteTestToolMissionContext context = JSONObject.parseObject(missionContext, ExecuteTestToolMissionContext.class);
            if (Objects.isNull(context) || Objects.isNull(context.getTaskId())) {
                return;
            }

            Long taskId = context.getTaskId();
            List<TestSubTaskContext> subTasks = queryTestSubTasks(taskId);
            if (CollectionUtils.isEmpty(subTasks)) {
                return;
            }
            String result = null;
            String link = null;
            String missionId = missionInfo.getMissionId();
            for (TestSubTaskContext subTask : subTasks) {

                if (StringUtils.isBlank(subTask.getMissionId()) || !subTask.getMissionId().equals(missionId)) {
                    continue;
                }

                Integer status = subTask.getStatus();
                String taskResultMessage = subTask.getTaskResultMessage();
                List<TestSubTaskContext.Reporter> reporters = subTask.getReporters();
                if (CollectionUtils.isNotEmpty(reporters)) {
                    link = reporters.get(0).getReporterAddr();
                }
                if (!StringUtils.isBlank(taskResultMessage)) {
                    if (taskResultMessage.contains("下载链接")) {
                        result = getUrl(taskResultMessage);
                    } else {
                        result = taskResultMessage;
                    }
                }
                Map<String, String> resultInfo = new HashMap<>();
                resultInfo.put("result", result);
                resultInfo.put("link", link);

                if (status == SUB_SUCCESS_STATUS && !(StringUtils.isBlank(result) && StringUtils.isBlank(link))) {
                    updateMissionStatus(missionInfo, context, String.format("测试工具相关任务执行成功,子任务执行成功!,subMissionId:%s", subTask.getMissionId()), true, JSONObject.toJSONString(resultInfo));
                    break;
                } else if (FAILURE_STATUSES.contains(status)) {
                    updateMissionStatus(missionInfo, context, String.format("测试工具相关任务执行失败,子任务%s执行失败!失败原因:%s", subTask.getMissionId(), taskResultMessage), false, null);
                    break;
                } else if (1 == status && checkIfOverTenDays(missionInfo)) {
                    context.setStatus(-1);
                    context.setExecuteMessage("相关任务执行超时,子任务执行超时!");
                    updateMissionStatus(missionInfo, context, String.format("相关任务执行超时,子任务%s执行超时!", subTask.getMissionId()), false, null);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("processTestToolMission error,missionInfo:{}", missionInfo, e);

        }
    }

    private Boolean checkIfOverTenDays(McpMissionInfo missionInfo) {
        if (Objects.isNull(missionInfo)) {
            return false;
        }
        Date updateTime = missionInfo.getUpdateTime();
        Date now = new Date();
        long diff = now.getTime() - updateTime.getTime();
        long days = diff / (24 * 60 * 60 * 1000);
        return days >= 10;
    }

    private void reportResultToAgent(String sessionId) {
        AgentMessageDTO agentMessageDTO = new AgentMessageDTO();
        agentMessageDTO.setSessionId(sessionId);
        TChatResponse chat = null;
        try {
            chat = tAgentChatService.chat(AGENT_MIS_ID, agentMessageDTO);
            if (chat == null || !Lists.newArrayList(0, 200).contains(chat.getCode())) {
                throw new RuntimeException("调用agent接口失败,无法唤醒agent,sessionId:" + sessionId);
            }
        } catch (Exception e) {
            log.error("调用agent接口失败,无法唤醒agent,sessionId:{}", sessionId, e);

        }
    }

    private void processCompositeMission(McpMissionInfo missionInfo) {
        try {
            String missionContext = missionInfo.getMissionContext();
            McpMissionCommonContext commonContext = JSONObject.parseObject(missionContext, McpMissionCommonContext.class);
            if (Objects.isNull(commonContext)) {
                return;
            }

            List<SubMissionState> subMissionStates = commonContext.getSubMissionStates();
            if (CollectionUtils.isEmpty(subMissionStates)) {
                return;
            }

            StringBuilder failureMessage = new StringBuilder();
            String missionId = missionInfo.getMissionId();

            String link = null;
            String result = null;
            boolean subTaskSuccess = false;
            boolean subTaskRunning = false;
            for (SubMissionState subMissionState : subMissionStates) {
                Long taskId = subMissionState.getSubMissionId();
                List<TestSubTaskContext> subTasks = queryTestSubTasks(taskId);
                if (CollectionUtils.isEmpty(subTasks)) {
                    failureMessage.append("子任务").append(taskId).append("查询失败; ");
                    break;
                }


                for (TestSubTaskContext subTask : subTasks) {
                    String missionIdInSubTask = subTask.getMissionId();
                    if (StringUtils.isBlank(missionIdInSubTask) || !missionIdInSubTask.equals(missionId)) {
                        continue;
                    }

                    Integer status = subTask.getStatus();
                    if (status == SUB_SUCCESS_STATUS) {
                        subTaskSuccess = true;
                        String taskResultMessage = subTask.getTaskResultMessage();
                        if (!StringUtils.isBlank(taskResultMessage)) {
                            if (taskResultMessage.contains("下载链接")) {
                                result = getUrl(taskResultMessage);
                            } else {
                                result = taskResultMessage;
                            }
                        }
                        List<TestSubTaskContext.Reporter> reporters = subTask.getReporters();
                        if (CollectionUtils.isNotEmpty(reporters)) {
                            TestSubTaskContext.Reporter reporter = reporters.get(0);
                            link = reporter.getReporterAddr();
                        }
                        break;
                    } else if (status == SUB_RUNNING_STATUS || status == SUB_INIT_STATUS || status == SUB_UNKNOWN_STATUS) {
                        subTaskRunning = true;
                    } else if (FAILURE_STATUSES.contains(status)) {
                        String taskResultMessage = formatTaskResultMessage(subTask);
                        failureMessage.append("子任务").append(subTask.getMissionId())
                                .append("执行失败, 原因: ").append(taskResultMessage).append("; ");
                        break;
                    } else if (checkIfOverTenDays(missionInfo)) {
                        commonContext.setStatus(-1);
                        commonContext.setExecuteMessage("相关任务执行超时,子任务执行超时!");
                        updateMissionStatus(missionInfo, commonContext, String.format("相关任务执行超时,子任务%s执行超时!", subTask.getMissionId()), false, null);
                        break;
                    }
                }
            }

            if (subTaskSuccess && !(StringUtils.isBlank(result) && StringUtils.isBlank(link))) {
                Map<String, String> resultInfo = new HashMap<>();
                resultInfo.put("result", result);
                resultInfo.put("link", link);
                updateMissionStatus(missionInfo, commonContext, "测试工具相关任务执行成功,所有子任务执行成功!", true, JSONObject.toJSONString(resultInfo));
            } else if (!subTaskRunning && !subTaskSuccess) {
                updateMissionStatus(missionInfo, commonContext, "测试工具相关任务执行失败: " + failureMessage.toString(), false, null);
            }

        } catch (Exception e) {
            log.error("processCompositeMission error", e);


        }

    }

    private String getUrl(String text) {
        String regex = "https://[^\"\\s]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        String url = null;
        if (matcher.find()) {
            url = matcher.group();
        }
        return url;
    }

    private List<TestSubTaskContext> queryTestSubTasks(Long taskId) {
        TMcpTestMissionResponse response = tMcpTestMissionService.queryMcpTestTaskByMissionId(taskId);
        if (response == null || response.code != 0) {
            Cat.logEvent("testTaskSchedule", "queryMcpTestTaskByMissionIdException");
            return Collections.emptyList();
        }

        Map<String, String> data = response.getData();
        if (MapUtils.isEmpty(data)) {
            Cat.logEvent("testTaskSchedule", "queryMcpTestTaskByMissionIdDataEmpty");
            return Collections.emptyList();
        }

        String testSubTaskContext = data.get("testSubTaskContext");
        Type type = new TypeToken<List<TestSubTaskContext>>() {
        }.getType();
        List<TestSubTaskContext> testSubTaskContexts = JSONObject.parseObject(testSubTaskContext, type);

        return testSubTaskContexts != null ? testSubTaskContexts : Collections.emptyList();
    }

    private String formatTaskResultMessage(TestSubTaskContext subTaskContext) {
        String statusName = subTaskContext.getStatusName();
        String taskResultMessage = subTaskContext.getTaskResultMessage();
        Integer status = subTaskContext.getStatus();

        if (StringUtils.isBlank(taskResultMessage) && StringUtils.isBlank(statusName)) {
            return "未知状态和结果!";
        } else {
            return "任务执行状态码:" + status + "_" + "状态说明：" + statusName;
        }
    }

    private void updateMissionStatus(McpMissionInfo missionInfo, Object context, String message, boolean isSuccess, String resultJson) {
        Integer status = isSuccess ? MISSION_SUCCESS : MISSION_FAILURE;
        missionInfo.setStatus(status);
        missionInfo.setMissionResult(isSuccess ? resultJson : null);
        missionInfo.setUpdateTime(new Date());

        String sessionId = null;
        if (context instanceof ExecuteTestToolMissionContext) {
            ExecuteTestToolMissionContext execContext = (ExecuteTestToolMissionContext) context;
            execContext.setStatus(status);
            execContext.setMessage(message);
            if (isSuccess) {
                execContext.setMissionResult(resultJson);
            }
            missionInfo.setMissionContext(JSONObject.toJSONString(execContext));
            sessionId = execContext.getSessionId();
        } else if (context instanceof McpMissionCommonContext) {
            McpMissionCommonContext commonContext = (McpMissionCommonContext) context;
            commonContext.setStatus(status);
            commonContext.setExecuteMessage(message);
            if (isSuccess) {
                commonContext.setMissionResult(resultJson);
            }
            missionInfo.setMissionContext(JSONObject.toJSONString(commonContext));
            sessionId = commonContext.getSessionId();
        }

        mcpserverMissionDao.updateMissionInfo(missionInfo);
        dxService.sendMsg2Users(message, Lists.newArrayList(missionInfo.getMisId(), "zhouzehao02"), null);
        reportResultToAgent(sessionId);
    }

}
