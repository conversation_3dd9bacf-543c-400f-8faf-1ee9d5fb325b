package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.config;

import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.tool.MatchTool;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/18
 */
@Configuration
public class MatchToolConfig {

    @Bean
    public ToolCallbackProvider MatchToolServiceConfig(MatchTool matchTool) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(matchTool)
                .build();
    }

}
