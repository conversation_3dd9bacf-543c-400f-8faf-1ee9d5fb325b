package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.llm.ChatClientFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessMcpServerInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolInfo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import org.springframework.ai.model.function.FunctionCallback;

import java.util.List;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/7
 */
public interface ToolManagerService {

    /**
     * 获取chatClientFactory
     */
    ChatClientFactory getChatClientFactory();

    /**
     * 注册工具
     */
    Long registerTool(Long serverId, Map<String, Object> toolInfoMap) throws Exception;

    /**
     * 注销工具
     */
    Long unregisterTool(Map<String, Object> toolInfoMap) throws Exception;

    void deleteToolWithService(Map<String, Object> toolInfoMap);

    /**
     * 根据工具类型获取工具列表
     */
    McpServerResponse listToolsByType(Long mcpServerId, ToolTypeEnum toolType);

    /**
     * 获取工具信息
     */
    McpServerResponse getToolInfo(Long mcpServerId, String toolName);

    /**
     * 获取LLM模型上下文
     */
    McpServerResponse getLLMModelContext(String interfaceName, String methodName, ToolTypeEnum toolTypeEnum);

    String getLLMModelContext(Map<String, Object> toolInfoMap);
    /**
     * 获取已经注册成功的工具
     */
    List<ToolInfo> getAllToolsInServer(Long mcpServerId);

    /**
     * 获取服务下已经注册成功的工具
     */
    List<String> getUsedToolsNameInServer(Long mcpServerId) ;


    List<ToolInfo> convertFuncationCallBackToTool(List<FunctionCallback> functionCallbackList, Long mcpServerId, ToolTypeEnum toolTypeEnum);

    /**
     * 更新工具
     */
    Long updateTool(Long serverId, Map<String, Object> toolInfoMap);

    List<BusinessMcpServerInfo> loadBusinessMcpServerInfoFromDataBase();

    Long unregisterTool(ToolInfo toolInfo);

    Boolean deleteToolByMcpServerEntity(McpServerEntity mcpServerEntity);

    Boolean checkToolisExist(Long serverId, String toolName);

    List<ToolInfo> getAllDefaultTools(Long mcpServerId);

    List<ToolInfo> processUsedDefaultTool(Long mcpServerId);
}
