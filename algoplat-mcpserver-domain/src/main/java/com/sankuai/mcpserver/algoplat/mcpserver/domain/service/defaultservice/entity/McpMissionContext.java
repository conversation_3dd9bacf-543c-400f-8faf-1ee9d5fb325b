package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class McpMissionContext {

    private String missionId;

    /**
     * 整个流程的执行结果
     * 0 执行中 1 执行成功 -1 执行失败 -2 未知
     */
    private Integer status = -2;

    /**
     * 整个流程的执行结果
     */
    private String missionResult;

    /**
     * 执行信息
     */
    private String executeMessage;

}
