package com.sankuai.mcpserver.algoplat.mcpserver.domain.register;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

@Component("toolRegisterFactory")
public class ToolRegisterFactory implements BeanFactoryAware {

    private BeanFactory beanFactory;

    // 使用ConcurrentHashMap并直接初始化，避免null问题
    public static Map<String, ToolRegister> toolRegisterMap = new ConcurrentHashMap<>();

    // 添加一个标志位，避免重复初始化
    private final AtomicBoolean initialized = new AtomicBoolean(false);

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    // 移除 @PostConstruct 注解，改为手动调用
    public void init() {
        // 使用 CAS 操作确保只初始化一次
        if (!initialized.compareAndSet(false, true)) {
            return;
        }

        // 清空Map并重新填充
        toolRegisterMap.clear();

        // 获取所有ToolRegister类型的bean
        Map<String, ToolRegister> strategyBeanMap = ((ListableBeanFactory) beanFactory).getBeansOfType(ToolRegister.class);

        // 填充Map
        for (ToolRegister toolRegister : strategyBeanMap.values()) {
            toolRegisterMap.put(toolRegister.getToolType().getValue(), toolRegister);
        }
    }

    public ToolRegister getToolRegister(ToolTypeEnum toolType) {
        // 确保已初始化
        if (initialized.get() == false) {
            init();
        }

        if (toolType == null) {
            return null;
        }
        return toolRegisterMap.get(toolType.getValue());
    }
}
