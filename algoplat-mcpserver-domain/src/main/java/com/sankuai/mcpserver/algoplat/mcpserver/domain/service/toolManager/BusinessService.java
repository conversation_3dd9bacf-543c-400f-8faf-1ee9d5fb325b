package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager;

import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;

/**
 * 业务线服务接口
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
public interface BusinessService {

    /**
     * 创建业务线
     * @param businessLine 业务线实体
     * @return 创建结果
     */
    McpServerResponse createBusinessLine(BusinessLine businessLine);

    /**
     * 获取所有业务线
     * @return 业务线列表
     */
    McpServerResponse getAllBusinessLine();

    /**
     * 根据ID获取业务线
     * @param businessLineId 业务线ID
     * @return 业务线详情
     */
    McpServerResponse getBusinessLineById(Long businessLineId);

    /**
     * 更新业务线
     * @param businessLine 业务线实体
     * @return 更新结果
     */
    McpServerResponse updateBusinessLine(BusinessLine businessLine);

    /**
     * 删除业务线（将状态设置为0）
     * 同时会级联删除该业务线下的所有McpServer和Tool
     * @param businessLineId 业务线ID
     * @return 删除结果
     */
    McpServerResponse deleteBusinessLine(Long businessLineId);

    /**
     * 检查业务线名称是否已存在
     * @param businessLineName 业务线名称
     * @return 检查结果
     */
    McpServerResponse checkBusinessLineNameIsExist(String businessLineName);

    /**
     * 根据名称获取业务线
     * @param businessLineName 业务线名称
     * @return 业务线详情
     */
    McpServerResponse getBusinessLineByBusiness(String businessLineName);
}
