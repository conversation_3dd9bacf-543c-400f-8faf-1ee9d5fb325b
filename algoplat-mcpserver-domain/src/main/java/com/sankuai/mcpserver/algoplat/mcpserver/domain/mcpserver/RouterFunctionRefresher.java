package com.sankuai.mcpserver.algoplat.mcpserver.domain.mcpserver;

import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.DispatcherServlet;
import org.springframework.web.servlet.function.RouterFunction;
import org.springframework.web.servlet.function.RouterFunctions;
import org.springframework.web.servlet.function.support.RouterFunctionMapping;
import org.springframework.web.servlet.handler.AbstractHandlerMapping;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 路由函数刷新器
 * 负责刷新 RouterFunction，使新注册的路由生效
 */
@Slf4j
@Component
public class RouterFunctionRefresher {

    @Autowired
    private ApplicationContext applicationContext;

    @Resource
    private DispatcherServlet dispatcherServlet;

    private final ReentrantLock refreshLock = new ReentrantLock();

    /**
     * 刷新RouterFunction，使新注册的路由生效
     * 增强版本，确保路由能够立即生效，同时不影响现有的 SSE 连接
     */
    public void refreshRouterFunctions() {
        // 使用锁确保刷新操作的线程安全
        refreshLock.lock();
        try {
            log.info("开始刷新RouterFunction...");

            // 获取所有RouterFunction类型的Bean
            Map<String, RouterFunction> routerFunctions =
                    applicationContext.getBeansOfType(RouterFunction.class);

            log.info("找到 {} 个RouterFunction Bean", routerFunctions.size());

            if (!routerFunctions.isEmpty()) {
                // 创建一个组合的RouterFunction
                RouterFunction<?> combinedRouterFunction = routerFunctions.values().stream()
                        .reduce(RouterFunction::andOther)
                        .orElse(null);

                if (combinedRouterFunction != null) {
                    // 更新RouterFunctionMapping
                    boolean routerFunctionMappingUpdated = updateRouterFunctionMapping(combinedRouterFunction);

                    // 更新DispatcherServlet的HandlerMapping
                    boolean handlerMappingsUpdated = updateDispatcherServletHandlerMappings(combinedRouterFunction);

                    // 尝试重新初始化DispatcherServlet的RouterFunctionMapping
                    boolean dispatcherServletReinitialized = reinitializeDispatcherServletRouterMapping();

                    // 尝试直接注册路由
                    boolean directRegistrationSuccessful = registerRouteDirectly(combinedRouterFunction);

                    log.info("RouterFunction刷新结果: RouterFunctionMapping更新={}, HandlerMappings更新={}, DispatcherServlet重新初始化={}, 直接注册={}",
                            routerFunctionMappingUpdated, handlerMappingsUpdated, dispatcherServletReinitialized, directRegistrationSuccessful);

                    if (routerFunctionMappingUpdated || handlerMappingsUpdated || dispatcherServletReinitialized || directRegistrationSuccessful) {
                        log.info("RouterFunction刷新成功");
                    } else {
                        log.warn("RouterFunction刷新可能不完整，请检查路由是否正常工作");
                    }
                } else {
                    log.warn("组合RouterFunction失败");
                }
            } else {
                log.warn("没有找到可用的RouterFunction，刷新操作被跳过");
            }
        } catch (Exception e) {
            
            log.error("刷新RouterFunction时发生错误: {}", e.getMessage(), e);
        } finally {
            refreshLock.unlock();
        }
    }

    /**
     * 直接注册路由，确保新路由能够立即生效
     */
    private boolean registerRouteDirectly(RouterFunction<?> combinedRouterFunction) {
        try {
            RouterFunctionMapping mapping = null;
            try {
                mapping = applicationContext.getBean(RouterFunctionMapping.class);
            } catch (Exception e) {
                try {
                    mapping = (RouterFunctionMapping) applicationContext.getBean("routerFunctionMapping");
                } catch (Exception ex) {
                    log.debug("无法获取 RouterFunctionMapping: {}", ex.getMessage());
                    return false;
                }
            }

            if (mapping != null) {
                // 2. 设置 RouterFunction
                try {
                    Method setRouterFunctionMethod = mapping.getClass().getMethod("setRouterFunction", RouterFunction.class);
                    setRouterFunctionMethod.invoke(mapping, combinedRouterFunction);

                    // 3. 重新初始化
                    Method initMethod = mapping.getClass().getMethod("afterPropertiesSet");
                    initMethod.invoke(mapping);

                    // 4. 尝试调用 initLookupPath 方法（如果存在）
                    try {
                        Method initLookupPathMethod = mapping.getClass().getMethod("initLookupPath", ServletContext.class);
                        ServletContext servletContext = applicationContext.getBean(ServletContext.class);
                        initLookupPathMethod.invoke(mapping, servletContext);
                    } catch (NoSuchMethodException e) {
                        // 忽略，因为这个方法可能不存在
                    }

                    log.info("直接注册路由成功");
                    return true;
                } catch (Exception e) {
                    log.warn("直接注册路由失败: {}", e.getMessage());
                }
            }

            // 5. 尝试强制刷新 DispatcherServlet
            try {
                Method detectAllHandlerMappingsMethod = DispatcherServlet.class.getDeclaredMethod("detectAllHandlerMappings");
                detectAllHandlerMappingsMethod.setAccessible(true);
                detectAllHandlerMappingsMethod.invoke(dispatcherServlet);

                // 6. 尝试重新初始化 HandlerMappings
                Field handlerMappingsField = DispatcherServlet.class.getDeclaredField("handlerMappings");
                handlerMappingsField.setAccessible(true);
                handlerMappingsField.set(dispatcherServlet, null);

                // 7. 再次调用 detectAllHandlerMappings
                detectAllHandlerMappingsMethod.invoke(dispatcherServlet);

                log.info("强制刷新 DispatcherServlet 成功");
                return true;
            } catch (Exception e) {
                log.warn("强制刷新 DispatcherServlet 失败: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.warn("直接注册路由过程中发生错误: {}", e.getMessage());
        }

        return false;
    }

    /**
     * 更新RouterFunctionMapping
     * 优化版本，确保不会影响现有的 SSE 连接
     */
    private boolean updateRouterFunctionMapping(RouterFunction<?> combinedRouterFunction) {
        boolean updated = false;

        try {
            // 尝试通过Bean名称获取RouterFunctionMapping
            Object routerFunctionMapping = applicationContext.getBean("routerFunctionMapping");
            if (routerFunctionMapping != null) {
                // 使用反射设置RouterFunction
                Method setRouterFunctionMethod =
                        routerFunctionMapping.getClass().getMethod("setRouterFunction", RouterFunction.class);
                setRouterFunctionMethod.invoke(routerFunctionMapping, combinedRouterFunction);

                // 调用afterPropertiesSet方法
                Method afterPropertiesSetMethod =
                        routerFunctionMapping.getClass().getMethod("afterPropertiesSet");
                afterPropertiesSetMethod.invoke(routerFunctionMapping);

                // 尝试调用initLookupPath方法（如果存在）
                try {
                    Method initLookupPathMethod = routerFunctionMapping.getClass().getMethod("initLookupPath", ServletContext.class);
                    ServletContext servletContext = applicationContext.getBean(ServletContext.class);
                    initLookupPathMethod.invoke(routerFunctionMapping, servletContext);
                } catch (NoSuchMethodException e) {
                    // 忽略，因为这个方法可能不存在
                }

                log.info("RouterFunctionMapping更新成功");
                updated = true;
            }
        } catch (Exception e) {
            log.debug("无法通过Bean名称获取RouterFunctionMapping: {}", e.getMessage());
            // 尝试通过类型获取
            try {
                Class<?> routerFunctionMappingClass =
                        Class.forName("org.springframework.web.servlet.function.support.RouterFunctionMapping");
                Object routerFunctionMapping = applicationContext.getBean(routerFunctionMappingClass);
                if (routerFunctionMapping != null) {
                    // 使用反射设置RouterFunction
                    Method setRouterFunctionMethod =
                            routerFunctionMapping.getClass().getMethod("setRouterFunction", RouterFunction.class);
                    setRouterFunctionMethod.invoke(routerFunctionMapping, combinedRouterFunction);

                    // 调用afterPropertiesSet方法
                    Method afterPropertiesSetMethod =
                            routerFunctionMapping.getClass().getMethod("afterPropertiesSet");
                    afterPropertiesSetMethod.invoke(routerFunctionMapping);

                    log.info("RouterFunctionMapping更新成功(通过类型获取)");
                    updated = true;
                }
            } catch (Exception ex) {
                log.debug("无法通过类型获取RouterFunctionMapping: {}", ex.getMessage());
            }
        }

        return updated;
    }

    /**
     * 更新DispatcherServlet的HandlerMapping
     * 优化版本，只更新RouterFunctionMapping，不影响其他HandlerMapping
     */
    private boolean updateDispatcherServletHandlerMappings(RouterFunction<?> combinedRouterFunction) {
        boolean updated = false;

        try {
            if (dispatcherServlet != null) {
                // 获取HandlerMappings
                List<?> handlerMappings = dispatcherServlet.getHandlerMappings();
                if (handlerMappings != null && !handlerMappings.isEmpty()) {
                    // 只更新RouterFunctionMapping
                    for (Object mapping : handlerMappings) {
                        if (mapping instanceof RouterFunctionMapping ||
                                (mapping.getClass().getSimpleName().equals("RouterFunctionMapping"))) {
                            try {
                                log.info("更新HandlerMapping: {}", mapping.getClass().getName());
                                // 使用反射设置RouterFunction
                                Method setRouterFunctionMethod =
                                        mapping.getClass().getMethod("setRouterFunction", RouterFunction.class);
                                setRouterFunctionMethod.invoke(mapping, combinedRouterFunction);

                                // 调用afterPropertiesSet方法，但不重新初始化整个映射
                                Method afterPropertiesSetMethod =
                                        mapping.getClass().getMethod("afterPropertiesSet");
                                afterPropertiesSetMethod.invoke(mapping);

                                log.info("HandlerMapping更新成功");
                                updated = true;
                            } catch (Exception e) {
                                log.warn("更新HandlerMapping失败: {}", e.getMessage(), e);
                            }
                        }
                    }
                } else {
                    log.debug("DispatcherServlet的HandlerMappings为null或为空，这是正常的，将在第一个请求到达时初始化");
                }
            } else {
                log.debug("DispatcherServlet为null，这是正常的，将在第一个请求到达时初始化");
            }
        } catch (Exception e) {
            log.warn("刷新DispatcherServlet的HandlerMapping失败: {}", e.getMessage(), e);
        }

        return updated;
    }

    /**
     * 尝试重新初始化DispatcherServlet的RouterFunctionMapping
     * 优化版本，只重新初始化RouterFunctionMapping，不影响其他组件
     */
    private boolean reinitializeDispatcherServletRouterMapping() {
        try {
            if (dispatcherServlet != null) {
                // 获取HandlerMappings
                Field handlerMappingsField = DispatcherServlet.class.getDeclaredField("handlerMappings");
                handlerMappingsField.setAccessible(true);
                List<?> handlerMappings = (List<?>) handlerMappingsField.get(dispatcherServlet);

                if (handlerMappings != null) {
                    // 找到并移除RouterFunctionMapping
                    boolean removed = false;
                    for (int i = 0; i < handlerMappings.size(); i++) {
                        Object mapping = handlerMappings.get(i);
                        if (mapping instanceof RouterFunctionMapping ||
                                (mapping.getClass().getSimpleName().equals("RouterFunctionMapping"))) {
                            // 不直接移除，而是重新初始化
                            try {
                                Method afterPropertiesSetMethod = mapping.getClass().getMethod("afterPropertiesSet");
                                afterPropertiesSetMethod.invoke(mapping);
                                log.debug("重新初始化RouterFunctionMapping: {}", mapping.getClass().getName());
                                removed = true;
                            } catch (Exception e) {
                                log.warn("重新初始化RouterFunctionMapping失败: {}", e.getMessage());
                            }
                        }
                    }

                    if (removed) {
                        log.debug("已重新初始化DispatcherServlet的RouterFunctionMapping");
                        return true;
                    }
                }

                // 如果没有找到RouterFunctionMapping，尝试重新检测
                try {
                    Method detectAllHandlerMappingsMethod = DispatcherServlet.class.getDeclaredMethod("detectAllHandlerMappings");
                    detectAllHandlerMappingsMethod.setAccessible(true);
                    detectAllHandlerMappingsMethod.invoke(dispatcherServlet);
                    log.debug("重新检测所有HandlerMappings");
                    return true;
                } catch (Exception e) {
                    log.debug("重新检测HandlerMappings失败: {}", e.getMessage());
                }
            }
        } catch (Exception e) {
            log.warn("重新初始化DispatcherServlet的RouterFunctionMapping失败: {}", e.getMessage(), e);
        }

        return false;
    }
}