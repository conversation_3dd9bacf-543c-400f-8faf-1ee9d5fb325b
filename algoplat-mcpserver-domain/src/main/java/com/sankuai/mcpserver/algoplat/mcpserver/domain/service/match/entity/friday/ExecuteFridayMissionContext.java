package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.McpMissionContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExecuteFridayMissionContext extends McpMissionContext {

    private Boolean modelTrainingRetry = false;

    private Integer modelTrainingRetryNum = 0;

    private String modelId;

    private String modelServiceId;

    private Long datasetId;

    private String datasetName;

    private String trainingName;

    private Integer modelType;

    private String trainingId;

    private String modelRegName;

    private String fridayModelUrl;

    private String fridayModelDeployUrl;

    private String iter;

    private String modelDeployName;

    private ExecuteFridayMissionRequest request;

    private List<Integer> versionList =new ArrayList<>();

    private String sessionId;

    private JSONObject deployJson;

    /**
     * 训练数据上传执行结果
     * 0 执行中 1 执行成功 -1 执行失败 -2 未知
     */
    private Integer uploadDatasetStatus = -2;

    /**
     * 训练任务执行结果
     * 0 执行中 1 执行成功 -1 执行失败 -2 未知
     */
    private Integer trainingTaskStatus = -2;

    /**
     * 模型注册执行结果
     * 0 执行中 1 执行成功 -1 执行失败 -2 未知
     */
    private Integer modelRegStatus = -2;

    /**
     * 模型部署执行结果
     * 0 执行中 1 执行成功 -1 执行失败 -2 未知
     */
    private Integer modelDeployStatus = -2;

    /**
     * 模型测试执行结果
     * 0 执行中 1 执行成功 -1 执行失败 -2 未知
     */
    private Integer modelTestStatus = -2;

    /**
     * 大模型调用方式 chat还是text
     */
    private String chatOrText;






}
