package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TKnowledgeCollectRequestDto {
    @ToolParam(description = "agentCode", required = true)
    public String agentCode;

    @ToolParam(description = "知识ID", required = true)
    public Long knowledgeKey;

    @ToolParam(description = "知识数据,格式为string类型的json数据", required = true)
    public String knowledgeData;

    @ToolParam(description = "知识收集ID", required = true)
    public Long collectId;

}
