package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class McpMissionResult {

    private String missionId;

    private Integer status;

    private Map<String, Object> contextInfo;

    private String missionResult;

    private String request;

    private String sessionId;
    
}


