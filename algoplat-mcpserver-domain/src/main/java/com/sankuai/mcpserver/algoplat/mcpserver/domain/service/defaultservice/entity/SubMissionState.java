package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/12
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SubMissionState {

    private Long subMissionId;

    private String subSessionId;

    private String subMissionName;

    private Integer status;

    private String message;

    private Map<String, String> extraInfo;

    private Date startTime = new Date();

    private Date endTime;
}
