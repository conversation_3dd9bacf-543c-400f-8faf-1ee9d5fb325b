package com.sankuai.mcpserver.algoplat.mcpserver.domain.register;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.zebra.util.StringUtils;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.meituan.mtrace.Tracer;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.configs.ThriftClientProxyBeanConfig;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.register.entity.ToolRegisterContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.schedules.AsyncJobExecutor;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.McpBeanRecordService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.JobExecutorService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.McpServerService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.others.BeanNameManager;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.RpcParameterUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.SpringContextUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.ToolContextUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.BusinessLineDao;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.LongTermTaskTypeEnum;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ToolTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;

@Slf4j
@Component
public class ThriftToolRegister extends AbstractToolRegister {

    @Autowired
    private McpServerService mcpserverService;

    @Autowired
    private BusinessLineDao businessLineDao;

    @Resource(name = "asynJobExecutorService")
    private JobExecutorService jobExecutorService;

    private static ThriftClientProxyBeanConfig thriftClientProxyBeanConfig;

    @Autowired
    public void setThriftClientProxyBeanConfig(ThriftClientProxyBeanConfig thriftClientProxyBeanConfig) {  // 添加构造方法
        this.thriftClientProxyBeanConfig = thriftClientProxyBeanConfig;
    }

    @Autowired
    private McpBeanRecordService mcpBeanRecordService;

    @Override
    public ToolCallback registerTool(ToolRegisterContext context, Long toolId) {
        ThriftToolInfo thriftToolInfo = transform(context, ThriftToolInfo.class);
        context.setToolInfo(thriftToolInfo);
        String toolName = thriftToolInfo.getName();
        try {
            ToolCallback toolCallback = getToolCallback(context);
            Long mcpServerId = thriftToolInfo.getMcpServerId();
            McpServerEntity mcpServerById = mcpserverService.getMcpServerById(mcpServerId);
            if (mcpServerById == null) {
                throw new RuntimeException("Failed to register Thrift tool: mcpServer is null");
            }
            Long businessLineId = mcpServerById.getBusinessLineId();
            BusinessLine existingLine = businessLineDao.getBusinessLineById(businessLineId);
            if (existingLine == null) {
                throw new RuntimeException("Failed to register Thrift tool: businessLine is null");
            }
            registerToolForMcpServer(toolCallback, existingLine, mcpServerById);
            mcpBeanRecordService.updateAliveMcpRecord(mcpServerId, toolId);
            return toolCallback;
        } catch (Exception e) {
            log.error("Failed to register Thrift tool: {}", toolName, e);
            throw new RuntimeException("Failed to register Thrift tool: " + e.getMessage(), e);
        }
    }

    @Override
    public ToolTypeEnum getToolType() {
        return ToolTypeEnum.THRIFT;
    }

    @Override
    public ToolCallback getToolCallback(ToolRegisterContext context) {
        ThriftToolInfo thriftToolInfo = transform(context, ThriftToolInfo.class);
        context.setToolInfo(thriftToolInfo);
        String toolName = thriftToolInfo.getName();

        try {
            if (context.isHasRegistered()) {
                // 注销Tool
                unregisterTool(thriftToolInfo);
            }
            // 注册Tool
            ToolCallback toolCallback = FunctionToolCallback
                    .builder(thriftToolInfo.getName(), new ThriftToolBiFunction(thriftToolInfo))
                    .description(thriftToolInfo.getDescription())
                    .inputType(Map.class)
                    .inputSchema(generateInputSchema(thriftToolInfo))
                    .build();

            return toolCallback;
        } catch (Exception e) {
            log.error("Failed to getToolCallback Thrift tool: {}", toolName, e);
            throw new RuntimeException("Failed to getToolCallback Thrift tool: {} " + e.getMessage(), e);
        }
    }


    private record ThriftToolBiFunction(
            ThriftToolInfo thriftToolInfo) implements BiFunction<Map<String, Object>, ToolContext, String> {
        @Override
        public String apply(Map<String, Object> toolRequest, ToolContext toolContext) {
            if (!ToolInfo.IS_LONG_TERM_TASK_TRUE.equals(thriftToolInfo.getIsLongTermTask())) {
                return executeThriftInvoke(thriftToolInfo, toolRequest);
            } else if (thriftToolInfo.getLongTermTaskType().equals(LongTermTaskTypeEnum.AUTO_AGENT.getCode())) {
                // 自动唤醒Agent长任务
                log.info("长连接任务，自动唤醒Agent长任务，executeThriftInvoke toolRequest:{} thriftToolInfo:{}", JSON.toJSONString(toolRequest), JSON.toJSONString(thriftToolInfo));
                String result = executeThriftInvoke(thriftToolInfo, toolRequest);
                log.info("长连接任务，自动唤醒Agent长任务，executeThriftInvoke toolRequest:{} result:{}", JSON.toJSONString(toolRequest), result);
                JSONObject resultJson = JSON.parseObject(result);
                if (resultJson.getInteger("code") == 200 && resultJson.getJSONObject("data") != null && resultJson.getJSONObject("data").getString("sessionId") != null) {
                    JobExecutorService asynJobExecutorService = SpringContextUtils.getBean("asynJobExecutorService");
                    String missionId = DigestUtils.md5Hex(JSON.toJSONString(toolRequest) + System.currentTimeMillis());
                    String taskAgentSessionId = resultJson.getJSONObject("data").getString("sessionId");
                    asynJobExecutorService.createAgentTask(missionId, taskAgentSessionId, toolRequest);
                } else {
                    log.error("长连接任务，自动唤醒Agent长任务启动失败，executeThriftInvoke error toolRequest:{} result:{}", JSON.toJSONString(toolRequest), result);
                }
                return result;
            } else if (thriftToolInfo.getLongTermTaskType().equals(LongTermTaskTypeEnum.NON_AUTO_AGENT.getCode())) {
                // 非自动唤醒Agent长任务
                log.info("长连接任务，非自动唤醒Agent长任务，executeThriftInvoke toolRequest:{} thriftToolInfo:{}", JSON.toJSONString(toolRequest), JSON.toJSONString(thriftToolInfo));
                return executeThriftInvoke(thriftToolInfo, toolRequest);
            } else {
                // Thrift长任务
                String missionId = DigestUtils.md5Hex(JSON.toJSONString(toolRequest) + System.currentTimeMillis());
                String sessionId = ToolContextUtil.querySessionIdByToolContext(toolContext);

                toolRequest.put("sessionId", sessionId);

                AsyncJobExecutor.submitTask(SpringContextUtils.getBean("asynJobExecutorService"),
                        missionId, (t) -> executeThriftInvoke(thriftToolInfo, toolRequest), toolRequest);

                Map<String, Object> result = new HashMap<>();
                result.put("missionId", missionId);
                result.put("sessionId", sessionId);
                result.put("isLongTermTask", "是");
                return JSON.toJSONString(McpServerResponse.success("任务提交成功", JSON.toJSONString(result)));
            }
        }
    }


    /**
     * 执行Thrift调用
     *
     * @param toolRequest
     */
    private static String executeThriftInvoke(ThriftToolInfo thriftToolInfo, Map<String, Object> toolRequest) {
        String result = null;
        Transaction t = Cat.newTransaction(ServiceConstants.JOB_NAME, "ThriftToolRegister");
        try {
            if (thriftToolInfo == null || toolRequest == null) {
                t.setStatus("param is null");
                return null;
            }
            ThriftClientProxy thriftProxy = thriftClientProxyBeanConfig.createThriftProxy(thriftToolInfo);
            if (thriftProxy == null) {
                throw new RuntimeException("Failed to create Thrift proxy");
            }
            GenericService service = (GenericService) thriftProxy.getObject();
            if (Objects.isNull(service)) {
                throw new RuntimeException("Failed to create Thrift proxy");
            }
            log.info("ThriftToolBiFunction apply start. thriftToolInfo:{},toolRequest:{}", JSONObject.toJSONString(thriftToolInfo), JacksonUtil.toJsonStrWithEmptyDefault(toolRequest));
            List<ToolParameter> toolParams = thriftToolInfo.getToolParams();
            String avitorScriptCode = thriftToolInfo.getAvitorScriptCode();
            if (StringUtils.isNotBlank(avitorScriptCode)) {
                Expression compile = AviatorEvaluator.compile(avitorScriptCode, true);
                toolRequest = (Map<String, Object>) compile.execute(toolRequest);
            }
            Object jsonReplaceRule = toolRequest.get("jsonReplaceRule");
            if (jsonReplaceRule != null) {
                toolRequest.remove("jsonReplaceRule");
            }
            Pair<List<String>, List<String>> pair = RpcParameterUtil.parseParamTypeAndValues(toolParams, toolRequest, jsonReplaceRule);
            result = service.$invoke(thriftToolInfo.getMethodName(), pair.getLeft(), pair.getRight());
            log.info("ThriftToolBiFunction apply end. result:{}", result);
            t.setSuccessStatus();
            return result;
        } catch (Exception e) {
            t.setStatus(e);
            thriftClientProxyBeanConfig.destroy(BeanNameManager.genThriftProxyKey(thriftToolInfo));
            log.error("ThriftToolBiFunction apply error,thriftToolInfo:{},e:{}", JSONObject.toJSON(thriftToolInfo), e);
            return ExceptionUtils.getRootCauseMessage(e);
        } finally {
            log.info("ThriftToolBiFunction.thriftToolInfo:{},toolRequest:{},result:{}", JSONObject.toJSONString(thriftToolInfo), JSONObject.toJSONString(toolRequest), result);
            t.complete();
        }
    }
}
