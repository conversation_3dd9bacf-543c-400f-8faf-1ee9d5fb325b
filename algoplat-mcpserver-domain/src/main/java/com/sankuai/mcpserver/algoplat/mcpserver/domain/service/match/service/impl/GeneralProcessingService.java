package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.google.common.collect.Lists;
import com.sankuai.algoplatform.agentapi.client.dto.AgentMessageDTO;
import com.sankuai.algoplatform.agentapi.client.service.TAgentChatService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.llm.ChatClientFactory;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.DxService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager.ToolManagerService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Map;

import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants.AGENT_MIS_ID;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/16
 */

@Service
@Slf4j
public class GeneralProcessingService {

    @Resource
    private DxService dxService;

    @Resource
    private TAgentChatService tAgentChatService;

    @Resource
    private ToolManagerService toolManagerService;

    public void generalProcess(McpMissionInfo mcpMissionInfo, Object context, Object result, String sessionId, Boolean isSuccess) {
        String msg = "";
        try {
            if (isSuccess) {
                result = JSON.toJSONString(result);
                msg = String.format("任务执行成功,missionId:%s,SessionId:%s,结果:\n%s", mcpMissionInfo.getMissionId(), sessionId, result);
            } else {
                Map map = JSON.parseObject(JSON.toJSONString(context), Map.class);
                String executeMessage = map.get("executeMessage") == null ? "未知错误" : map.get("executeMessage").toString();
                String advice = exceptionAnalysis(new Exception(executeMessage));
                if (StringUtils.isBlank(advice)) {
                    advice = "请联系mcpserver管理员&&zhouzehao02";
                }
                msg = String.format("任务执行失败,missionId:%s,SessionId:%s,失败原因:%s\n 解决建议：%s", mcpMissionInfo.getMissionId(), sessionId, executeMessage, advice);
            }
            dxService.sendMsg2Users(msg, Lists.newArrayList(mcpMissionInfo.getMisId(), "zhouzehao02"), null);
            AgentMessageDTO agentMessageDTO = new AgentMessageDTO();
            agentMessageDTO.setSessionId(sessionId);
            tAgentChatService.chat(AGENT_MIS_ID, agentMessageDTO);

        } catch (Exception e) {
            log.error("generalProcess error", e);
        }
    }

    public String exceptionAnalysis(Exception exception) {
        try {
            log.info("exceptionAnalysis exception:{}", exception.getMessage());
            ChatClientFactory chatClientFactory = toolManagerService.getChatClientFactory();
            ChatClient exceptionAnalysisClient = chatClientFactory.getToolExceptionAnalysisClient();
            String content = exceptionAnalysisClient.prompt("报错信息为:" + JSONObject.toJSONString(exception)).call().content();
            log.info("exceptionAnalysis content:{}", content);
            return content;
        } catch (Exception e) {
            dxService.sendMsg2Users("异常分析失败，检查账户余额" + e.getMessage(), Lists.newArrayList("zhouzehao02"), null);
            log.error("exceptionAnalysis error", e);
            return null;
        }
    }
}
