package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import java.util.List;

/**
 * 模板信息实体类
 */
public class TemplateInfo {
    private String templateName;      // 模板名称
    private String templatePattern;   // 模板匹配模式
    private List<String> aliases;     // 别名列表

    // getters and setters
    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplatePattern() {
        return templatePattern;
    }

    public void setTemplatePattern(String templatePattern) {
        this.templatePattern = templatePattern;
    }

    public List<String> getAliases() {
        return aliases;
    }

    public void setAliases(List<String> aliases) {
        this.aliases = aliases;
    }
}