package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeCollectorRequest {

    @ToolParam(description = "agentCode", required = true)
    private String agentCode;

    @ToolParam(description = "知识库key,用于唯一标记知识的key", required = true)
    private String knowledgeKey;

    @ToolParam(description = "知识数据,格式为string类型的json数据", required = true)
    private String knowledgeData;
}
