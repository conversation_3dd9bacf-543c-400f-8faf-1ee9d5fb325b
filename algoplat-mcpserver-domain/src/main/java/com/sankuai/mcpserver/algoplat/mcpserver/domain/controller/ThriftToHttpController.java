package com.sankuai.mcpserver.algoplat.mcpserver.domain.controller;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.StringUtils;
import com.google.common.reflect.TypeToken;
//import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TGetXuechengContentRequest;
//import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TGetXuechengContentResponse;
import com.sankuai.algoplatform.matchops.api.request.testreprotertemplate.TGetXuechengContentRequest;
import com.sankuai.algoplatform.matchops.api.response.testreportertemplate.TGetXuechengContentResponse;
import com.sankuai.algoplatform.matchops.api.service.TXuechengService;
import com.sankuai.grocery.dola.thrift.model.response.TBaseResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.FlowEntityRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.entity.McpMissionResult;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl.ThriftToHttpService;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.McpToolResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.AnnotationLabelingToolRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionContext;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.friday.ExecuteFridayMissionRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.impl.FridayServiceImpl;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.tool.MatchTool;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.XuechengToMarkdownUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao.McpMissionDao;
import com.sankuai.nib.data.zb.flow.platform.api.request.FlowVersionVerifyRequestDTO;
import com.sankuai.nib.data.zb.flow.platform.api.response.FlowVerifyResponseDTO;
import com.sankuai.nib.data.zb.flow.platform.api.service.FlowExecutorThriftService;
import com.sankuai.zb.metadata.api.dto.flow.standard.FlowAnalysisDTO;
import com.sankuai.zb.metadata.api.dto.rule.ParseRuleVersionDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/11
 */
@Slf4j
@RestController
@RequestMapping("/thrift/api")
public class ThriftToHttpController {
    @Autowired
    private ThriftToHttpService thriftToHttpService;
    @Resource
    private TXuechengService tXuechengService;

    @Autowired
    private MatchTool matchTool;

    @Resource
    private FlowExecutorThriftService flowExecutorThriftService ;

    @PostMapping("/addFlowAnalysisRule")
    public McpServerResponse addFlowAnalysisRule(@RequestBody FlowAnalysisDTO flowAnalysisDTO) {
        try {
            TBaseResponse tBaseResponse = thriftToHttpService.addFlowAnalysisRule(flowAnalysisDTO);
            if (tBaseResponse == null || tBaseResponse.getCode() != 0) {
                McpServerResponse.fail(tBaseResponse.getMsg());
            }
            return McpServerResponse.success("成功", JSONObject.toJSONString(tBaseResponse.getData()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return McpServerResponse.fail(e.getMessage());
        }
    }

    @GetMapping("/queryFlowInstanceByVersion")
    public McpServerResponse queryFlowInstanceByVersion(@RequestParam(name = "flowId") Object flowId, @RequestParam(name = "version") Object version) {
        try {
            TBaseResponse tBaseResponse = thriftToHttpService.queryFlowInstanceByVersion(flowId, version);
            if (tBaseResponse == null || tBaseResponse.getCode() != 0) {
                McpServerResponse.fail(tBaseResponse.getMsg());
            }
            return McpServerResponse.success("成功", JSONObject.toJSONString(tBaseResponse.getData()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return McpServerResponse.fail(e.getMessage());
        }
    }

    @GetMapping("/getRuleWithVersion")
    public McpServerResponse getRuleWithVersion(@RequestParam(name = "ruleId") Object ruleId, @RequestParam(name = "version") Object version) {
        try {
            TBaseResponse ruleWithVersion = thriftToHttpService.getRuleWithVersion(ruleId, version);
            if (ruleWithVersion == null || ruleWithVersion.getCode() != 0) {
                return McpServerResponse.fail(ruleWithVersion.getMsg());
            }
            return McpServerResponse.success("成功", JSONObject.toJSONString(ruleWithVersion.getData()));
        } catch (Exception e) {
            log.error("getRuleWithVersion error", e);
            return McpServerResponse.fail(e.getMessage());
        }
    }

    @PostMapping("/addParseRuleVersionAndReturn")
    public McpServerResponse addParseRuleVersionAndReturn(@RequestBody ParseRuleVersionDTO parseRuleVersionDTO) {
        try {
            TBaseResponse tBaseResponse = thriftToHttpService.addParseRuleVersionAndReturn(parseRuleVersionDTO);
            if (tBaseResponse == null || tBaseResponse.getCode() != 0) {
                return McpServerResponse.fail(tBaseResponse.getMsg());
            }
            return McpServerResponse.success("成功", JSONObject.toJSONString(tBaseResponse.getData()));
        } catch (Exception e) {
            log.error("addParseRuleVersionAndReturn error", e);
            return McpServerResponse.fail(e.getMessage());
        }
    }

    @GetMapping("/getXuechengDoc")
    public String getXuechengDoc(@RequestParam(name = "url") String url) {
        Transaction t = Cat.newTransaction("getXuechengDoc", "query_transform_rule_entity");
        String outPut = null;
        try {
            log.info("get_xuecheng_doc url:{}", url);
            TGetXuechengContentRequest tGetXuechengContentRequest = new TGetXuechengContentRequest();
            tGetXuechengContentRequest.setLink(url);
            TGetXuechengContentResponse response = tXuechengService.getXuechengContent(tGetXuechengContentRequest);
            if (response == null || response.getData() == null) {
                t.setStatus(response.getMessage());
                return response.getMessage();
            }
            String data = response.getData();
            Type type = new TypeToken<Map<String, Object>>() {
            }.getType();
            Map<String, Object> processInput = JSONObject.parseObject(data, type);
            XuechengToMarkdownUtil xuechengToMarkdownUtil = new XuechengToMarkdownUtil();
            String convert = xuechengToMarkdownUtil.convert(processInput);
            t.setSuccessStatus();
            log.info("get_xuecheng_doc convert:{}", convert);
            outPut = convert;
            return convert;
        } catch (Exception e) {
            log.error("get_xuecheng_doc error,url:{}", url, e);
            t.setStatus(e.getMessage());
            return null;
        } finally {
            log.info("get_xuecheng_doc ,url:{}, outPut:{}", url, outPut);
            t.complete();
        }


    }

    @PostMapping("/saveNewFlowEntity")
    public McpServerResponse saveNewFlowEntity(@RequestBody FlowEntityRequest flowEntityRequest) {

        return thriftToHttpService.saveNewFlowEntity(flowEntityRequest.getFlowName(), flowEntityRequest.getInterfaceCode(), flowEntityRequest.getMjScheduleFlag(), flowEntityRequest.getDesc(), flowEntityRequest.getModelList());
    }

    @GetMapping("/queryFridayMissionStatus")
    public McpToolResponse<McpMissionResult> queryFridayMissionStatus(@RequestParam(name = "missionId") String missionId) {
        if (StringUtils.isBlank(missionId)) {
            return McpToolResponse.paramsError();
        }

        Cat.logEvent("queryFridayMissionStatus", missionId);
        try {
            log.info("查询Friday任务状态，missionId：{}", missionId);
            McpToolResponse<McpMissionResult> mcpToolResponse = thriftToHttpService.queryFridayMissionStatus(missionId);
            log.info("查询Friday任务结果成功：missionId：{},result{}", missionId, JSONObject.toJSONString(mcpToolResponse));
            return mcpToolResponse;
        } catch (Exception e) {
            
            log.error(e.getMessage());
            return McpToolResponse.fail();
        }
    }

    @PostMapping("/executeFridayModelFactoryProcess")
    public McpToolResponse<String> executeFridayModelFactoryProcess(@RequestBody ExecuteFridayMissionRequest fridayRequest) throws Exception {
        if (fridayRequest == null) {
            return McpToolResponse.paramsError();
        }
        String jsonString = JSONObject.toJSONString(fridayRequest);
        McpToolResponse mcpToolResponse1 = matchTool.executeFridayModelFactoryProcess(jsonString,null);
        return mcpToolResponse1;
    }

    @PostMapping("/searchAnnotationDataUsingLabelingTool")
    public McpToolResponse<Map<String,Object>> searchAnnotationDataUsingLabelingTool(@RequestBody AnnotationLabelingToolRequest annotationLabelingToolRequest) {
        McpToolResponse<Map<String, Object>> mapMcpToolResponse = matchTool.searchAnnotationDataUsingLabelingTool(annotationLabelingToolRequest, null);
        return mapMcpToolResponse;
    }



    @PostMapping("/verifyFlowVersion")
    public McpToolResponse<Object> verifyFlowVersion(@RequestBody FlowVersionVerifyRequestDTO request) throws Exception {
        if (request == null) {
            return McpToolResponse.paramsError();
        }
        TBaseResponse<FlowVerifyResponseDTO> flowVerifyResponseDTOTBaseResponse = flowExecutorThriftService.verifyFlowVersion(request);
        if (flowVerifyResponseDTOTBaseResponse == null || flowVerifyResponseDTOTBaseResponse.getCode() != 0) {
            return McpToolResponse.fail(flowVerifyResponseDTOTBaseResponse.getMsg());
        }
        return McpToolResponse.success("成功", flowVerifyResponseDTOTBaseResponse.getData());
    }
}