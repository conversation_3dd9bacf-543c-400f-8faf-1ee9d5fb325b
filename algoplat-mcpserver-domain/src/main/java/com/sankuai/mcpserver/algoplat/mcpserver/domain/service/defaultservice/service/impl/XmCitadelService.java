package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.defaultservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.util.CollectionUtils;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.GetChildContentBySsoReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.GetCollaborationContentBySsoReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.req.GetCollaborationMarkdownBySsoReq;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetChildContentBySsoResp;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetCollaborationContentBySsoResp;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.resp.GetCollaborationMarkdownBySsoResp;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.CitadelDoc;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.CreateCitadelDocReq;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.McpServerResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.entity.XueChengRequest;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.entity.McpToolResponse;
import com.sankuai.mcpserver.algoplat.mcpserver.domain.service.match.service.impl.GeneralProcessingService;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.MarkdownToCitadelConverter;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.ServerHostEnvUtil;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.XmAuthServiceWrapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.cache.TairClient;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.service.impl.S3Service;
import com.sankuai.xm.openplatform.common.entity.RespStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.ServiceConstants.*;

@Service
@Slf4j
public class XmCitadelService {
    @Autowired
    private XmCitadelProxy xmCitadelProxy;

    @Autowired
    private EmployInfoProxy employInfoProxy;

    @Autowired
    private XmAuthServiceWrapper xmAuthServiceWrapper;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private DxService dxService;

    @Autowired
    private CitadelService citadelService;

    @Autowired
    private TairClient tairClient;

    private static final String XUECHENG = "XueCheng";

    @Autowired
    private GeneralProcessingService generalProcessingService;

    public McpServerResponse getXuechengContentWithMarkdown(String url, String misId) {
        Transaction transaction = Cat.newTransaction(XUECHENG, "getXuechengContentWithMarkdown");
        log.info("getXuechengContentWithMarkdown url is {}, misId is {}", url, misId);
        Cat.logEvent(XUECHENG, "getXuechengContentWithMarkdown");
        try {
            String contentId = StringUtils.substringAfter(url, "collabpage/");
            Long docId = null;
            if (!StringUtils.isBlank(contentId)) {
                docId = Long.valueOf(contentId);
            }
            String ssoid = tairClient.get(XUECHENG_KEY + misId);
            if (StringUtils.isBlank(ssoid)) {
                String msg = "获取学城文档内容失败! ssoid为空,请登录BML门户更新sso信息。";
                transaction.setStatus(msg);
                return McpServerResponse.fail(msg);
            }
            GetCollaborationMarkdownBySsoReq request = new GetCollaborationMarkdownBySsoReq();
            GetCollaborationMarkdownBySsoReq.Request docReq = new GetCollaborationMarkdownBySsoReq.Request();
            docReq.setContentId(docId);
            docReq.setNeedNodeId(false);
            Set<String> extraNodeTypes = new HashSet<>();
            extraNodeTypes.add("html");
            docReq.setExtraNodeTypes(extraNodeTypes);
            Set<String> ultraNodeTypes = new HashSet<>();
            ultraNodeTypes.add("minder");
            ultraNodeTypes.add("drawio");
            docReq.setUltraNodeTypes(ultraNodeTypes);
            request.setRequest(docReq);
            GetCollaborationMarkdownBySsoResp resp = citadelService.getCollaborationMarkdownBySso(xmAuthServiceWrapper.getToken(), ssoid, request);
            RespStatus status = resp.getStatus();
            if (status.getCode() != 0) {
                String msg = "调用学城接口失败:" + status.getMsg();
                transaction.setStatus(msg);
                log.error("调用学城接口失败,contentId is {},userToken is {}", contentId, ssoid);
                return McpServerResponse.fail(msg);
            }
            String content = resp.getData().getContent();
            if (StringUtils.isBlank(content)) {
                String msg = "获取学城Markdown文档失败! content为空";
                transaction.setStatus(msg);
                log.error("获取学城Markdown文档失败! content为空,url is {},misId is{}", url, misId);
                return McpServerResponse.fail("获取学城文档内容失败! content为空");
            }
            log.info("getXuechengContentWithMarkdown success,url is {},misId is {},content is {}", url, misId, content);
            return McpServerResponse.success("获取学城Markdown文档成功！", content);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error(e.getMessage());
            String advice = generalProcessingService.exceptionAnalysis(e);
            if (StringUtils.isBlank(advice)) {
                advice = "请联系mcpserver负责人或者zhouzehao02";
            }
            String msg = "获取学城Markdown文档失败! 发起人:" + misId + "文档url为:" + url + "报错信息为：" + e.getMessage() + "指导建议:" + advice;
            dxService.sendMsg2Users(msg, Lists.newArrayList(misId, "zhouzehao02"), null);
            return McpServerResponse.fail(msg);
        } finally {
            transaction.complete();
        }
    }

    public McpServerResponse createCitadelDoc(CreateCitadelDocReq req) {
        Cat.logEvent(XUECHENG, "createCitadelDoc");
        log.info("createCitadelDoc req is {}", JSONObject.toJSONString(req));
        McpServerResponse mcpServerResponse = new McpServerResponse();
        Transaction transaction = Cat.newTransaction(XUECHENG, "createCitadelDoc");
        try {
            String checkResult = checkCreateCitadelDocReq(req);
            if (StringUtils.isNotBlank(checkResult)) {
                transaction.setStatus(checkResult);
                return mcpServerResponse.fail(checkResult);
            }
            String content = req.getContent();
            if (!StringUtils.isBlank(req.getContentS3Url())) {
                content = getContentFromS3(req.getContentS3Url());
            }
            log.info("createCitadelDoc 原始内容长度: {}", content.length());
            CitadelDoc citadelDoc = new CitadelDoc();
            citadelDoc.setMis(req.getMisId());
            citadelDoc.setTitle(req.getTitle());
            citadelDoc.setContent(content);
            citadelDoc.setSpaceId(req.getSpaceId());
            String parentUrl = req.getParentUrl();
            String contentId = StringUtils.substringAfter(parentUrl, "collabpage/");
            citadelDoc.setParentId(contentId);
            String link = createDoc(citadelDoc);
            log.info("createCitadelDoc success,req:{},link is {}", JSONObject.toJSONString(req), link);
            dxService.sendMsg2Users("创建学城文档成功！文档地址为:" + link, Lists.newArrayList(req.getMisId()), null);
            return mcpServerResponse.success("创建学城文档成功！", link);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("createCitadelDoc error,req:{}", JSONObject.toJSONString(req), e);
            String advice = generalProcessingService.exceptionAnalysis(e);
            if (StringUtils.isBlank(advice)) {
                advice = "请联系mcpserver负责人或者zhouzehao02";
            }
            String msg = "创建学城文档失败! 发起人:" + req.getMisId() + "报错信息为：" + e.getMessage() + "指导建议:" + advice;
            dxService.sendMsg2Users(msg, Lists.newArrayList(req.getMisId(), "zhouzehao02"), null);
            return McpServerResponse.fail(msg);
        } finally {
            transaction.complete();
        }
    }

    private String getContentFromS3(String s3Url) {
        Map<String, String> dataMeta = s3Service.getObjectInfoAndSetUTF8(s3Url);
        String bucketName = dataMeta.get("bucketName");
        String objectName = dataMeta.get("objectName");
        StringBuilder markdownContent = new StringBuilder();
        try (S3Object s3object = s3Service.getObject(new GetObjectRequest(bucketName, objectName));
             InputStream content = s3object.getObjectContent();
             BufferedReader reader = new BufferedReader(new InputStreamReader(content))) {
            if (content != null) {
                while (true) {
                    String line = reader.readLine();
                    if (line == null) break;
                    ObjectMapper objectMapper = new ObjectMapper();
                    objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
                    objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
                    objectMapper.configure(JsonParser.Feature.INCLUDE_SOURCE_IN_LOCATION, true);
                    markdownContent.append(line).append("\n");
                }
            }
        } catch (IOException e) {
            log.error("getContentFromS3 error,s3Url is {}", s3Url, e);
            throw new RuntimeException(e);
        }
        return markdownContent.toString();
    }

    private String checkCreateCitadelDocReq(CreateCitadelDocReq req) {
        if (req == null) {
            return "请求参数为null";
        } else if (StringUtils.isBlank(req.getMisId())) {
            return "misId为空";
        } else if (StringUtils.isBlank(req.getContent()) && StringUtils.isBlank(req.getContentS3Url())) {
            return "文档内容为空，请给出文档内容或者markdown文档S3链接！";
        } else {
            return null;
        }
    }

    public McpServerResponse getCitadelContentJson(String misId, String url) {
        Transaction transaction = Cat.newTransaction(XUECHENG, "getCitadelContentJson");
        Cat.logEvent(XUECHENG, "getCitadelContentJson");
        log.info("getCitadelContentJson url is {}, misId is {}", url, misId);
        try {
            if (StringUtils.isBlank(url) || StringUtils.isBlank(misId)) {
                transaction.setStatus("url or misId is blank");
                log.error("getCitadelContentJson url or misId is blank,url is {},misId is {}", url, misId);
                return McpServerResponse.fail("url or misId is blank");
            }
            String contentId = StringUtils.substringAfter(url, "collabpage/");
            Long docId = null;
            if (!StringUtils.isBlank(contentId)) {
                docId = Long.valueOf(contentId);
            }
            String ssoid = tairClient.get(XUECHENG_KEY + misId);
            if (StringUtils.isBlank(ssoid)) {
                transaction.setStatus("ssoid is blank");
                log.error("getCitadelContentJson ssoid is blank,url is {},misId is {}", url, misId);
                return McpServerResponse.fail("ssoid is blank,请登录BML门户更新sso信息!");
            }
            GetCollaborationContentBySsoReq req = new GetCollaborationContentBySsoReq();
            req.setContentId(docId);
            GetCollaborationContentBySsoResp resp = citadelService.getCollaborationContentBySso(xmAuthServiceWrapper.getToken(), ssoid, req);
            if (resp != null && resp.getStatus() != null && resp.getStatus().getCode() == 0 && resp.getData().getSuccess()) {
                transaction.setSuccessStatus();
                String content = resp.getData().getContent();
                log.info("getCitadelContentJson success,url is {},misId is {},content:{}", url, misId, content);
                return McpServerResponse.success("成功读取学城文档！", content);
            } else {
                transaction.setStatus("getCitadelContentJson failed");
                log.error("getCitadelContentJson failed,url is {},misId is {},resp:{}", url, misId, JSONObject.toJSONString(resp));
                return McpServerResponse.fail("读取学城文档失败！");
            }
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("获取学城文档内容失败! url:{}", url);
            String advice = generalProcessingService.exceptionAnalysis(e);
            if (StringUtils.isBlank(advice)) {
                advice = "请联系mcpserver负责人或者zhouzehao02";
            }
            String msg = "获取学城文档内容失败!发起人:" + misId + "报错信息:" + e.getMessage() + "指导建议:" + advice;
            dxService.sendMsg2Users(msg, Lists.newArrayList(misId, "zhouzehao"), null);
            return McpServerResponse.fail("获取学城文档内容失败!" + e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    private String createDoc(CitadelDoc citadelDoc) throws Exception {
        String accessToken = xmAuthServiceWrapper.getToken();
        if (StringUtils.isBlank(accessToken)) {
            throw new Exception("accessToken is blank");
        }
        long operatorEmpId = employInfoProxy.getEmployId(citadelDoc.getMis());
        if (operatorEmpId == 0) {
            throw new Exception("operatorEmpId is 0");
        }
        
        log.info("createDoc 开始转换Markdown为学城文档，原始内容长度: {}", citadelDoc.getContent().length());
        
        // 添加重试机制和内容完整性验证
        String content = null;
        int maxRetries = 3;
        int retryCount = 0;
        boolean conversionSuccess = false;
        
        while (!conversionSuccess && retryCount < maxRetries) {
            try {
                content = MarkdownToCitadelConverter.convertMarkdownToXuecheng(citadelDoc.getContent());
                
                // 验证转换后的内容是否完整
                if (isContentComplete(citadelDoc.getContent(), content)) {
                    conversionSuccess = true;
                    log.info("createDoc 转换成功，尝试次数: {}", retryCount + 1);
                } else {
                    log.warn("createDoc 转换结果不完整，准备重试，当前尝试次数: {}", retryCount + 1);
                    retryCount++;
                    Thread.sleep(50);
                }
            } catch (Exception e) {
                log.error("createDoc 转换异常，准备重试，当前尝试次数: {}", retryCount + 1, e);
                retryCount++;
                if (retryCount >= maxRetries) {
                    throw e;
                }
                Thread.sleep(50);
            }
        }
        
        if (!conversionSuccess) {
            throw new Exception("Markdown转换失败，内容不完整，已重试" + maxRetries + "次");
        }
        
        log.info("createDoc 转换后JSON长度: {}", content.length());
        if (log.isDebugEnabled()) {
            log.debug("createDoc 转换后JSON内容: {}", content);
        }
        
        String docId = xmCitadelProxy.createXueChengDocument(accessToken, operatorEmpId, citadelDoc.getTitle(), content, citadelDoc.getSpaceId(), citadelDoc.getParentId());
        log.info("citadelDoc is {},content:{},contentJson:{},result:{}", JSONObject.toJSONString(citadelDoc),citadelDoc.getContent(),content,docId);
        if (StringUtils.isBlank(docId)) {
            throw new Exception("docId is blank");
        }
        if (ServerHostEnvUtil.isProd() || ServerHostEnvUtil.isSt()) {
            return ONLINE_DOC_URL + docId;
        }
        return OFFLINE_DOC_URL + docId;
    }
    
    /**
     * 验证转换后的内容是否完整
     * @param originalMarkdown 原始Markdown内容
     * @param convertedJson 转换后的JSON内容
     * @return 内容是否完整
     */
    private boolean isContentComplete(String originalMarkdown, String convertedJson) {
        if (StringUtils.isBlank(convertedJson)) {
            return false;
        }
        
        try {
            // 1. 检查JSON格式是否有效
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
            objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
            Map<String, Object> jsonMap = objectMapper.readValue(convertedJson, Map.class);
            
            // 2. 检查基本结构
            if (!jsonMap.containsKey("type") || !jsonMap.containsKey("content")) {
                log.warn("转换后JSON缺少基本结构");
                return false;
            }
            
            // 3. 检查内容数组
            List<Map<String, Object>> contentList = (List<Map<String, Object>>) jsonMap.get("content");
            if (contentList == null || contentList.isEmpty()) {
                log.warn("转换后JSON内容为空");
                return false;
            }
            
            // 4. 检查关键部分是否存在
            // 分割原始Markdown按行
            String[] markdownLines = originalMarkdown.split("\n");
            
            // 检查标题是否存在
            boolean headingsFound = false;
            for (String line : markdownLines) {
                if (line.trim().startsWith("#")) {
                    headingsFound = true;
                    break;
                }
            }
            
            // 如果原始内容有标题，检查转换后是否有heading类型
            if (headingsFound) {
                boolean headingInJson = contentList.stream()
                    .anyMatch(item -> "heading".equals(item.get("type")));
                
                if (!headingInJson) {
                    log.warn("原始Markdown包含标题，但转换后JSON中未找到heading类型");
                    return false;
                }
            }
            
            // 5. 检查分隔线后的内容是否存在
            // 在原始Markdown中查找分隔线
            boolean hasSeparator = false;
            boolean hasContentAfterSeparator = false;
            
            for (int i = 0; i < markdownLines.length; i++) {
                if (markdownLines[i].trim().matches("^-{3,}$")) {
                    hasSeparator = true;
                    // 检查分隔线后是否还有内容
                    for (int j = i + 1; j < markdownLines.length; j++) {
                        if (!markdownLines[j].trim().isEmpty()) {
                            hasContentAfterSeparator = true;
                            break;
                        }
                    }
                    break;
                }
            }
            
            // 如果有分隔线且分隔线后有内容，检查JSON中是否有horizontal_rule类型
            if (hasSeparator && hasContentAfterSeparator) {
                boolean horizontalRuleFound = contentList.stream()
                    .anyMatch(item -> "horizontal_rule".equals(item.get("type")));
                
                if (!horizontalRuleFound) {
                    log.warn("原始Markdown包含分隔线，但转换后JSON中未找到horizontal_rule类型");
                    return false;
                }
            }
            
            // 6. 检查内容长度比例
            // 如果转换后的JSON长度小于原始Markdown长度的一定比例，可能表示内容不完整
            if (convertedJson.length() < originalMarkdown.length() * 0.8) {
                log.warn("转换后JSON长度明显小于原始Markdown，可能内容不完整");
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("验证转换内容完整性时发生异常", e);
            return false;
        }
    }

    public McpToolResponse<List<Map<String, Object>>> fetchSubDocLinkAndContentBasedOnParentDocId(XueChengRequest xueChengRequest) {
        log.info("fetchSubDocLinkAndContentBasedOnParentDocId start,XueChengRequest{}", JSONObject.toJSONString(xueChengRequest));
        Transaction transaction = Cat.newTransaction("DefaultToolService", "fetch_sub_doc_link_and_content_based_on_parent_doc_id");
        if(xueChengRequest == null || StringUtils.isBlank(xueChengRequest.getMisId()) || xueChengRequest.getContentId() == null || CollectionUtils.isEmpty(xueChengRequest.getContentTitlies())){
            transaction.setStatus("xueChengRequest is null");
            return McpToolResponse.paramsError("获取xuehceng文档失败！xueChengRequest入参错误！",null);
        }
        Long contentId = xueChengRequest.getContentId();
        String misId = xueChengRequest.getMisId();
        GetChildContentBySsoReq childContentBySsoReq = new GetChildContentBySsoReq();
        GetChildContentBySsoReq.Request request = new GetChildContentBySsoReq.Request();
        request.setContentId(contentId);

        childContentBySsoReq.setRequest(request);
        String token = xmAuthServiceWrapper.getToken();
        String userToken = tairClient.get(XUECHENG_KEY + misId);
        try {
            GetChildContentBySsoResp childContentBySso = citadelService.getChildContentBySso(token, userToken, childContentBySsoReq);
            if (childContentBySso == null || childContentBySso.getStatus() == null || childContentBySso.getStatus().getCode() != 0) {
                transaction.setStatus("childContentBySso is null");
                return McpToolResponse.fail("获取xuehceng文档失败！childContentBySso is null",null);
            }
            GetChildContentBySsoResp.Data data = childContentBySso.getData();
            Integer total = data.getTotal();
            List<GetChildContentBySsoResp.Data.ContentList> contentList = data.getContentList();
            if (CollectionUtils.isEmpty(contentList) || total <= 0) {
                transaction.setStatus("contentList is empty");
                return McpToolResponse.fail("获取xuehceng文档失败！contentList is empty",null);
            }

            List<String> contentTitlies = xueChengRequest.getContentTitlies();

            boolean prod = ServerHostEnvUtil.isProd();
            String preix = prod ? ONLINE_DOC_URL : OFFLINE_DOC_URL;
            Map<String, Long> timeCheck = new HashMap<>();
            List<Map<String, Object>> contentListRes = new ArrayList<>();
            for (GetChildContentBySsoResp.Data.ContentList content : contentList) {
                Map<String, Object> result = new HashMap<>();
                String title = content.getTitle();
                Long modifyTime = content.getModifyTime();
                if (!StringUtils.isBlank(title) && contentTitlies.contains(title)) {
                    if (timeCheck.containsKey(title) && modifyTime < (Long) timeCheck.get(title)) {
                        continue;
                    }
                    result.put("title", title);
                    String url = preix + content.getContentId();
                    result.put("contentId", url);
                    try {
                        McpServerResponse xuechengContentWithMarkdown = getXuechengContentWithMarkdown(url, misId);
                        if(xuechengContentWithMarkdown.getCode() != 0){
                            throw new Exception(xuechengContentWithMarkdown.getMessage());
                        }
                        String markdownData = xuechengContentWithMarkdown.getData();
                        result.put("content", markdownData);
                        timeCheck.put(title, modifyTime);
                        contentListRes.add(result);
                    } catch (Exception e) {
                        log.error("getXuechengContentWithMarkdown error,url is {},misId:{}", url, misId, e);
                        transaction.setStatus(e);
                        return McpToolResponse.fail("获取学城子文档内容失败！父文档Id:" + contentId + "子文档Id" + content.getContentId() + "errorMessage:" + e.getMessage(), null);
                    }
                }
            }
            transaction.setSuccessStatus();
            log.info("fetchSubDocLinkAndContentBasedOnParentDocId success,contentId is {},misId:{}", contentId, misId);
            return McpToolResponse.success("获取学城文档内容成功！", contentListRes);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("fetchSubDocLinkAndContentBasedOnParentDocId error,contentId is {},misId:{}", contentId, misId, e);
            return McpToolResponse.fail("获取学城子文档内容失败！父文档Id:" + contentId + "errorMessage:" + e.getMessage(), null);
        }
    }
}
