package com.sankuai.mcpserver.algoplat.mcpserver.domain.service.toolManager;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import org.springframework.ai.tool.ToolCallback;

import java.util.List;

/**
 * McpServer服务接口
 *
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
public interface McpServerService {

    /**
     * 获取所有McpServer
     *
     * @return McpServer列表
     */
    List<McpServerEntity> getAllMcpServers();

    /**
     * 根据ID获取McpServer
     *
     * @param id McpServer ID
     * @return McpServer实体
     */
    McpServerEntity getMcpServerById(Long id);

    /**
     * 根据业务线ID获取McpServer列表
     *
     * @param businessLineId 业务线ID
     * @return McpServer列表
     */
    List<McpServerEntity> getMcpServersByBusinessLineId(Long businessLineId);

    /**
     * 根据名称获取McpServer列表
     *
     * @param name McpServer名称
     * @return McpServer列表
     */
    List<McpServerEntity> getMcpServersByName(String name);

    /**
     * 创建McpServer
     *
     * @param mcpServerEntity McpServer实体
     * @return 创建后的McpServer实体
     */
    McpServerEntity createMcpServer(McpServerEntity mcpServerEntity);

    /**
     * 更新McpServer
     *
     * @param mcpServerEntity McpServer实体
     * @return 更新后的McpServer实体
     */
    McpServerEntity updateMcpServer(McpServerEntity mcpServerEntity);

    /**
     * 删除McpServer
     *
     * @param id McpServer ID
     * @return 是否删除成功
     */
    boolean deleteMcpServer(Long id);

    /**
     * 删除业务线下所有McpServer
     *
     * @param businessLineId 业务线ID
     * @return 删除的记录数
     */
    int deleteAllMcpServersByBusinessLineId(Long businessLineId);

    /**
     * 批量获取McpServer
     *
     * @param ids McpServer ID列表
     * @return McpServer列表
     */
    List<McpServerEntity> batchGetMcpServers(List<Long> ids);

    /**
     * 检查McpServer是否存在
     *
     * @param id McpServer ID
     * @return 是否存在
     */
    boolean existsMcpServer(Long id);

    /**
     * 检查指定名称的McpServer是否存在
     *
     * @param name McpServer名称
     * @return 是否存在
     */
    boolean existsMcpServerByName(String name);

    /**
     * 根据服务ID和服务负责人查询McpServer列表
     */
    List<McpServerEntity> selectByServerIdAndOwner(Long businessLineId, String owner);

    McpServerEntity saveDefaultToolForMcpServer(Long mcpServerId,List<String> useDefaultToolList) throws Exception;

    void deleteSystemDefaultTool(Long mcpServerId, String toolName);

    void registerToolForMcpServer(ToolCallback toolCallback, BusinessLine bussiness, McpServerEntity mcpServer);
}
