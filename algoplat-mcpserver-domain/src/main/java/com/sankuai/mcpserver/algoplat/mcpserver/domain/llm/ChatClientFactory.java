package com.sankuai.mcpserver.algoplat.mcpserver.domain.llm;

import com.dianping.cat.Cat;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.LionConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Configuration
public class ChatClientFactory {
    @Resource(name="chatClientBuilderBean")
    private ChatClient.Builder builder;


    @Resource(name="paramsBuilderBean")
    private ChatClient.Builder paramsBuilder;

    @Resource(name="toolExceptionAnalysisBuilderBean")
    private ChatClient.Builder toolExceptionAnalysisBuilder;

    // 缓存不同配置的ChatClient实例
    private static Map<String, ChatClient> clientCache = new ConcurrentHashMap<>();
    public ChatClientFactory() {
    }

    @PostConstruct
    public void init() {
        initClient();
    }

    private void initClient(){
       clientCache.put("defaultClient", builder.defaultSystem(LionConfig.rpcInterfaceDescSystemPromptTemplate).build());
       clientCache.put("paramClient", paramsBuilder.defaultSystem(LionConfig.paramsDescLLM).build());
       clientCache.put("toolExceptionAnalysis",toolExceptionAnalysisBuilder.defaultSystem(LionConfig.toolExceptionAnalysis).build());

    }
    // 获取默认的ChatClient
    public ChatClient getDefaultClient() {
        // 添加空检查
        if (builder == null) {
            log.error("ChatClient.Builder is null, cannot create default client");
            return null;
        }
        return clientCache.get("defaultClient");
    }

    public ChatClient getParamsClient() {
        // 添加空检查
        if (builder == null) {
            log.error("ChatClient.Builder is null, cannot create default client");
            return null;
        }
        return clientCache.get("paramClient");
    }

    public ChatClient getToolExceptionAnalysisClient() {
        // 添加空检查
        if (builder == null) {
            log.error("ChatClient.Builder is null, cannot create default client");
            Cat.logError(new Exception("ChatClient.Builder is null, cannot create default client"));
            return null;
        }
        return clientCache.get("toolExceptionAnalysis");
    }

}