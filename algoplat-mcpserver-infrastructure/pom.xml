<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.mcpserver</groupId>
        <artifactId>algoplat-mcpserver</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>algoplat-mcpserver-infrastructure</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>algoplat-mcpserver-infrastructure</name>

    <dependencies>

        <dependency>
            <groupId>com.sankuai.algoplatform.agentapi</groupId>
            <artifactId>apiserver-client</artifactId>
            <version>1.0.1</version>
        </dependency>
        <!-- TalosTwo SDK 依赖 -->
        <dependency>
            <groupId>com.meituan.talostwo</groupId>
            <artifactId>talostwo-sdk-java</artifactId>
            <version>1.8.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.data</groupId>
            <artifactId>zb-flow-platform-api</artifactId>
            <version>1.0.2-gj-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.algoplatform</groupId>
            <artifactId>algoplat-matchops-api</artifactId>
            <version>0.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cellar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-s3plus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-openai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-mcp-server-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
            <version>9.6</version>
        </dependency>
        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm-tree</artifactId>
            <version>9.6</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.spider</groupId>
            <artifactId>spider-database-api</artifactId>
            <version>2.1.44-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.sankuai.zb</groupId>
            <artifactId>schedule-manageplatform-api</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.ead</groupId>
            <artifactId>citadel-client</artifactId>
            <version>3.0.56</version>
        </dependency>
        <dependency>
            <groupId>org.commonmark</groupId>
            <artifactId>commonmark</artifactId>
            <version>0.21.0</version>
        </dependency>

        <!-- commonmark table extension -->
        <dependency>
            <groupId>org.commonmark</groupId>
            <artifactId>commonmark-ext-gfm-tables</artifactId>
            <version>0.21.0</version>
        </dependency>

        <!-- Auto link extension -->
        <dependency>
            <groupId>org.commonmark</groupId>
            <artifactId>commonmark-ext-autolink</artifactId>
            <version>0.21.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>udb-common</artifactId>
            <version>1.2.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
            <version>1.5.8</version>
            <scope>compile</scope>
        </dependency>

        <!-- Strikethrough extension -->
        <dependency>
            <groupId>org.commonmark</groupId>
            <artifactId>commonmark-ext-gfm-strikethrough</artifactId>
            <version>0.21.0</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.hbdata</groupId>
            <artifactId>mt-hbi-rpc-client</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.org</groupId>
            <artifactId>open-sdk</artifactId>
            <version>5.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
            <artifactId>open-sdk</artifactId>
            <version>1.0.47-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibhtp.os</groupId>
            <artifactId>htp-cms-knowledge-build-client</artifactId>
            <version>0.0.24</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.guiguzi</groupId>
            <artifactId>guiguzi-annotation-thrift</artifactId>
            <version>1.2.8</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
