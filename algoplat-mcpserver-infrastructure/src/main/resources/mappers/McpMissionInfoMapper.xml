<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.McpMissionInfoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mission_id" jdbcType="VARCHAR" property="missionId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="mission_request" jdbcType="CHAR" property="missionRequest" />
    <result column="mission_context" jdbcType="CHAR" property="missionContext" />
    <result column="mission_result" jdbcType="CHAR" property="missionResult" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="mis_id" jdbcType="VARCHAR" property="misId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mission_id, type, mission_request, mission_context, mission_result, status, mis_id,
    add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpMissionInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mcp_mission_execute_context_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mcp_mission_execute_context_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from mcp_mission_execute_context_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpMissionInfoExample">
    delete from mcp_mission_execute_context_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo">
    insert into mcp_mission_execute_context_info (id, mission_id, type,
      mission_request, mission_context, mission_result,
      status, mis_id, add_time,
      update_time)
    values (#{id,jdbcType=BIGINT}, #{missionId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
      #{missionRequest,jdbcType=CHAR}, #{missionContext,jdbcType=CHAR}, #{missionResult,jdbcType=CHAR}, 
      #{status,jdbcType=INTEGER}, #{misId,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo" useGeneratedKeys="true" keyProperty="id">
    insert into mcp_mission_execute_context_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="missionId != null">
        mission_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="missionRequest != null">
        mission_request,
      </if>
      <if test="missionContext != null">
        mission_context,
      </if>
      <if test="missionResult != null">
        mission_result,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="misId != null">
        mis_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="missionId != null">
        #{missionId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="missionRequest != null">
        #{missionRequest,jdbcType=CHAR},
      </if>
      <if test="missionContext != null">
        #{missionContext,jdbcType=CHAR},
      </if>
      <if test="missionResult != null">
        #{missionResult,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="misId != null">
        #{misId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpMissionInfoExample" resultType="java.lang.Long">
    select count(*) from mcp_mission_execute_context_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mcp_mission_execute_context_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.missionId != null">
        mission_id = #{row.missionId,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=INTEGER},
      </if>
      <if test="row.missionRequest != null">
        mission_request = #{row.missionRequest,jdbcType=CHAR},
      </if>
      <if test="row.missionContext != null">
        mission_context = #{row.missionContext,jdbcType=CHAR},
      </if>
      <if test="row.missionResult != null">
        mission_result = #{row.missionResult,jdbcType=CHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.misId != null">
        mis_id = #{row.misId,jdbcType=VARCHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mcp_mission_execute_context_info
    set id = #{row.id,jdbcType=BIGINT},
      mission_id = #{row.missionId,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=INTEGER},
      mission_request = #{row.missionRequest,jdbcType=CHAR},
      mission_context = #{row.missionContext,jdbcType=CHAR},
      mission_result = #{row.missionResult,jdbcType=CHAR},
      status = #{row.status,jdbcType=INTEGER},
      mis_id = #{row.misId,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo">
    update mcp_mission_execute_context_info
    <set>
      <if test="missionId != null">
        mission_id = #{missionId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="missionRequest != null">
        mission_request = #{missionRequest,jdbcType=CHAR},
      </if>
      <if test="missionContext != null">
        mission_context = #{missionContext,jdbcType=CHAR},
      </if>
      <if test="missionResult != null">
        mission_result = #{missionResult,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="misId != null">
        mis_id = #{misId,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo">
    update mcp_mission_execute_context_info
    set mission_id = #{missionId,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      mission_request = #{missionRequest,jdbcType=CHAR},
      mission_context = #{missionContext,jdbcType=CHAR},
      mission_result = #{missionResult,jdbcType=CHAR},
      status = #{status,jdbcType=INTEGER},
      mis_id = #{misId,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>