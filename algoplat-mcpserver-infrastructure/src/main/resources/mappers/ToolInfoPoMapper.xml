<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.ToolInfoPoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ToolInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="tool_uuid" jdbcType="CHAR" property="toolUuid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="http_tool_id" jdbcType="BIGINT" property="httpToolId" />
    <result column="thrift_tool_id" jdbcType="BIGINT" property="thriftToolId" />
    <result column="pigeon_tool_id" jdbcType="BIGINT" property="pigeonToolId" />
    <result column="tool_params" jdbcType="VARCHAR" property="toolParams" typeHandler="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.ToolParameterListTypeHandler" />
    <result column="time_out" jdbcType="INTEGER" property="timeOut" />
    <result column="llm_gen_description" jdbcType="TINYINT" property="llmGenDescription" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="tool_version" jdbcType="INTEGER" property="toolVersion" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mcp_server_id" jdbcType="BIGINT" property="mcpServerId" />
    <result column="is_long_term_task" jdbcType="INTEGER" property="isLongTermTask" />
    <result column="long_term_task_type" jdbcType="INTEGER" property="longTermTaskType" />
    <result column="avitor_script_code" jdbcType="LONGVARCHAR" property="avitorScriptCode" />
    <result column="text_type" jdbcType="TINYINT" property="textType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_line, tool_uuid, name, description, type, http_tool_id, thrift_tool_id,
    pigeon_tool_id, tool_params, time_out, llm_gen_description, status, tool_version,
    owner, add_time, update_time, mcp_server_id, is_long_term_task, long_term_task_type, avitor_script_code, text_type
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.ToolInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bml_mcp_server_tools_register
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bml_mcp_server_tools_register
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bml_mcp_server_tools_register
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.ToolInfoPoExample">
    delete from bml_mcp_server_tools_register
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ToolInfoPo"
          useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into bml_mcp_server_tools_register (id, business_line, tool_uuid,
      name, description, type,
      http_tool_id, thrift_tool_id, pigeon_tool_id,
      tool_params, time_out, llm_gen_description,
      status, tool_version, owner,
      add_time, update_time, mcp_server_id, is_long_term_task, long_term_task_type, avitor_script_code, text_type)
    values (#{id,jdbcType=BIGINT}, #{businessLine,jdbcType=VARCHAR}, #{toolUuid,jdbcType=CHAR},
      #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
      #{httpToolId,jdbcType=BIGINT}, #{thriftToolId,jdbcType=BIGINT}, #{pigeonToolId,jdbcType=BIGINT},
      #{toolParams,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.ToolParameterListTypeHandler}, #{timeOut,jdbcType=INTEGER}, #{llmGenDescription,jdbcType=TINYINT},
      #{status,jdbcType=TINYINT}, #{toolVersion,jdbcType=INTEGER}, #{owner,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{mcpServerId,jdbcType=BIGINT}, #{isLongTermTask,jdbcType=INTEGER}, #{longTermTaskType,jdbcType=INTEGER}, #{avitorScriptCode,jdbcType=LONGVARCHAR}, #{textType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ToolInfoPo" useGeneratedKeys="true" keyProperty="id">
    insert into bml_mcp_server_tools_register
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessLine != null">
        business_line,
      </if>
      <if test="toolUuid != null">
        tool_uuid,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="httpToolId != null">
        http_tool_id,
      </if>
      <if test="thriftToolId != null">
        thrift_tool_id,
      </if>
      <if test="pigeonToolId != null">
        pigeon_tool_id,
      </if>
      <if test="toolParams != null">
        tool_params,
      </if>
      <if test="timeOut != null">
        time_out,
      </if>
      <if test="llmGenDescription != null">
        llm_gen_description,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="toolVersion != null">
        tool_version,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="mcpServerId != null">
        mcp_server_id,
      </if>
      <if test="isLongTermTask != null">
        is_long_term_task,
      </if>
      <if test="longTermTaskType != null">
        long_term_task_type,
      </if>
        <if test="avitorScriptCode != null">
        avitor_script_code,
      </if>
      <if test="textType != null">
        text_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessLine != null">
        #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="toolUuid != null">
        #{toolUuid,jdbcType=CHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="httpToolId != null">
        #{httpToolId,jdbcType=BIGINT},
      </if>
      <if test="thriftToolId != null">
        #{thriftToolId,jdbcType=BIGINT},
      </if>
      <if test="pigeonToolId != null">
        #{pigeonToolId,jdbcType=BIGINT},
      </if>
      <if test="toolParams != null">
        #{toolParams,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.ToolParameterListTypeHandler},
      </if>
      <if test="timeOut != null">
        #{timeOut,jdbcType=INTEGER},
      </if>
      <if test="llmGenDescription != null">
        #{llmGenDescription,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="toolVersion != null">
        #{toolVersion,jdbcType=INTEGER},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mcpServerId != null">
        #{mcpServerId,jdbcType=BIGINT},
      </if>
      <if test="isLongTermTask != null">
        #{isLongTermTask,jdbcType=INTEGER},
      </if>
      <if test="longTermTaskType != null">
        #{longTermTaskType,jdbcType=INTEGER},
      </if>
      <if test="avitorScriptCode != null">
        #{avitorScriptCode,jdbcType=LONGVARCHAR},
      </if>
      <if test="textType != null">
        #{textType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.ToolInfoPoExample" resultType="java.lang.Long">
    select count(*) from bml_mcp_server_tools_register
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bml_mcp_server_tools_register
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.businessLine != null">
        business_line = #{row.businessLine,jdbcType=VARCHAR},
      </if>
      <if test="row.toolUuid != null">
        tool_uuid = #{row.toolUuid,jdbcType=CHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.httpToolId != null">
        http_tool_id = #{row.httpToolId,jdbcType=BIGINT},
      </if>
      <if test="row.thriftToolId != null">
        thrift_tool_id = #{row.thriftToolId,jdbcType=BIGINT},
      </if>
      <if test="row.pigeonToolId != null">
        pigeon_tool_id = #{row.pigeonToolId,jdbcType=BIGINT},
      </if>
      <if test="row.toolParams != null">
        tool_params = #{row.toolParams,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.ToolParameterListTypeHandler},
      </if>
      <if test="row.timeOut != null">
        time_out = #{row.timeOut,jdbcType=INTEGER},
      </if>
      <if test="row.llmGenDescription != null">
        llm_gen_description = #{row.llmGenDescription,jdbcType=TINYINT},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.toolVersion != null">
        tool_version = #{row.toolVersion,jdbcType=INTEGER},
      </if>
      <if test="row.owner != null">
        owner = #{row.owner,jdbcType=VARCHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.mcpServerId != null">
        mcp_server_id = #{row.mcpServerId,jdbcType=BIGINT},
      </if>
      <if test="row.isLongTermTask != null">
        is_long_term_task = #{row.isLongTermTask,jdbcType=INTEGER},
      </if>
      <if test="row.longTermTaskType != null">
        long_term_task_type = #{row.longTermTaskType,jdbcType=INTEGER},
      </if>
      <if test="row.avitorScriptCode != null">
        avitor_script_code = #{row.avitorScriptCode,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.textType != null">
        text_type = #{row.textType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bml_mcp_server_tools_register
    set id = #{row.id,jdbcType=BIGINT},
      business_line = #{row.businessLine,jdbcType=VARCHAR},
      tool_uuid = #{row.toolUuid,jdbcType=CHAR},
      name = #{row.name,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      http_tool_id = #{row.httpToolId,jdbcType=BIGINT},
      thrift_tool_id = #{row.thriftToolId,jdbcType=BIGINT},
      pigeon_tool_id = #{row.pigeonToolId,jdbcType=BIGINT},
      tool_params = #{row.toolParams,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.ToolParameterListTypeHandler},
      time_out = #{row.timeOut,jdbcType=INTEGER},
      llm_gen_description = #{row.llmGenDescription,jdbcType=TINYINT},
      status = #{row.status,jdbcType=TINYINT},
      tool_version = #{row.toolVersion,jdbcType=INTEGER},
      owner = #{row.owner,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      mcp_server_id = #{row.mcpServerId,jdbcType=BIGINT},
      is_long_term_task = #{row.isLongTermTask,jdbcType=INTEGER},
      long_term_task_type = #{row.longTermTaskType,jdbcType=INTEGER},
      avitor_script_code = #{row.avitorScriptCode,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ToolInfoPo">
    update bml_mcp_server_tools_register
    <set>
      <if test="businessLine != null">
        business_line = #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="toolUuid != null">
        tool_uuid = #{toolUuid,jdbcType=CHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="httpToolId != null">
        http_tool_id = #{httpToolId,jdbcType=BIGINT},
      </if>
      <if test="thriftToolId != null">
        thrift_tool_id = #{thriftToolId,jdbcType=BIGINT},
      </if>
      <if test="pigeonToolId != null">
        pigeon_tool_id = #{pigeonToolId,jdbcType=BIGINT},
      </if>
      <if test="toolParams != null">
        tool_params = #{toolParams,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.ToolParameterListTypeHandler},
      </if>
      <if test="timeOut != null">
        time_out = #{timeOut,jdbcType=INTEGER},
      </if>
      <if test="llmGenDescription != null">
        llm_gen_description = #{llmGenDescription,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="toolVersion != null">
        tool_version = #{toolVersion,jdbcType=INTEGER},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mcpServerId != null">
        mcp_server_id = #{mcpServerId,jdbcType=BIGINT},
      </if>
      <if test="isLongTermTask != null">
        is_long_term_task = #{isLongTermTask,jdbcType=INTEGER},
      </if>
      <if test="longTermTaskType != null">
        long_term_task_type = #{longTermTaskType,jdbcType=INTEGER},
      </if>
      <if test="avitorScriptCode != null">
        avitor_script_code = #{avitorScriptCode,jdbcType=LONGVARCHAR},
      </if>
      <if test="textType != null">
        text_type = #{textType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ToolInfoPo">
    update bml_mcp_server_tools_register
    set business_line = #{businessLine,jdbcType=VARCHAR},
      tool_uuid = #{toolUuid,jdbcType=CHAR},
      name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      http_tool_id = #{httpToolId,jdbcType=BIGINT},
      thrift_tool_id = #{thriftToolId,jdbcType=BIGINT},
      pigeon_tool_id = #{pigeonToolId,jdbcType=BIGINT},
      tool_params = #{toolParams,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.ToolParameterListTypeHandler},
      time_out = #{timeOut,jdbcType=INTEGER},
      llm_gen_description = #{llmGenDescription,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      tool_version = #{toolVersion,jdbcType=INTEGER},
      owner = #{owner,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      mcp_server_id = #{mcpServerId,jdbcType=BIGINT},
      is_long_term_task = #{isLongTermTask,jdbcType=INTEGER},
      long_term_task_type = #{longTermTaskType,jdbcType=INTEGER},
      avitor_script_code = #{avitorScriptCode,jdbcType=LONGVARCHAR},
      text_type = #{textType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>