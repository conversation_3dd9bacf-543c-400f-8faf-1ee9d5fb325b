<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.McpServerEntityPoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_line_id" jdbcType="BIGINT" property="businessLineId" />
    <result column="mcp_server_name" jdbcType="VARCHAR" property="mcpServerName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="default_tool_name_list" jdbcType="VARCHAR" property="defaultToolNameList"
            typeHandler="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringListTypeHandler" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="default_tool_name_list" jdbcType="VARCHAR" property="defaultToolNameList"
           typeHandler="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringListTypeHandler" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_line_id, mcp_server_name, status, owner, add_time, update_time,description, default_tool_name_list
  </sql>
  <sql id="Blob_Column_List">
    description, default_tool_name_list
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpServerEntityPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from bml_mcp_server
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpServerEntityPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from bml_mcp_server
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from bml_mcp_server
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bml_mcp_server
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpServerEntityPoExample">
    delete from bml_mcp_server
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo">
    insert into bml_mcp_server (id, business_line_id, mcp_server_name,
      status, owner, add_time,
      update_time, description, default_tool_name_list)
    values (#{id,jdbcType=BIGINT}, #{businessLineId,jdbcType=BIGINT}, #{mcpServerName,jdbcType=VARCHAR},
      #{status,jdbcType=TINYINT}, #{owner,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{description,jdbcType=LONGVARCHAR},
      #{defaultToolNameList,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringListTypeHandler})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo" useGeneratedKeys="true" keyProperty="id">
    insert into bml_mcp_server
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessLineId != null">
        business_line_id,
      </if>
      <if test="mcpServerName != null">
        mcp_server_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="owner != null">
        owner,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="defaultToolNameList != null">
        default_tool_name_list,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessLineId != null">
        #{businessLineId,jdbcType=BIGINT},
      </if>
      <if test="mcpServerName != null">
        #{mcpServerName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="owner != null">
        #{owner,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="defaultToolNameList != null">
        #{defaultToolNameList,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringListTypeHandler},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpServerEntityPoExample" resultType="java.lang.Long">
    select count(*) from bml_mcp_server
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bml_mcp_server
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.businessLineId != null">
        business_line_id = #{row.businessLineId,jdbcType=BIGINT},
      </if>
      <if test="row.mcpServerName != null">
        mcp_server_name = #{row.mcpServerName,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.owner != null">
        owner = #{row.owner,jdbcType=VARCHAR},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update bml_mcp_server
    set id = #{row.id,jdbcType=BIGINT},
      business_line_id = #{row.businessLineId,jdbcType=BIGINT},
      mcp_server_name = #{row.mcpServerName,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      owner = #{row.owner,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      description = #{row.description,jdbcType=LONGVARCHAR},
      default_tool_name_list = #{row.defaultToolNameList,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringListTypeHandler}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bml_mcp_server
    set id = #{row.id,jdbcType=BIGINT},
      business_line_id = #{row.businessLineId,jdbcType=BIGINT},
      mcp_server_name = #{row.mcpServerName,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      owner = #{row.owner,jdbcType=VARCHAR},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo">
    update bml_mcp_server
    <set>
      <if test="businessLineId != null">
        business_line_id = #{businessLineId,jdbcType=BIGINT},
      </if>
      <if test="mcpServerName != null">
        mcp_server_name = #{mcpServerName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="owner != null">
        owner = #{owner,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="defaultToolNameList != null">
        default_tool_name_list = #{defaultToolNameList,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringListTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo">
    update bml_mcp_server
    set business_line_id = #{businessLineId,jdbcType=BIGINT},
      mcp_server_name = #{mcpServerName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      owner = #{owner,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=LONGVARCHAR},
      default_tool_name_list = #{defaultToolNameList,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringListTypeHandler}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo">
    update bml_mcp_server
    set business_line_id = #{businessLineId,jdbcType=BIGINT},
      mcp_server_name = #{mcpServerName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      owner = #{owner,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>