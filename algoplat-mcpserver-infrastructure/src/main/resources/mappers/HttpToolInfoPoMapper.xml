<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.HttpToolInfoPoMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.HttpToolInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="method_name" jdbcType="VARCHAR" property="methodName" />
    <result column="headers" jdbcType="VARCHAR" property="headers" typeHandler="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringMapTypeHandler" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, note, url, method_name, headers, status, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.HttpToolInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bml_mcp_server_tool_http_interface_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bml_mcp_server_tool_http_interface_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bml_mcp_server_tool_http_interface_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.HttpToolInfoPoExample">
    delete from bml_mcp_server_tool_http_interface_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.HttpToolInfoPo"
          useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into bml_mcp_server_tool_http_interface_info (id, note, url,
      method_name, headers, status,
      add_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{note,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
      #{methodName,jdbcType=VARCHAR}, #{headers,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringMapTypeHandler}, #{status,jdbcType=TINYINT},
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.HttpToolInfoPo" useGeneratedKeys="true" keyProperty="id">
    insert into bml_mcp_server_tool_http_interface_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="note != null">
        note,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="methodName != null">
        method_name,
      </if>
      <if test="headers != null">
        headers,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="note != null">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="headers != null">
        #{headers,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringMapTypeHandler},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.HttpToolInfoPoExample" resultType="java.lang.Long">
    select count(*) from bml_mcp_server_tool_http_interface_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bml_mcp_server_tool_http_interface_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.note != null">
        note = #{row.note,jdbcType=VARCHAR},
      </if>
      <if test="row.url != null">
        url = #{row.url,jdbcType=VARCHAR},
      </if>
      <if test="row.methodName != null">
        method_name = #{row.methodName,jdbcType=VARCHAR},
      </if>
      <if test="row.headers != null">
        headers = #{row.headers,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringMapTypeHandler},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bml_mcp_server_tool_http_interface_info
    set id = #{row.id,jdbcType=BIGINT},
      note = #{row.note,jdbcType=VARCHAR},
      url = #{row.url,jdbcType=VARCHAR},
      method_name = #{row.methodName,jdbcType=VARCHAR},
      headers = #{row.headers,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringMapTypeHandler},
      status = #{row.status,jdbcType=TINYINT},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.HttpToolInfoPo">
    update bml_mcp_server_tool_http_interface_info
    <set>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        method_name = #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="headers != null">
        headers = #{headers,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringMapTypeHandler},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.HttpToolInfoPo">
    update bml_mcp_server_tool_http_interface_info
    set note = #{note,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      method_name = #{methodName,jdbcType=VARCHAR},
      headers = #{headers,jdbcType=VARCHAR,typeHandler=com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler.StringMapTypeHandler},
      status = #{status,jdbcType=TINYINT},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>