package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ToolInfoPo;
import lombok.Data;

import java.util.Map;

@Data
public class ToolInfo extends ToolInfoPo {

    /**
     * 是否为长连接任务 1-是
     */
    public static final Integer IS_LONG_TERM_TASK_TRUE = 1;

    private Boolean usedInDefault;

    public Map<String, Object> convertToMap() {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.convertValue(this, Map.class);

    }
}
