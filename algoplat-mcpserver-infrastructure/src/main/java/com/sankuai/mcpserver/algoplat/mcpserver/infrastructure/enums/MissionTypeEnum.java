package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * McpMissionType
 */
@Getter
@AllArgsConstructor
public enum MissionTypeEnum {
    /**
     * friday相关的任务
     */
    FRIDAY(1, "FRIDAY"),
    /**
     * 测试工具-badcase率、一致性校验
     */
    CHECK(2, "CHECK"),
    /**
     * 测试工具-模型压测
     */
    STRESS(3, "STRESS"),
    /**
     * Thrift长任务
     */
    THRIFT(4, "THRIFT"),

    /**
     * 自动唤醒Agent长任务
     */
    AUTO_AGENT(5, "AUTO_AGENT");

    /**
     * 枚举值
     */
    private final Integer code;
    private final String value;
}
