package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.HttpToolInfoPoExample;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.HttpToolInfoPoMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.HttpToolInfoPo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.HttpToolInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/7
 */
@Service
public class HttpToolInfoDao {
    @Resource
    private HttpToolInfoPoMapper httpToolInfoPoMapper;

    public HttpToolInfo getHttpToolInfoByToolId(Long id) {
        HttpToolInfoPo httpToolInfoPo = httpToolInfoPoMapper.selectByPrimaryKey(id);
        return convert(httpToolInfoPo);
    }
    private HttpToolInfo convert(HttpToolInfoPo httpToolInfoPo){
        if (Objects.isNull(httpToolInfoPo)) {
            return null;
        }
        HttpToolInfo httpToolInfo = new HttpToolInfo();
        BeanUtils.copyProperties(httpToolInfoPo, httpToolInfo);
        return httpToolInfo;
    }
    public Long insert(HttpToolInfoPo HttpToolInfoPo){
        if (Objects.isNull(HttpToolInfoPo) ) {
            return null;
        }
        HttpToolInfoPo.setId(null);
        httpToolInfoPoMapper.insertSelective(HttpToolInfoPo);
        String url = HttpToolInfoPo.getUrl();
        String methodName = HttpToolInfoPo.getMethodName();
        HttpToolInfoPo httpToolInfoPo = selectByInfo(url, methodName);
        if(Objects.isNull(httpToolInfoPo)){
            return null;
        }
        Long id = httpToolInfoPo.getId();
        return id;
    }

    public HttpToolInfoPo selectByInfo(String url, String methodName){
        HttpToolInfoPoExample example = new HttpToolInfoPoExample();
        HttpToolInfoPoExample.Criteria criteria = example.createCriteria();
        criteria.andUrlEqualTo(url);
        criteria.andMethodNameEqualTo(methodName);
        criteria.andStatusEqualTo(1);
        List<HttpToolInfoPo> httpToolInfoPos = httpToolInfoPoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(httpToolInfoPos)) {
            return null;
        }
        return httpToolInfoPos.get(0);
    }

    public HttpToolInfoPo getHttpToolInfoPoByTo2olId(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        HttpToolInfoPo httpToolInfoPo = httpToolInfoPoMapper.selectByPrimaryKey(id);
        return httpToolInfoPo;
    }
    public boolean deleteById(Long id){
        if (Objects.isNull(id)) {
            return false;
        }
        HttpToolInfoPo httpToolInfoPoByTo2olId = getHttpToolInfoPoByTo2olId(id);
        httpToolInfoPoByTo2olId.setStatus(0);
        httpToolInfoPoByTo2olId.setUpdateTime(new Date());
        int i = httpToolInfoPoMapper.updateByPrimaryKeySelective(httpToolInfoPoByTo2olId);
        return i > 0;

    }
}
