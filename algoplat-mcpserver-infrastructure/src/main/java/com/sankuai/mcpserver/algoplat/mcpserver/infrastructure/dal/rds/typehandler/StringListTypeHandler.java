package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * 专门用于处理String列表的TypeHandler
 * 主要用于处理default_tool_name_list字段
 * <AUTHOR>
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(String.class)
public class StringListTypeHandler extends JsonListTypeHandler<String> {
    public StringListTypeHandler() {
        super(String.class);
    }
}