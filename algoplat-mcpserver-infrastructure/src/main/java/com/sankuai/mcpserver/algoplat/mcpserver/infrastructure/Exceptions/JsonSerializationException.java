package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Exceptions;

public class JsonSerializationException extends RuntimeException {
    public JsonSerializationException() {
        super();
    }

    public JsonSerializationException(String message) {
        super(message);
    }

    public JsonSerializationException(String message, Throwable cause) {
        super(message, cause);
    }

    public JsonSerializationException(Throwable cause) {
        super(cause);
    }

    protected JsonSerializationException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
