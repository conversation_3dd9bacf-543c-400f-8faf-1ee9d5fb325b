package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: bml_mcp_business_line
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessLinePo {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: business_line
     *   说明: 业务线名称
     */
    private String businessLine;

    /**
     *   字段: status
     *   说明: 0-不启用 1-启用
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 记录创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 记录更新时间
     */
    private Date updateTime;

    /**
     *   字段: description
     *   说明: 业务线描述
     */
    private String description;
}