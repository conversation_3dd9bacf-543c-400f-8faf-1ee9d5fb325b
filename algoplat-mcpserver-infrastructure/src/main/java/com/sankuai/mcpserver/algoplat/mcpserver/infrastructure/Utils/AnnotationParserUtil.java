package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.alibaba.fastjson.JSONObject;
import org.objectweb.asm.ClassReader;
import org.objectweb.asm.Type;
import org.objectweb.asm.tree.AnnotationNode;
import org.objectweb.asm.tree.ClassNode;
import org.objectweb.asm.tree.MethodNode;

import java.util.*;

/**
 * @program: testnew
 * <AUTHOR>
 * @Date 2025/4/8
 */
public class AnnotationParserUtil {

    public static Map<String, Object> processAnnInfo(String classAllPath,String processedMethodName) {
        if(processedMethodName==null){
            return null;
        }
        try {
            ClassReader reader = new ClassReader(classAllPath);
            ClassNode cn = new ClassNode();
            reader.accept(cn, 0);
            Map<String, Object> result = new LinkedHashMap<>();
            List<Map<String,Object>> methodAnnDocList = new ArrayList<>();

            // 处理类级别的注解
            List<AnnotationNode> annotations = cn.invisibleAnnotations;
            if (annotations != null) {
                for (AnnotationNode an : annotations) {
                    String anno = an.desc.replaceAll("/", ".");
                    String annoName = anno.substring(1, anno.length()-1);
                    if ("com.meituan.servicecatalog.api.annotations.InterfaceDoc".equals(annoName)) {
                        String className = cn.name.replaceAll("/", ".");
                        Map<String, Object> map = convertKeyValueListToMap(an.values);
                        map.put("type", className);
                        result.put("interfaceAnnDoc", map);
                    }
                }
            }

            // 处理方法级别的注解
            if (cn.methods != null) {
                for (MethodNode methodNode : cn.methods) {
                    String methodName = methodNode.name;
                    // 获取方法上的注解
                    if (methodName.equals(processedMethodName) && methodNode.invisibleAnnotations != null) {
                        for (AnnotationNode an : methodNode.invisibleAnnotations) {
                            String anno = an.desc.replaceAll("/", ".");
                            String annoName = anno.substring(1, anno.length()-1);
                            if ("com.meituan.servicecatalog.api.annotations.MethodDoc".equals(annoName)) {
                                // 创建一个完整的方法信息Map
                                Map<String, Object> methodInfo = new HashMap<>();
                                methodInfo.put("methodName", methodName);
                                // 处理MethodDoc注解的所有属性
                                Map<String, Object> methodDocInfo = processMethodDocAnnotation(an.values);
                                methodInfo.put("methodDoc", methodDocInfo);
                                methodAnnDocList.add(methodInfo);
                            }
                        }

                    }
                }
            }
            result.put("methodAnnDocList", JSONObject.toJSONString(methodAnnDocList));
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 处理MethodDoc注解的所有属性
     * @param values 注解值列表
     * @return 包含注解所有属性的Map
     */
    private static Map<String, Object> processMethodDocAnnotation(List<Object> values) {
        Map<String, Object> methodDocInfo = new HashMap<>();
        if (values == null) {
            return methodDocInfo;
        }
        for (int i = 0; i < values.size(); i += 2) {
            if (i + 1 >= values.size()) {
                break;
            }
            Object key = values.get(i);
            Object value = values.get(i + 1);
            if (key == null) {
                continue;
            }
            String keyStr = key.toString();
            if ("displayName".equals(keyStr) || "description".equals(keyStr) || "returnValueDescription".equals(keyStr)) {
                methodDocInfo.put(keyStr, value.toString());
            }
            // 处理requestMethods字段，它是一个枚举数组
            else if ("requestMethods".equals(keyStr) && value instanceof List) {
                List<?> methodsList = (List<?>) value;
                List<String> httpMethods = new ArrayList<>();

                for (Object methodObj : methodsList) {
                    if (methodObj instanceof String[]) {
                        String[] enumValue = (String[]) methodObj;
                        if (enumValue.length >= 2) {
                            // 枚举值通常以[枚举类型, 枚举常量]的形式存储
                            httpMethods.add(enumValue[1]);
                        }
                    } else if (methodObj instanceof Type) {
                        // 有时枚举可能以Type形式存储
                        httpMethods.add(((Type) methodObj).getClassName());
                    } else {
                        httpMethods.add(methodObj.toString());
                    }
                }

                methodDocInfo.put("requestMethods", httpMethods);
            }
            // 处理parameters字段，它是一个ParamDoc注解数组
            else if ("parameters".equals(keyStr) && value instanceof List) {
                List<?> parametersList = (List<?>) value;
                List<Map<String, Object>> paramDocs = new ArrayList<>();

                for (Object paramObj : parametersList) {
                    if (paramObj instanceof AnnotationNode) {
                        AnnotationNode paramAnnotation = (AnnotationNode) paramObj;
                        Map<String, Object> paramMap = convertKeyValueListToMap(paramAnnotation.values);
                        paramDocs.add(paramMap);
                    }
                }

                methodDocInfo.put("parameters", JSONObject.toJSONString(paramDocs));
            }
        }

        return methodDocInfo;
    }


    private static Map<String, Object> convertKeyValueListToMap(List<Object> keyValueList) {
        Map<String, Object> resultMap = new HashMap<>();

        if (keyValueList == null || keyValueList.size() % 2 != 0) {
            return resultMap; // 返回空Map或根据需求抛出异常
        }
        for (int i = 0; i < keyValueList.size(); i += 2) {
            Object keyObj = keyValueList.get(i);
            Object valueObj = keyValueList.get(i + 1);
            if(keyObj!=null){
                String key = keyObj.toString();
                resultMap.put(key, valueObj);
            }
        }
        return resultMap;
    }
}
