package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 *   表名: mcp_mission_execute_context_info
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class McpMissionInfo {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: mission_id
     *   说明: 任务执行唯一标识
     */
    private String missionId;

    /**
     *   字段: type
     *   说明: 任务类型
     */
    private Integer type;

    /**
     *   字段: mission_request
     *   说明: 任务请求入参
     */
    private String missionRequest;

    /**
     *   字段: mission_context
     *   说明: 用于存储任务执行的上下文信息
     */
    private String missionContext;

    /**
     *   字段: mission_result
     *   说明: 任务执行结果
     */
    private String missionResult;

    /**
     *   字段: status
     *   说明: 任务执行状态
     */
    private Integer status;

    /**
     *   字段: mis_id
     *   说明: 任务发起人MisId
     */
    private String misId;

    /**
     *   字段: add_time
     *   说明: 记录创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 记录更新时间
     */
    private Date updateTime;

    public McpMissionInfo addId(Long id) {
        this.id = id;
        return this;
    }
    // 链式调用方法
    public McpMissionInfo addMissionId(String missionId) {
        this.missionId = missionId;
        return this;
    }

    public McpMissionInfo addType(Integer type) {
        this.type = type;
        return this;
    }

    public McpMissionInfo addMissionRequest(String missionRequest) {
        this.missionRequest = missionRequest;
        return this;
    }

    public McpMissionInfo addMissionContext(String missionContext) {
        this.missionContext = missionContext;
        return this;
    }

    public McpMissionInfo addMissionResult(String missionResult) {
        this.missionResult = missionResult;
        return this;
    }

    public McpMissionInfo addStatus(Integer status) {
        this.status = status;
        return this;
    }

    public McpMissionInfo addMisId(String misId) {
        this.misId = misId;
        return this;
    }

    public McpMissionInfo addAddTime(Date addTime) {
        this.addTime = addTime;
        return this;
    }

    public McpMissionInfo addUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }
}