package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs;

import com.dianping.cat.Cat;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class KmsConfigs {

    private static final String TALOS_KEY = "talos.password";

    public static String getTalosKey() {
        return getKmsValueByKey(TALOS_KEY);
    }

    public static String getKmsValueByKey(String key) {
        try {
            return Kms.getByName(MdpContextUtils.getAppKey(), key);
        } catch (KmsResultNullException e) {
            Cat.logError(e.getMessage(), e);
            log.error("获取KMS秘钥失败", e);
        }
        return "";
    }
}
