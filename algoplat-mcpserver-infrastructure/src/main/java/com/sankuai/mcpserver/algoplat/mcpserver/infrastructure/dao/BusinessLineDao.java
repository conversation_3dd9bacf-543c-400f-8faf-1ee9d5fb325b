package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao;

import com.dianping.cat.Cat;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.BusinessLinePoExample;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.BusinessLinePoMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.BusinessLinePo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.BusinessLine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务线数据访问对象
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Slf4j
@Service
public class BusinessLineDao {

    @Resource
    private BusinessLinePoMapper businessLinePoMapper;

    /**
     * 获取所有状态为1的业务线
     * @return 所有可用的业务线列表
     */
    public List<BusinessLine> getAllBusinessLine() {
        try {
            BusinessLinePoExample example = new BusinessLinePoExample();
            example.createCriteria().andStatusEqualTo(1);
            List<BusinessLinePo> businessLinePos = businessLinePoMapper.selectByExample(example);
            return businessLinePos.stream().map(this::convert).collect(Collectors.toList());
        } catch (Exception e) {
            
            log.error("获取所有业务线失败", e);
            return null;
        }
    }

    /**
     * 根据ID获取业务线
     * @param id 业务线ID
     * @return 业务线实体
     */
    public BusinessLine getBusinessLineById(Long id) {
        try {
            if (id == null) {
                log.warn("获取业务线时ID不能为空");
                return null;
            }
            BusinessLinePo businessLinePo = businessLinePoMapper.selectByPrimaryKey(id);
            return convert(businessLinePo);
        } catch (Exception e) {
            
            log.error("根据ID获取业务线失败, id={}", id, e);
            return null;
        }
    }

    /**
     * 插入新的业务线
     * @param businessLine 业务线实体
     * @return 插入后的业务线实体
     */
    @Transactional(rollbackFor = Exception.class)
    public BusinessLine insertBusinessLine(BusinessLine businessLine) {
        try {
            if (businessLine == null) {
                log.warn("插入业务线时实体不能为空");
                return null;
            }

            // 设置创建和更新时间
            Date now = new Date();
            businessLine.setAddTime(now);
            businessLine.setUpdateTime(now);
            // 设置状态为1（启用）
            businessLine.setStatus(1);

            BusinessLinePo businessLinePo = new BusinessLinePo();
            BeanUtils.copyProperties(businessLine, businessLinePo);

            businessLinePoMapper.insertSelective(businessLinePo);
            return convert(businessLinePoMapper.selectByPrimaryKey(businessLinePo.getId()));
        } catch (Exception e) {
            
            log.error("插入业务线失败, businessLine={}", businessLine, e);
            throw e; // 抛出异常以触发事务回滚
        }
    }

    /**
     * 更新业务线
     * @param businessLine 业务线实体
     * @return 更新后的业务线实体
     */
    @Transactional(rollbackFor = Exception.class)
    public BusinessLine updateBusinessLine(BusinessLine businessLine) {
        try {
            if (businessLine == null || businessLine.getId() == null) {
                log.warn("更新业务线时ID不能为空");
                return null;
            }

            // 设置更新时间
            businessLine.setUpdateTime(new Date());

            BusinessLinePo businessLinePo = new BusinessLinePo();
            BeanUtils.copyProperties(businessLine, businessLinePo);

            int result = businessLinePoMapper.updateByPrimaryKeySelective(businessLinePo);
            if (result <= 0) {
                log.warn("更新业务线失败, id={}", businessLine.getId());
                return null;
            }

            return convert(businessLinePoMapper.selectByPrimaryKey(businessLine.getId()));
        } catch (Exception e) {
            
            log.error("更新业务线失败, businessLine={}", businessLine, e);
            throw e; // 抛出异常以触发事务回滚
        }
    }

    /**
     * 删除业务线（将状态设置为0）
     * @param businessLine 业务线实体
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBusinessLine(BusinessLine businessLine) {
        try {
            if (businessLine == null || businessLine.getId() == null) {
                log.warn("删除业务线时ID不能为空");
                return false;
            }

            // 设置状态为0（删除）和更新时间
            businessLine.setStatus(0);
            businessLine.setUpdateTime(new Date());

            BusinessLinePo businessLinePo = new BusinessLinePo();
            BeanUtils.copyProperties(businessLine, businessLinePo);

            int result = businessLinePoMapper.updateByPrimaryKeySelective(businessLinePo);
            return result > 0;
        } catch (Exception e) {
            
            log.error("删除业务线失败, businessLine={}", businessLine, e);
            throw e; // 抛出异常以触发事务回滚
        }
    }

    /**
     * 将BusinessLinePo转换为BusinessLine
     * @param businessLinePo 数据库实体
     * @return 业务实体
     */
    private BusinessLine convert(BusinessLinePo businessLinePo) {
        if (businessLinePo == null) {
            return null;
        }
        BusinessLine businessLine = new BusinessLine();
        BeanUtils.copyProperties(businessLinePo, businessLine);
        return businessLine;
    }

    /**
     * 根据业务线名称获取业务线列表
     * @param businessLineName 业务线名称
     * @return 业务线列表
     */
    public List<BusinessLine> getBussinessByName(String businessLineName) {
        try {
            if (businessLineName == null || businessLineName.trim().isEmpty()) {
                log.warn("根据名称获取业务线时名称不能为空");
                return null;
            }

            BusinessLinePoExample example = new BusinessLinePoExample();
            example.createCriteria().andBusinessLineEqualTo(businessLineName.trim()).andStatusEqualTo(1);
            List<BusinessLinePo> businessLinePos = businessLinePoMapper.selectByExample(example);
            return businessLinePos.stream().map(this::convert).collect(Collectors.toList());
        } catch (Exception e) {
            
            log.error("根据名称获取业务线失败, businessLineName={}", businessLineName, e);
            return null;
        }
    }
}
