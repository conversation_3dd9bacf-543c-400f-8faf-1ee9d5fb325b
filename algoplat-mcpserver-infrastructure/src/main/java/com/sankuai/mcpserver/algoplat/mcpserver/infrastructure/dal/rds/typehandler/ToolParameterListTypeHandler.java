package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolParameter;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;


/**
 * 专门用于处理ToolParameter列表的TypeHandler
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(ToolParameter.class)
public class ToolParameterListTypeHandler extends JsonListTypeHandler<ToolParameter> {
    public ToolParameterListTypeHandler() {
        super(ToolParameter.class);
    }
}