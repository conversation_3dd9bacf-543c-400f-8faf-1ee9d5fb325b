package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.KmsConfigs;

import com.sankuai.xm.openplatform.auth.entity.AccessTokenResp;
import com.sankuai.xm.openplatform.auth.entity.AppAuthInfo;
import com.sankuai.xm.openplatform.auth.service.XmAuthServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants.AuthConstants.XM_SECRET;

@Service
@Slf4j
public class XmAuthServiceWrapper {
    private long expireTime = 0;
    private String accessToken = "";
    private final static long FIVE_MINUTES = 1000 * 60 * 5;

    @Value("${xuecheng.appkey}")
    private String appKey;
    @Resource
    private XmAuthServiceI.Iface xmAuthService;

    public String getToken() {
        long nowTime = System.currentTimeMillis();
        if (expireTime - FIVE_MINUTES > nowTime) {
            return accessToken;
        }
        String appSecret = KmsConfigs.getKmsValueByKey(XM_SECRET);
        AppAuthInfo appAuthInfo = new AppAuthInfo();
        appAuthInfo.setAppkey(appKey);
        appAuthInfo.setAppSecret(appSecret);

        try {
            AccessTokenResp resp = xmAuthService.accessToken(appAuthInfo);
            if (resp.status.getCode() == 0) {
                accessToken = resp.getAccessToken().getToken();
                expireTime = resp.getAccessToken().getExpireTime();
            } else {
                throw new RuntimeException("生成开放平台Token失败");
            }
            return accessToken;
        } catch (TException e) {
            log.error("生成开放平台Token失败", e);
            throw new RuntimeException("生成开放平台Token失败", e);
        }
    }
}
