package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.ThriftToolInfoPoExample;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.ThriftToolInfoPoMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.ThriftToolInfoPo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ThriftToolInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/7
 */
@Service
public class ThriftToolInfoDao {
    @Resource
    private ThriftToolInfoPoMapper thriftToolInfoPoMapper;

    public ThriftToolInfo getThriftToolInfoByToolId(Long id) {
        ThriftToolInfoPo thriftToolInfoPo = thriftToolInfoPoMapper.selectByPrimaryKey(id);
        return convert(thriftToolInfoPo);
    }

    private ThriftToolInfo convert(ThriftToolInfoPo thriftToolInfoPo) {
        if (Objects.isNull(thriftToolInfoPo)) {
            return null;
        }
        ThriftToolInfo thriftToolInfo = new ThriftToolInfo();
        BeanUtils.copyProperties(thriftToolInfoPo, thriftToolInfo);
        return thriftToolInfo;
    }

    public Long insert(ThriftToolInfoPo thriftToolInfoPo) {
        thriftToolInfoPoMapper.insertSelective(thriftToolInfoPo);
        String appKey = thriftToolInfoPo.getAppKey();
        String serviceName = thriftToolInfoPo.getInterfaceName();
        String methodName = thriftToolInfoPo.getMethodName();
        ThriftToolInfoPo thriftToolInfoPo1 = selectByInfo(appKey, serviceName, methodName);
        if (Objects.isNull(thriftToolInfoPo1)) {
            return null;
        }
        Long id = thriftToolInfoPo1.getId();
        return id;
    }

    public ThriftToolInfoPo selectByInfo(String appKey, String serviceName, String methodName) {
        ThriftToolInfoPoExample example = new ThriftToolInfoPoExample();
        ThriftToolInfoPoExample.Criteria criteria = example.createCriteria();
        criteria.andAppKeyEqualTo(appKey);
        criteria.andInterfaceNameEqualTo(serviceName);
        criteria.andMethodNameEqualTo(methodName);
        criteria.andStatusEqualTo(1);
        List<ThriftToolInfoPo> thriftToolInfoPos = thriftToolInfoPoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(thriftToolInfoPos)) {
            return null;
        }
        ThriftToolInfoPo thriftToolInfoPo = thriftToolInfoPos.get(0);
        return thriftToolInfoPo;
    }

    private ThriftToolInfoPo getThriftToolInfoPoByToolId(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        ThriftToolInfoPo thriftToolInfoPo = thriftToolInfoPoMapper.selectByPrimaryKey(id);
        return thriftToolInfoPo;
    }

    public Boolean deleteByToolId(Long id) {
        if (Objects.isNull(id)) {
            return false;
        }
        ThriftToolInfoPo thriftToolInfoPoByToolId = getThriftToolInfoPoByToolId(id);
        if (Objects.isNull(thriftToolInfoPoByToolId)) {
            return false;
        }
        thriftToolInfoPoByToolId.setStatus(0);
        thriftToolInfoPoByToolId.setUpdateTime(new Date());
        int i = thriftToolInfoPoMapper.updateByPrimaryKeySelective(thriftToolInfoPoByToolId);
        return i > 0;
    }
}
