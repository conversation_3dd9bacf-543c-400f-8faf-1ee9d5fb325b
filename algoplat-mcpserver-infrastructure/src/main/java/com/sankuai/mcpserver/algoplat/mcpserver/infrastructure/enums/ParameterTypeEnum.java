package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.Set;

public enum ParameterTypeEnum {

    STRING("String", String.class, "java.lang.String"),
    INTEGER("Integer", Integer.class, "java.lang.Integer"),
    LONG("Long", Long.class, "java.lang.Long"),
    DOUBLE("Double", Double.class, "java.lang.Double"),
    BOOLEAN("Boolean", Boolean.class, "java.lang.Boolean"),
    LIST("List", List.class, "java.util.List"),
    SET("Set", Set.class, "java.util.Set"),
    MAP("Map", Map.class, "java.util.Map");

    private final String code;

    private final Type type;

    private final String realType;

    ParameterTypeEnum(String code, Type type, String realType) {
        this.code = code;
        this.type = type;
        this.realType = realType;
    }

    public String getCode() {
        return code;
    }

    public Type getType() {
        return type;
    }

    public String getRealType() {
        return realType;
    }

    public static Type getTypeByCode(String code) {
        for (ParameterTypeEnum value : ParameterTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getType();
            }
        }
        return String.class;
    }

    public static String getRealTypeByCode(String code) {
        for (ParameterTypeEnum value : ParameterTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getRealType();
            }
        }
        return STRING.realType;
    }

//    public static String getRealTypeByCode(String code) {
//        if (code != null && code.equals("com")) {
//            return code;
//        }
//        for (ParameterTypeEnum value : ParameterTypeEnum.values()) {
//            if (value.getCode().equals(code)) {
//                return value.getRealType();
//            }
//        }
//        return STRING.realType;
//    }
}
