package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo;
import lombok.Data;
import lombok.ToString;
import org.springframework.ai.model.function.FunctionCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Data
@ToString(callSuper = true)
public class McpServerEntity extends McpServerEntityPo {

    private List<ToolInfo> toolInfoList = new ArrayList<>();


}
