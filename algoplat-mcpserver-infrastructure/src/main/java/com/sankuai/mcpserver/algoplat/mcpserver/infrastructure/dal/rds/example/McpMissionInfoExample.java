package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class McpMissionInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public McpMissionInfoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMissionIdIsNull() {
            addCriterion("mission_id is null");
            return (Criteria) this;
        }

        public Criteria andMissionIdIsNotNull() {
            addCriterion("mission_id is not null");
            return (Criteria) this;
        }

        public Criteria andMissionIdEqualTo(String value) {
            addCriterion("mission_id =", value, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdNotEqualTo(String value) {
            addCriterion("mission_id <>", value, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdGreaterThan(String value) {
            addCriterion("mission_id >", value, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdGreaterThanOrEqualTo(String value) {
            addCriterion("mission_id >=", value, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdLessThan(String value) {
            addCriterion("mission_id <", value, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdLessThanOrEqualTo(String value) {
            addCriterion("mission_id <=", value, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdLike(String value) {
            addCriterion("mission_id like", value, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdNotLike(String value) {
            addCriterion("mission_id not like", value, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdIn(List<String> values) {
            addCriterion("mission_id in", values, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdNotIn(List<String> values) {
            addCriterion("mission_id not in", values, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdBetween(String value1, String value2) {
            addCriterion("mission_id between", value1, value2, "missionId");
            return (Criteria) this;
        }

        public Criteria andMissionIdNotBetween(String value1, String value2) {
            addCriterion("mission_id not between", value1, value2, "missionId");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andMissionRequestIsNull() {
            addCriterion("mission_request is null");
            return (Criteria) this;
        }

        public Criteria andMissionRequestIsNotNull() {
            addCriterion("mission_request is not null");
            return (Criteria) this;
        }

        public Criteria andMissionRequestEqualTo(String value) {
            addCriterion("mission_request =", value, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestNotEqualTo(String value) {
            addCriterion("mission_request <>", value, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestGreaterThan(String value) {
            addCriterion("mission_request >", value, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestGreaterThanOrEqualTo(String value) {
            addCriterion("mission_request >=", value, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestLessThan(String value) {
            addCriterion("mission_request <", value, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestLessThanOrEqualTo(String value) {
            addCriterion("mission_request <=", value, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestLike(String value) {
            addCriterion("mission_request like", value, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestNotLike(String value) {
            addCriterion("mission_request not like", value, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestIn(List<String> values) {
            addCriterion("mission_request in", values, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestNotIn(List<String> values) {
            addCriterion("mission_request not in", values, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestBetween(String value1, String value2) {
            addCriterion("mission_request between", value1, value2, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionRequestNotBetween(String value1, String value2) {
            addCriterion("mission_request not between", value1, value2, "missionRequest");
            return (Criteria) this;
        }

        public Criteria andMissionContextIsNull() {
            addCriterion("mission_context is null");
            return (Criteria) this;
        }

        public Criteria andMissionContextIsNotNull() {
            addCriterion("mission_context is not null");
            return (Criteria) this;
        }

        public Criteria andMissionContextEqualTo(String value) {
            addCriterion("mission_context =", value, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextNotEqualTo(String value) {
            addCriterion("mission_context <>", value, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextGreaterThan(String value) {
            addCriterion("mission_context >", value, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextGreaterThanOrEqualTo(String value) {
            addCriterion("mission_context >=", value, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextLessThan(String value) {
            addCriterion("mission_context <", value, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextLessThanOrEqualTo(String value) {
            addCriterion("mission_context <=", value, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextLike(String value) {
            addCriterion("mission_context like", value, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextNotLike(String value) {
            addCriterion("mission_context not like", value, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextIn(List<String> values) {
            addCriterion("mission_context in", values, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextNotIn(List<String> values) {
            addCriterion("mission_context not in", values, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextBetween(String value1, String value2) {
            addCriterion("mission_context between", value1, value2, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionContextNotBetween(String value1, String value2) {
            addCriterion("mission_context not between", value1, value2, "missionContext");
            return (Criteria) this;
        }

        public Criteria andMissionResultIsNull() {
            addCriterion("mission_result is null");
            return (Criteria) this;
        }

        public Criteria andMissionResultIsNotNull() {
            addCriterion("mission_result is not null");
            return (Criteria) this;
        }

        public Criteria andMissionResultEqualTo(String value) {
            addCriterion("mission_result =", value, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultNotEqualTo(String value) {
            addCriterion("mission_result <>", value, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultGreaterThan(String value) {
            addCriterion("mission_result >", value, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultGreaterThanOrEqualTo(String value) {
            addCriterion("mission_result >=", value, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultLessThan(String value) {
            addCriterion("mission_result <", value, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultLessThanOrEqualTo(String value) {
            addCriterion("mission_result <=", value, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultLike(String value) {
            addCriterion("mission_result like", value, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultNotLike(String value) {
            addCriterion("mission_result not like", value, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultIn(List<String> values) {
            addCriterion("mission_result in", values, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultNotIn(List<String> values) {
            addCriterion("mission_result not in", values, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultBetween(String value1, String value2) {
            addCriterion("mission_result between", value1, value2, "missionResult");
            return (Criteria) this;
        }

        public Criteria andMissionResultNotBetween(String value1, String value2) {
            addCriterion("mission_result not between", value1, value2, "missionResult");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andMisIdIsNull() {
            addCriterion("mis_id is null");
            return (Criteria) this;
        }

        public Criteria andMisIdIsNotNull() {
            addCriterion("mis_id is not null");
            return (Criteria) this;
        }

        public Criteria andMisIdEqualTo(String value) {
            addCriterion("mis_id =", value, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdNotEqualTo(String value) {
            addCriterion("mis_id <>", value, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdGreaterThan(String value) {
            addCriterion("mis_id >", value, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdGreaterThanOrEqualTo(String value) {
            addCriterion("mis_id >=", value, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdLessThan(String value) {
            addCriterion("mis_id <", value, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdLessThanOrEqualTo(String value) {
            addCriterion("mis_id <=", value, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdLike(String value) {
            addCriterion("mis_id like", value, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdNotLike(String value) {
            addCriterion("mis_id not like", value, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdIn(List<String> values) {
            addCriterion("mis_id in", values, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdNotIn(List<String> values) {
            addCriterion("mis_id not in", values, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdBetween(String value1, String value2) {
            addCriterion("mis_id between", value1, value2, "misId");
            return (Criteria) this;
        }

        public Criteria andMisIdNotBetween(String value1, String value2) {
            addCriterion("mis_id not between", value1, value2, "misId");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
