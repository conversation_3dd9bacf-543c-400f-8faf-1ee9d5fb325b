package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamQueryThriftService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
//import com.sankuai.algoplatform.matchops.api.service.TMcpTestMissionService;
import com.sankuai.algoplatform.matchops.api.service.TMcpTestMissionService;
import com.sankuai.dxenterprise.open.gateway.service.dx.api.DxService;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.xm.openplatform.api.service.open.XmOpenMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.udb.common.UdbServiceI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.meituan.service.inf.sts.thrift.token.TokenService.refresh_args._Fields.APP_KEY;
import static com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.LionConfig.matchopsIp;

@Slf4j
@Configuration
public class ThriftConfig {

    @Bean
    public AmazonS3 amazonS3(S3Properties s3Properties) {
        AWSCredentials credentials = new BasicAWSCredentials(s3Properties.getAccessKey(), s3Properties.getSecretKey());
        ClientConfiguration configuration = new ClientConfiguration();
        configuration.setProtocol(Protocol.HTTPS);
        AmazonS3 s3client = new AmazonS3Client(credentials, configuration);
        s3client.setEndpoint(s3Properties.getHostname());
        S3ClientOptions s3ClientOptions = new S3ClientOptions();
        s3ClientOptions.setPathStyleAccess(true);
        s3client.setS3ClientOptions(s3ClientOptions);
        return s3client;
    }
    @MdpThriftClient(remoteAppKey = "com.sankuai.xm.pubapi", timeout = 5000)
    private PushMessageServiceI.Iface pushMessageService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.xm.udb", timeout = 3000)
    private UdbServiceI.Iface udbService;

    @Bean
    public ThriftClientProxy tMcpTestMissionService() {
        ThriftClientProxy proxy = new ThriftClientProxy();
        proxy.setRemoteAppkey("com.sankuai.algoplatform.matchops");
        proxy.setServiceInterface(TMcpTestMissionService.class);
        proxy.setFilterByServiceName(true);
        proxy.setTimeout(10000);
        return proxy;
    }
}
