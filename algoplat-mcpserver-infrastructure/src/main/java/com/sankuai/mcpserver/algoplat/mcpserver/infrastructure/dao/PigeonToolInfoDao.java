package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.PigeonToolInfoPoExample;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.PigeonToolInfoPoMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.PigeonToolInfoPo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.PigeonToolInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/7
 */
@Service
public class PigeonToolInfoDao {
    @Resource
    private PigeonToolInfoPoMapper pigeonToolInfoPoMapper;

    public PigeonToolInfo getPigeonToolInfoPoByToolId(Long id) {
        PigeonToolInfoPo pigeonToolInfoPo = pigeonToolInfoPoMapper.selectByPrimaryKey(id);
        return convertToPigeonToolInfo(pigeonToolInfoPo);
    }

    private PigeonToolInfo convertToPigeonToolInfo(PigeonToolInfoPo pigeonToolInfoPo) {
        if (pigeonToolInfoPo == null) {
            return null;
        }
        PigeonToolInfo pigeonToolInfo = new PigeonToolInfo();
        BeanUtils.copyProperties(pigeonToolInfoPo, pigeonToolInfo);
        return pigeonToolInfo;
    }

    public Long insert(PigeonToolInfoPo pigeonToolInfoPo) {
        int insert = pigeonToolInfoPoMapper.insertSelective(pigeonToolInfoPo);
        String appKey = pigeonToolInfoPo.getAppKey();
        String interfaceName = pigeonToolInfoPo.getInterfaceName();
        String methodName = pigeonToolInfoPo.getMethodName();
        PigeonToolInfoPo pigeonToolInfoPo1 = selectByInfo(appKey, interfaceName, methodName);
        if (pigeonToolInfoPo1 == null) {
            return null;
        }
        Long id = pigeonToolInfoPo1.getId();
        return id;
    }

    public PigeonToolInfoPo selectByInfo(String appKey, String interfaceName, String methodName) {
        PigeonToolInfoPoExample example = new PigeonToolInfoPoExample();
        PigeonToolInfoPoExample.Criteria criteria = example.createCriteria();
        criteria.andAppKeyEqualTo(appKey);
        criteria.andInterfaceNameEqualTo(interfaceName);
        criteria.andMethodNameEqualTo(methodName);
        criteria.andStatusEqualTo(1);
        List<PigeonToolInfoPo> pigeonToolInfoPos = pigeonToolInfoPoMapper.selectByExample(example);
        if (pigeonToolInfoPos.isEmpty()) {
            return null;
        }
        return pigeonToolInfoPos.get(0);
    }

    private PigeonToolInfoPo getPoById(Long id) {
        if (id == null) {
            return null;
        }
        PigeonToolInfoPo pigeonToolInfoPo = pigeonToolInfoPoMapper.selectByPrimaryKey(id);
        return pigeonToolInfoPo;
    }


    public Boolean deleteByToolId(Long id) {
        if (id == null) {
            return false;
        }
        PigeonToolInfoPo poById = getPoById(id);
        PigeonToolInfo pigeonToolInfoPoByToolId = getPigeonToolInfoPoByToolId(id);
        if (pigeonToolInfoPoByToolId == null) {
            return false;
        }
        pigeonToolInfoPoByToolId.setUpdateTime(new Date());
        pigeonToolInfoPoByToolId.setStatus(0);
        int i = pigeonToolInfoPoMapper.updateByPrimaryKeySelective(poById);
        return i > 0;
    }


}
