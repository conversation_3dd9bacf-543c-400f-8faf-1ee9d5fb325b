package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

public class XuechengToMarkdownUtil {
    private List<String> markdown;
    private Stack<ListStackItem> listStack;
    private TableContext currentTable;
    private List<List<String>> tableMatrix;
    private Map<Position, String> mergeMap;

    public XuechengToMarkdownUtil() {
        this.markdown = new ArrayList<>();
        this.listStack = new Stack<>();
        this.currentTable = null;
        this.tableMatrix = new ArrayList<>();
        this.mergeMap = new HashMap<>();
    }

    private static class ListStackItem {
        String type;
        int indentLevel;

        ListStackItem(String type, int indentLevel) {
            this.type = type;
            this.indentLevel = indentLevel;
        }
    }

    private static class Position {
        int row;
        int col;

        Position(int row, int col) {
            this.row = row;
            this.col = col;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Position position = (Position) o;
            return row == position.row && col == position.col;
        }

        @Override
        public int hashCode() {
            return Objects.hash(row, col);
        }
    }

    private static class TableContext {
        List<Integer> colwidth;
        List<List<String>> headerRows;
        List<List<String>> bodyRows;
        List<List<String>> matrix;

        TableContext() {
            this.colwidth = new ArrayList<>();
            this.headerRows = new ArrayList<>();
            this.bodyRows = new ArrayList<>();
            this.matrix = new ArrayList<>();
        }
    }

    public String convert(Map<String, Object> jsonData) {
        processNode(jsonData, 0);
        String result = String.join("\n", markdown).trim();
        return result;
    }

    @SuppressWarnings("unchecked")
    private void processNode(Map<String, Object> node, int indentLevel) {

        String nodeType = (String) node.get("type");
        // 如果是 title 节点，直接忽略
        if ("title".equals(nodeType)) {
            return;
        }
        if (nodeType == null) {
            handleUnknown(node, indentLevel);
            return;
        }

        switch (nodeType) {
            case "doc":
                handleDoc(node, indentLevel);
                break;
            case "heading":
                handleHeading(node, indentLevel);
                break;
            case "paragraph":
                handleParagraph(node, indentLevel);
                break;
            case "bullet_list":
                handleBulletList(node, indentLevel);
                break;
            case "ordered_list":
                handleOrderedList(node, indentLevel);
                break;
            case "list_item":
                handleListItem(node, indentLevel);
                break;
            case "table":
                handleTable(node, indentLevel);
                break;
            case "image":
                handleImage(node, indentLevel);
                break;
            default:
                handleUnknown(node, indentLevel);
        }
    }

    @SuppressWarnings("unchecked")
    private void handleDoc(Map<String, Object> node, int indentLevel) {
        List<Map<String, Object>> content = (List<Map<String, Object>>) node.get("content");
        if (content != null) {
            for (Map<String, Object> child : content) {
                processNode(child, indentLevel);
            }
        }
    }

    @SuppressWarnings("unchecked")
    private void handleHeading(Map<String, Object> node, int indentLevel) {
        Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
        int level = attrs != null ? (Integer) attrs.get("level") : 1;
        String text = getInlineContent(node);
        markdown.add("#".repeat(level) + " " + text + "\n");
    }

    private void handleParagraph(Map<String, Object> node, int indentLevel) {
        String text = getInlineContent(node);
        if (!text.isEmpty()) {
            String prefix = "  ".repeat(indentLevel);
            markdown.add(prefix + text + "\n");
        }
    }

    @SuppressWarnings("unchecked")
    private void handleBulletList(Map<String, Object> node, int indentLevel) {
        listStack.push(new ListStackItem("bullet", indentLevel));
        List<Map<String, Object>> content = (List<Map<String, Object>>) node.get("content");
        if (content != null) {
            for (Map<String, Object> child : content) {
                processNode(child, indentLevel + 1);
            }
        }
        listStack.pop();
    }

    @SuppressWarnings("unchecked")
    private void handleOrderedList(Map<String, Object> node, int indentLevel) {
        listStack.push(new ListStackItem("ordered", indentLevel));
        List<Map<String, Object>> content = (List<Map<String, Object>>) node.get("content");
        if (content != null) {
            for (Map<String, Object> child : content) {
                processNode(child, indentLevel + 1);
            }
        }
        listStack.pop();
    }

    @SuppressWarnings("unchecked")
    private void handleListItem(Map<String, Object> node, int indentLevel) {
        if (listStack.isEmpty()) return;

        ListStackItem currentList = listStack.peek();
        String prefix = "  ".repeat(currentList.indentLevel) +
                (currentList.type.equals("bullet") ? "- " : "1. ");

        List<String> content = new ArrayList<>();
        List<Map<String, Object>> children = (List<Map<String, Object>>) node.get("content");
        if (children != null) {
            for (Map<String, Object> child : children) {
                if ("paragraph".equals(child.get("type"))) {
                    content.add(getInlineContent(child));
                } else {
                    processNode(child, indentLevel);
                }
            }
        }

        if (!content.isEmpty()) {
            markdown.add(prefix + String.join(" ", content) + "\n");
        }
    }

    @SuppressWarnings("unchecked")
    private void handleTable(Map<String, Object> node, int indentLevel) {
        currentTable = new TableContext();
        currentTable.colwidth = getColwidths(node);
        tableMatrix = new ArrayList<>();
        mergeMap = new HashMap<>();

        List<Map<String, Object>> rows = (List<Map<String, Object>>) node.get("content");
        if (rows == null) return;

        int rowIdx = 0;
        for (Map<String, Object> row : rows) {
            if (!"table_row".equals(row.get("type"))) continue;

            List<Map<String, Object>> cells = (List<Map<String, Object>>) row.get("content");
            List<String> rowData = new ArrayList<>();
            int colIdx = 0;

            for (Map<String, Object> cell : cells) {
                while (mergeMap.containsKey(new Position(rowIdx, colIdx))) {
                    rowData.add(null);
                    colIdx++;
                }

                String cellContent = processTableCell(cell);
                Map<String, Object> attrs = (Map<String, Object>) cell.get("attrs");
                int colspan = attrs != null ? (Integer) attrs.getOrDefault("colspan", 1) : 1;
                int rowspan = attrs != null ? (Integer) attrs.getOrDefault("rowspan", 1) : 1;

                for (int r = 0; r < rowspan; r++) {
                    for (int c = 0; c < colspan; c++) {
                        Position pos = new Position(rowIdx + r, colIdx + c);
                        if (r == 0 && c == 0) {
                            rowData.add(cellContent);
                        } else {
                            mergeMap.put(pos, cellContent);
                        }
                    }
                }

                colIdx += colspan;
            }

            tableMatrix.add(rowData);
            rowIdx++;
        }

        int maxCols = tableMatrix.stream()
                .mapToInt(List::size)
                .max()
                .orElse(0);
        for (List<String> row : tableMatrix) {
            while (row.size() < maxCols) {
                row.add(null);
            }
        }

        List<List<String>> filledMatrix = new ArrayList<>();
        for (int r = 0; r < tableMatrix.size(); r++) {
            List<String> filledRow = new ArrayList<>();
            for (int c = 0; c < tableMatrix.get(r).size(); c++) {
                String cell = tableMatrix.get(r).get(c);
                filledRow.add(cell != null ? cell : findMergedContent(r, c));
            }
            filledMatrix.add(filledRow);
        }

        List<List<String>> headerRows = new ArrayList<>();
        List<List<String>> bodyRows = new ArrayList<>();
        for (List<String> row : filledMatrix) {
            boolean isHeader = row.stream()
                    .anyMatch(cell -> cell != null && cell.startsWith("HEADER:"));
            if (isHeader) {
                headerRows.add(row.stream()
                        .map(cell -> cell != null ? cell.replace("HEADER:", "") : " ")
                        .collect(java.util.stream.Collectors.toList()));
            } else {
                bodyRows.add(row);
            }
        }

        buildMarkdownTable(headerRows, bodyRows);
    }

    @SuppressWarnings("unchecked")
    private String processTableCell(Map<String, Object> cell) {
        List<String> cellContent = new ArrayList<>();
        List<Map<String, Object>> content = (List<Map<String, Object>>) cell.get("content");

        if (content != null) {
            for (Map<String, Object> child : content) {
                if ("paragraph".equals(child.get("type"))) {
                    cellContent.add(getInlineContent(child));
                } else if (Arrays.asList("bullet_list", "ordered_list").contains(child.get("type"))) {
                    processNode(child, 0);
                    cellContent.add(extractLastListItems());
                } else {
                    String fallback = extractFallbackText(child);
                    if (fallback != null && !fallback.isEmpty()) {
                        cellContent.add(fallback);
                    }
                }
            }
        }
        return "table_header".equals(cell.get("type")) ? "HEADER:" + String.join(" ", cellContent).trim() : String.join(" ", cellContent).trim();
    }

    private String findMergedContent(int row, int col) {
        Position pos = new Position(row, col);
        if (mergeMap.containsKey(pos)) {
            return mergeMap.get(pos);
        }

        for (int r = row; r >= 0; r--) {
            for (int c = col; c >= 0; c--) {
                Position p = new Position(r, c);
                if (mergeMap.containsKey(p)) {
                    return mergeMap.get(p);
                }
            }
        }
        return " ";
    }

    private void buildMarkdownTable(List<List<String>> headerRows, List<List<String>> bodyRows) {
        List<String> formattedTable = new ArrayList<>();

        if (!headerRows.isEmpty()) {
            for (List<String> row : headerRows) {
                formattedTable.add(formatTableRow(row));
            }
            List<String> separator = currentTable.colwidth.stream()
                    .map(width -> "---")
                    .collect(java.util.stream.Collectors.toList());
            formattedTable.add("|" + String.join("|", separator) + "|");
        }

        for (List<String> row : bodyRows) {
            formattedTable.add(formatTableRow(row));
        }

        markdown.add(String.join("\n", formattedTable) + "\n\n");
        currentTable = null;
    }

    private String formatTableRow(List<String> cells) {
        String s = "|" + cells.stream()
                .map(cell -> {
                    if (cell == null || cell.isEmpty()) {
                        return "   ";
                    } else {
                        return " " + cell + " ";
                    }
                })
                .collect(Collectors.joining("|")) + "|";
        return s;
    }



    @SuppressWarnings("unchecked")
    private List<Integer> getColwidths(Map<String, Object> node) {
        List<Integer> colwidths = new ArrayList<>();
        List<Map<String, Object>> content = (List<Map<String, Object>>) node.get("content");

        if (content != null && !content.isEmpty()) {
            Map<String, Object> firstRow = content.stream()
                    .filter(r -> "table_row".equals(r.get("type")))
                    .findFirst()
                    .orElse(null);

            if (firstRow != null) {
                List<Map<String, Object>> cells = (List<Map<String, Object>>) firstRow.get("content");
                for (Map<String, Object> cell : cells) {
                    Map<String, Object> attrs = (Map<String, Object>) cell.get("attrs");
                    if (attrs != null) {
                        List<Integer> widths = (List<Integer>) attrs.get("colwidth");
                        colwidths.add(widths != null ?
                                widths.stream().mapToInt(Integer::intValue).sum() : 0);
                    }
                }
            }
        }
        return colwidths;
    }

    private void handleImage(Map<String, Object> node, int indentLevel) {
        Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
        String alt = ((String) attrs.getOrDefault("name", "image")).replace("\n", " ");
        String src = (String) attrs.get("src");
        markdown.add(String.format("![%s](%s)\n", alt, src));
    }

    private String handleLink(Map<String, Object> node) {
        String text = getInlineContent(node);
        Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
        String href = (String) attrs.get("href");
        return String.format("[%s](%s)", text, href);
    }

    private String handleMention(Map<String, Object> node) {
        Map<String, Object> attrs = (Map<String, Object>) node.get("attrs");
        String name = (String) attrs.get("name");
        return "@" + name;
    }

    private String handleText(Map<String, Object> node) {
        String text = (String) node.get("text");
        List<Map<String, Object>> marks = (List<Map<String, Object>>) node.get("marks");
        return applyTextMarks(text, marks);
    }

    @SuppressWarnings("unchecked")
    private String applyTextMarks(String text, List<Map<String, Object>> marks) {
        if (marks == null) return text;

        for (int i = marks.size() - 1; i >= 0; i--) {
            Map<String, Object> mark = marks.get(i);
            String markType = (String) mark.get("type");
            Map<String, Object> attrs = (Map<String, Object>) mark.get("attrs");

            switch (markType) {
                case "strong":
                    text = "**" + text + "**";
                    break;
                case "em":
                    text = "*" + text + "*";
                    break;
                case "underline":
                    text = "<u>" + text + "</u>";
                    break;
                case "strikethrough":
                    text = "~~" + text + "~~";
                    break;
                case "color":
                    String color = attrs != null ? (String) attrs.get("color") : "";
                    text = String.format("<span style=\"color:%s\">%s</span>", color, text);
                    break;
                case "font":
                    String size = attrs != null ? attrs.get("dataSize").toString() : "";
                    text = String.format("<span style=\"font-size:%spx\">%s</span>", size, text);
                    break;
            }
        }
        return text;
    }

    @SuppressWarnings("unchecked")
    private String getInlineContent(Map<String, Object> node) {
        List<String> content = new ArrayList<>();
        List<Map<String, Object>> children = (List<Map<String, Object>>) node.get("content");

        if (children != null) {
            for (Map<String, Object> child : children) {
                String type = (String) child.get("type");
                switch (type) {
                    case "text":
                        content.add(handleText(child));
                        break;
                    case "link":
                        content.add(handleLink(child));
                        break;
                    case "mention":
                        content.add(handleMention(child));
                        break;
                    default:
                        String fallback = extractFallbackText(child);
                        if (fallback != null && !fallback.isEmpty()) {
                            content.add(fallback);
                        }
                }
            }
        }
        return String.join(" ", content);
    }

    private String extractLastListItems() {
        List<String> items = new ArrayList<>();
        while (!markdown.isEmpty() &&
                (markdown.get(markdown.size() - 1).startsWith("  - ") ||
                        markdown.get(markdown.size() - 1).startsWith("  1."))) {
            items.add(0, markdown.remove(markdown.size() - 1).trim());
        }
        return String.join("\n", items);
    }

    @SuppressWarnings("unchecked")
    private String extractFallbackText(Map<String, Object> node) {
        List<String> textParts = new ArrayList<>();

        if (node.containsKey("text")) {
            String text = (String) node.get("text");
            List<Map<String, Object>> marks = (List<Map<String, Object>>) node.get("marks");
            if (marks != null) {
                text = applyTextMarks(text, marks);
            }
            textParts.add(text);
        }

        List<Map<String, Object>> content = (List<Map<String, Object>>) node.get("content");
        if (content != null) {
            for (Map<String, Object> child : content) {
                textParts.add(extractFallbackText(child));
            }
        }

        if ("hard_break".equals(node.get("type"))) {
            textParts.add("\n");
        }

        return String.join(" ", textParts.stream()
                .filter(s -> s != null && !s.isEmpty())
                .collect(java.util.stream.Collectors.toList()));
    }

    private void handleUnknown(Map<String, Object> node, int indentLevel) {
        String fallbackText = extractFallbackText(node);
        if (fallbackText != null && !fallbackText.isEmpty()) {
            markdown.add(fallbackText + "\n");
        } else {
            List<Map<String, Object>> content = (List<Map<String, Object>>) node.get("content");
            if (content != null) {
                for (Map<String, Object> child : content) {
                    processNode(child, indentLevel);
                }
            }
        }
    }

    public static void main(String[] args) {
        try {
            // 从文件读取JSON数据
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> data = mapper.readValue(new File("input.json"), Map.class);

            // 转换文档
            XuechengToMarkdownUtil converter = new XuechengToMarkdownUtil();
            String markdown = converter.convert(data);

            // 保存结果
            try (FileWriter writer = new FileWriter("output.md")) {
                writer.write(markdown);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}