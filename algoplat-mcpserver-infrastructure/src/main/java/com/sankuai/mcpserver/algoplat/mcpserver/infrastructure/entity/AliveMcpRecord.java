package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/8/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AliveMcpRecord {
    private Long mcpServerId;

    private Set<Long> aliveMcpToolIds = ConcurrentHashMap.newKeySet();

    private List<String> aliveDefaultMcpToolNames = new CopyOnWriteArrayList<>();


    public void addAliveMcpToolIds(Long toolId){
        this.aliveMcpToolIds.add(toolId);
    }

    public void deleteAliveMcpToolIds(Long toolId){
        this.aliveMcpToolIds.remove(toolId);
    }

    public void addAliveDefaultMcpToolName(String toolName){
        this.aliveDefaultMcpToolNames.add(toolName);
    }

    public void deleteAliveDefaultMcpToolName(String toolName){
        this.aliveDefaultMcpToolNames.remove(toolName);
    }
}
