package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *   表名: bml_mcp_server_tools_register
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ToolInfoPo {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: business_line
     *   说明: 业务线
     */
    private String businessLine;

    /**
     *   字段: tool_uuid
     *   说明: tool唯一标识
     */
    private String toolUuid;

    /**
     *   字段: name
     *   说明: tool名称
     */
    private String name;

    /**
     *   字段: description
     *   说明: tool的描述信息
     */
    private String description;

    /**
     *   字段: type
     *   说明: tool的类型：目前分为pigeon、thrift、http
     */
    private String type;

    /**
     *   字段: http_tool_id
     *   说明: http类型tool的具体配置id
     */
    private Long httpToolId;

    /**
     *   字段: thrift_tool_id
     *   说明: thrift类型tool的具体配置id
     */
    private Long thriftToolId;

    /**
     *   字段: pigeon_tool_id
     *   说明: pigeon类型tool的具体配置id
     */
    private Long pigeonToolId;

    /**
     *   字段: tool_params
     *   说明: tool的参数信息：name（参数名）、type（参数类型，例如String）、description（描述信息）、required（是否必传）
     */
    private List<ToolParameter> toolParams;

    /**
     *   字段: time_out
     *   说明: RPC接口超时时间默认是6秒
     */
    private Integer timeOut = 6000;

    /**
     *   字段: llm_gen_description
     *   说明: 0-不用大模型生成描述 1-需要生成
     */
    private Integer llmGenDescription;

    /**
     *   字段: status
     *   说明: 0-不启用 1-启用
     */
    private Integer status;

    /**
     *   字段: tool_version
     *   说明: tool版本号
     */
    private Integer toolVersion;

    /**
     *   字段: owner
     *   说明: tool的负责人
     */
    private String owner;

    /**
     *   字段: add_time
     *   说明: 记录创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 记录更新时间
     */
    private Date updateTime;

    private Long mcpServerId;

    /**
     *   字段: avitor_script_code
     *   说明: Avitor脚本语言代码
     */
    private String avitorScriptCode;

    /**
     * 是否为长连接任务 0-否 1-是
     */
    private Integer isLongTermTask;

    /**
     * 长期任务类型 1-Thrift长任务 2-自动唤醒Agent长任务 3-非自动唤醒Agent长任务，默认1
     */
    private Integer longTermTaskType;

    /**
     *   字段: text_type
     *   说明: 文本类型：1-富文本，2-普通文本
     */
    private Integer textType = 2;

}