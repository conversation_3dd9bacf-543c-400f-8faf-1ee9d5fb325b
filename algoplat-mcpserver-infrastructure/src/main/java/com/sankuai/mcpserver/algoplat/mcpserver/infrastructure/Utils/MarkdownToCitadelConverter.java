package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.sankuai.ead.citadel.document.node.concept.Inline;
import com.sankuai.ead.citadel.document.node.impl.mark.Em;
import com.sankuai.ead.citadel.document.node.impl.mark.Strong;
import com.sankuai.ead.citadel.document.node.impl.node.*;
import com.sankuai.ead.citadel.document.parser.DocumentParsingException;
import com.sankuai.ead.citadel.document.parser.Serializer;
import com.sankuai.ead.citadel.document.processor.impl.DocValidateResult;
import com.sankuai.ead.citadel.document.processor.impl.DocValidator;
import com.sankuai.ead.citadel.document.util.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.commonmark.ext.gfm.tables.*;
import org.commonmark.ext.gfm.tables.TableCell;
import org.commonmark.ext.gfm.tables.TableRow;
import org.commonmark.node.*;
import org.commonmark.node.BulletList;
import org.commonmark.node.Heading;
import org.commonmark.node.Image;
import org.commonmark.node.Link;
import org.commonmark.node.ListItem;
import org.commonmark.node.OrderedList;
import org.commonmark.node.Paragraph;
import org.commonmark.node.Text;
import org.commonmark.parser.Parser;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

@Slf4j
public class MarkdownToCitadelConverter {
    public static final String NULL = "NULL";

    public static String getNonEmptyText(Text text) {
        String content = text.getLiteral();
        if (StringUtils.isBlank(content)) {
            return NULL;
        }
        return content;
    }


    public static String buildCitadelDoc(String content) throws DocumentParsingException {
        // 插入文档内容
        List<com.sankuai.ead.citadel.document.node.concept.Block> blockList = new ArrayList<>();

        blockList.add(Builder.paragraph(content));

        Doc doc = Builder.doc(Builder.title("hello"), blockList.toArray(new com.sankuai.ead.citadel.document.node.concept.Block[0]));
        String serialize = Serializer.serialize(doc);
        DocValidateResult valid = DocValidator.valid(doc);
        if(!valid.getSuccess()){
            log.warn(serialize);
            throw new DocumentParsingException(valid.getMessage());
        }
        return serialize;
    }

    public static String convertMarkdownToXuecheng(String markdown) throws DocumentParsingException {
        log.info("开始转换Markdown为学城文档，原始内容长度: {}", markdown.length());
        
        // 预处理Markdown，确保分隔线格式正确
        markdown = preprocessMarkdown(markdown);
        
        Parser parser = Parser.builder()
                .extensions(List.of(TablesExtension.create()))
                .build();
        Node document = parser.parse(markdown);

        List<com.sankuai.ead.citadel.document.node.concept.Block> blocks = new ArrayList<>();
        
        // 记录节点数量，用于调试
        final int[] nodeCount = {0};
        final int[] headingCount = {0};
        final int[] paragraphCount = {0};
        final int[] horizontalRuleCount = {0};

        document.accept(new AbstractVisitor() {
            // 标题
            @Override
            public void visit(Heading heading) {
                nodeCount[0]++;
                headingCount[0]++;
                int level = heading.getLevel();
                String content = getTextContent(heading);
                log.debug("处理标题: level={}, content={}", level, content);
                com.sankuai.ead.citadel.document.node.impl.node.Heading xuechengHeading = Builder.heading(level, content);
                xuechengHeading.setId("heading-" + System.currentTimeMillis());
                blocks.add(xuechengHeading);
            }

            // 段落
            @Override
            public void visit(Paragraph paragraph) {
                nodeCount[0]++;
                paragraphCount[0]++;
                List<Inline> inlines = getInlineContent(paragraph);
                log.debug("处理段落: inlines={}", inlines.size());
                blocks.add(Builder.paragraph(inlines.toArray(new Inline[0])));
            }

            // 无序列表
            @Override
            public void visit(BulletList bulletList) {
                nodeCount[0]++;
                log.debug("处理无序列表");
                if (isTaskList(bulletList)) {
                    blocks.add(convertTaskList(bulletList));
                } else {
                    blocks.add(convertBulletList(bulletList));
                }
            }
            
            // 有序列表
            @Override
            public void visit(OrderedList orderedList) {
                nodeCount[0]++;
                log.debug("处理有序列表");
                blocks.add(convertOrderedList(orderedList));
            }

            // 代码块
            @Override
            public void visit(FencedCodeBlock codeBlock) {
                nodeCount[0]++;
                log.debug("处理代码块: info={}", codeBlock.getInfo());
                if ("plantuml".equals(codeBlock.getInfo())) {
                    Plantuml plantuml = new Plantuml();
                    plantuml.setContent(codeBlock.getLiteral());
                    plantuml.setHeight(600);
                    plantuml.setWidth(800);
                    blocks.add(Builder.paragraph(plantuml));
                    return;
                }
                CodeBlock xuechengCodeBlock = new CodeBlock();
                xuechengCodeBlock.setLanguage(codeBlock.getInfo());
                xuechengCodeBlock.setTheme("xq-light");
                xuechengCodeBlock.setIsExpand(false);
                xuechengCodeBlock.setContents(List.of(Builder.text(codeBlock.getLiteral())));
                blocks.add(xuechengCodeBlock);
            }

            // 分割线
            @Override
            public void visit(ThematicBreak thematicBreak) {
                nodeCount[0]++;
                horizontalRuleCount[0]++;
                log.debug("处理分割线 #{}", horizontalRuleCount[0]);
                blocks.add(new HorizontalRule());
            }
            
            // 引用块
            @Override
            public void visit(BlockQuote blockQuote) {
                nodeCount[0]++;
                log.debug("处理引用块");
                List<Inline> inlines = new ArrayList<>();
                Node child = blockQuote.getFirstChild();
                while (child != null) {
                    if (child instanceof Paragraph) {
                        inlines.addAll(getInlineContent((Paragraph) child));
                    }
                    child = child.getNext();
                }
                
                if (!inlines.isEmpty()) {
                    // 由于学城文档库可能不支持BlockQuote，改用普通段落处理
                    com.sankuai.ead.citadel.document.node.impl.node.Paragraph quoteParagraph = 
                        Builder.paragraph(inlines.toArray(new Inline[0]));
                    // 添加特殊标记，表示这是引用块
                    quoteParagraph.setIndent(2);
                    blocks.add(quoteParagraph);
                }
            }

            // 表格
            @Override
            public void visit(CustomBlock tableBlock) {
                nodeCount[0]++;
                if (!(tableBlock instanceof TableBlock)) {
                    log.debug("跳过未知的自定义块: {}", tableBlock.getClass().getName());
                    return;
                }

                log.debug("处理表格");
                com.sankuai.ead.citadel.document.node.impl.node.Table xuechengTable;
                List<com.sankuai.ead.citadel.document.node.impl.node.TableRow> xuechengRows = new ArrayList<>();
                Node row = tableBlock.getFirstChild();

                while (row != null) {
                    if (row instanceof TableBody) {
                        xuechengRows.addAll(convertTableBody((TableBody) row));
                    } else if (row instanceof TableHead) {
                        xuechengRows.add(convertTableHead((TableHead) row));
                    }
                    row = row.getNext();
                }
                xuechengTable = Builder.table(xuechengRows.toArray(new com.sankuai.ead.citadel.document.node.impl.node.TableRow[0]));
                xuechengTable.setResponsive(true);
                blocks.add(xuechengTable);
            }

            private com.sankuai.ead.citadel.document.node.impl.node.TableRow convertTableRows(Node parent, boolean isHeader) {
                List<com.sankuai.ead.citadel.document.node.impl.node.TableCell> xuechengCells = new ArrayList<>();
                forEachChildOfType(parent, TableCell.class, cell ->
                        xuechengCells.add(convertTableCell(cell, isHeader))
                );
                return Builder.tableRow(xuechengCells.toArray(new com.sankuai.ead.citadel.document.node.impl.node.TableCell[0]));
            }

            private com.sankuai.ead.citadel.document.node.impl.node.TableCell convertTableCell(TableCell cell, boolean isHeader) {
                Inline[] content = getInlineContent(cell).toArray(new Inline[0]);
                if (content.length == 0) {
                    content = new Inline[]{Builder.text(NULL)};
                }
                if (isHeader) {
                    return Builder.tableHeader(content[0]);
                } else {
                    return Builder.tableCell(Builder.paragraph(content));
                }
            }

            private <T extends Node> void forEachChildOfType(Node parent, Class<T> childType, Consumer<T> action) {
                Node child = parent.getFirstChild();
                while (child != null) {
                    if (childType.isInstance(child)) {
                        action.accept(childType.cast(child));
                    }
                    child = child.getNext();
                }
            }

            private List<com.sankuai.ead.citadel.document.node.impl.node.TableRow> convertTableBody(TableBody body) {
                List<com.sankuai.ead.citadel.document.node.impl.node.TableRow> xuechengRows = new ArrayList<>();

                forEachChildOfType(body, TableRow.class, row -> {
                    xuechengRows.add(convertTableRows(row, false));
                });

                return xuechengRows;
            }

            private com.sankuai.ead.citadel.document.node.impl.node.TableRow convertTableHead(TableHead head) {
                return convertTableRows(head.getFirstChild(), true);
            }
        });
        
        log.info("Markdown解析完成，共处理节点数: {}, 标题数: {}, 段落数: {}, 分隔线数: {}", 
                nodeCount[0], headingCount[0], paragraphCount[0], horizontalRuleCount[0]);
        
        Doc doc = Builder.doc(Builder.title("hello"), blocks.toArray(new com.sankuai.ead.citadel.document.node.concept.Block[0]));
        String serialize = Serializer.serialize(doc);
        DocValidateResult valid = DocValidator.valid(doc);
        if(!valid.getSuccess()){
            log.warn("文档验证失败: {}", valid.getMessage());
            log.warn("序列化结果: {}", serialize);
            throw new DocumentParsingException(valid.getMessage());
        }
        
        log.info("Markdown转换完成，转换后JSON长度: {}", serialize.length());
        if (log.isDebugEnabled()) {
            log.debug("转换后JSON内容: {}", serialize);
        }
        
        return serialize;
    }
    
    /**
     * 预处理Markdown内容，确保分隔线格式正确，并处理可能导致解析问题的特殊情况
     * @param markdown 原始Markdown内容
     * @return 预处理后的Markdown内容
     */
    private static String preprocessMarkdown(String markdown) {
        if (StringUtils.isBlank(markdown)) {
            return markdown;
        }
        
        StringBuilder result = new StringBuilder();
        String[] lines = markdown.split("\n");
        boolean inCodeBlock = false;
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            
            // 处理代码块标记
            if (line.trim().startsWith("```")) {
                inCodeBlock = !inCodeBlock;
                result.append(line).append("\n");
                continue;
            }
            
            // 在代码块内的内容不做处理
            if (inCodeBlock) {
                result.append(line).append("\n");
                continue;
            }
            
            // 处理分隔线，确保前后有空行
            if (line.trim().matches("^-{3,}$")) {
                // 如果分隔线前面没有空行，添加一个
                if (i > 0 && !lines[i-1].trim().isEmpty()) {
                    result.append("\n");
                }
                
                // 添加分隔线
                result.append(line).append("\n");
                
                // 如果分隔线后面没有空行，添加一个
                if (i < lines.length - 1 && !lines[i+1].trim().isEmpty()) {
                    result.append("\n");
                }
            } else {
                result.append(line).append("\n");
            }
        }
        
        return result.toString();
    }

    private static List<Inline> getInlineContent(Node node) {
        List<Inline> inlines = new ArrayList<>();
        node.accept(new AbstractVisitor() {
            // Inline Code
            @Override
            public void visit(Code code) {
                com.sankuai.ead.citadel.document.node.impl.node.Text codeText = Builder.text(code.getLiteral());
                codeText.setMarks(List.of(new com.sankuai.ead.citadel.document.node.impl.mark.Code()));
                inlines.add(codeText);
            }

            @Override
            public void visit(Text text) {
                inlines.add(Builder.text(getNonEmptyText(text)));
            }

            @Override
            public void visit(StrongEmphasis strong) {
                com.sankuai.ead.citadel.document.node.impl.node.Text strongText = Builder.text(getTextContent(strong));
                strongText.setMarks(Arrays.asList(new Strong()));
                inlines.add(strongText);
            }

            @Override
            public void visit(Emphasis emphasis) {
                com.sankuai.ead.citadel.document.node.impl.node.Text emText = Builder.text(getTextContent(emphasis));
                emText.setMarks(List.of(new Em()));
                inlines.add(emText);
            }

            @Override
            public void visit(Link link) {
                com.sankuai.ead.citadel.document.node.impl.node.Text text = Builder.text(getTextContent(link));
                com.sankuai.ead.citadel.document.node.impl.node.Link citadelLink = Builder.link(
                        link.getDestination(),
                        link.getTitle(),
                        false,
                        text
                );
                inlines.add(citadelLink);
            }

            @Override
            public void visit(Image image) {
                com.sankuai.ead.citadel.document.node.impl.node.Image xuechengImage = new com.sankuai.ead.citadel.document.node.impl.node.Image();
                xuechengImage.setSrc(image.getDestination());
                xuechengImage.setName(image.getTitle());
                inlines.add(xuechengImage);
            }
        });
        return inlines;
    }

    private static String getTextContent(Node node) {
        StringBuilder sb = new StringBuilder();
        node.accept(new AbstractVisitor() {
            @Override
            public void visit(Text text) {
                sb.append(getNonEmptyText(text));
            }
        });
        return sb.toString();
    }

    private static com.sankuai.ead.citadel.document.node.impl.node.ListItem convertListItem(org.commonmark.node.ListItem item) {
        com.sankuai.ead.citadel.document.node.impl.node.ListItem xuechengItem = new com.sankuai.ead.citadel.document.node.impl.node.ListItem();
        List<com.sankuai.ead.citadel.document.node.concept.Block> contents = new ArrayList<>();

        org.commonmark.node.Node child = item.getFirstChild();
        while (child != null) {
            if (child instanceof org.commonmark.node.Paragraph) {
                contents.add(Builder.paragraph(getInlineContent((org.commonmark.node.Paragraph) child).toArray(new Inline[0])));
            } else if (child instanceof org.commonmark.node.BulletList) {
                contents.add(convertBulletList((org.commonmark.node.BulletList) child));
            } else if (child instanceof org.commonmark.node.OrderedList) {
                contents.add(convertOrderedList((org.commonmark.node.OrderedList) child));
            }
            // 可以在这里处理其他类型的子节点
            child = child.getNext();
        }

        xuechengItem.setContents(contents);
        return xuechengItem;
    }

    private static com.sankuai.ead.citadel.document.node.impl.node.BulletList convertBulletList(org.commonmark.node.BulletList bulletList) {
        List<com.sankuai.ead.citadel.document.node.impl.node.ListItem> items = new ArrayList<>();

        org.commonmark.node.Node child = bulletList.getFirstChild();
        while (child != null) {
            if (child instanceof org.commonmark.node.ListItem) {
                items.add(convertListItem((org.commonmark.node.ListItem) child));
            }
            child = child.getNext();
        }

        return Builder.bulletList(items.toArray(new com.sankuai.ead.citadel.document.node.impl.node.ListItem[0]));
    }

    private static com.sankuai.ead.citadel.document.node.impl.node.OrderedList convertOrderedList(org.commonmark.node.OrderedList orderedList) {
        List<com.sankuai.ead.citadel.document.node.impl.node.ListItem> items = new ArrayList<>();

        org.commonmark.node.Node child = orderedList.getFirstChild();
        while (child != null) {
            if (child instanceof org.commonmark.node.ListItem) {
                items.add(convertListItem((org.commonmark.node.ListItem) child));
            }
            child = child.getNext();
        }

        return Builder.orderedList(items.toArray(new com.sankuai.ead.citadel.document.node.impl.node.ListItem[0]));
    }

    private static boolean isTaskList(BulletList bulletList) {
        ListItem firstItem = (ListItem) bulletList.getFirstChild();
        if (firstItem == null) return false;

        Paragraph paragraph = (Paragraph) firstItem.getFirstChild();
        if (paragraph == null) return false;
        StringBuilder content = new StringBuilder();
        for (Node child = paragraph.getFirstChild(); child != null; child = child.getNext()) {
            if (child instanceof Text) {
                content.append(((Text) child).getLiteral());
            }
        }

        String trimmedContent = content.toString().trim();
        return trimmedContent.startsWith("[ ] ") || trimmedContent.startsWith("[x] ");
    }

    private static TaskList convertTaskList(BulletList bulletList) {
        List<TaskItem> items = new ArrayList<>();

        for (Node child = bulletList.getFirstChild(); child != null; child = child.getNext()) {
            if (child instanceof ListItem) {
                ListItem item = (ListItem) child;
                Paragraph paragraph = (Paragraph) item.getFirstChild();
                Text text = (Text) paragraph.getFirstChild();

                boolean checked = text.getLiteral().startsWith("[x] ");
                String content = text.getLiteral().substring(4); // 移除 "[ ] " 或 "[x] "

                TaskItem taskItem = new TaskItem();
                taskItem.setChecked(checked);
                taskItem.setContents(List.of(Builder.paragraph(Builder.text(content))));
                items.add(taskItem);
            }
        }

        return Builder.taskList(items.toArray(new TaskItem[0]));
    }

}