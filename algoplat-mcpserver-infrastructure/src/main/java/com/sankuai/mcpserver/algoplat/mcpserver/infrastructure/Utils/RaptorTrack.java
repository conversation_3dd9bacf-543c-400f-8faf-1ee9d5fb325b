package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import org.apache.commons.collections4.MapUtils;

import java.util.Collections;
import java.util.Map;
import java.util.function.Supplier;

public class RaptorTrack {

    private static final String PREFIX = "BmlMatch_";


    /**
     * Transaction信息上报
     */
    public static <T> T logTransaction(String type, String name, Map<String, Object> appends, Supplier<T> metric){
        return take(PREFIX + type, name, appends, metric);
    }


    /**
     * 异常访问Cat上报信息
     */
    public static void logCatUnexpectedVisit(String name) {
        logEvent(PREFIX + "Sys_UnexpectedVisitNum", name);
    }


    /**
     * 内部错误CAT上报信息
     */
    public static void logCatInternalError(String name) {
        logEvent(PREFIX + "Sys_InternalErrorNum", name);
    }

    /**
     * 其他CAT上报信息
     */
    public static void logCat(String type, String name) {
        logEvent(PREFIX + type, name);
    }

    /**
     * 1次metric上报
     */
    public static void logMetric(String name, Map<String, String> tags) {
        Cat.logMetricForCount(PREFIX + name, 1, tags);
    }

    /**
     * 多次metric上报
     */
    public static void logMetric(String name, Map<String, String> tags, int count) {
        Cat.logMetricForCount(PREFIX + name, count, tags);
    }


    public static void logEvent(String type, Object... params) {
        switch (params.length) {
            case 1: {
                Cat.logEvent(type, String.valueOf(params[0]));
                return;
            }
            case 2: {
                for (int i = 0; i < ((Number) params[1]).intValue(); i++) {
                    Cat.logEvent(type, String.valueOf(params[0]));
                }
                return;
            }
            case 3: {
                Cat.logEvent(type, String.valueOf(params[0]),
                        String.valueOf(params[1]), params[2] == null ? null : String.valueOf(params[2]));
                return;
            }
            default: {
            }
        }
    }

    public static <T> T take(String type, String name, Supplier<T> metric) {
        return take(type, name, Collections.emptyMap(), metric);
    }

    public static <T> T take(String type, String name, Map<String, Object> appends, Supplier<T> metric) {
        Transaction t = Cat.newTransaction(type, name);
        if (MapUtils.isNotEmpty(appends)) {
            appends.forEach(t::addData);
        }
        try {
            T r = metric.get();
            t.setSuccessStatus();
            return r;
        } catch (Exception e) {
            t.setStatus(e);
            throw e;
        } finally {
            t.complete();
        }
    }
    @FunctionalInterface
    public interface Item {
        void report(Object... params);
    }

}

