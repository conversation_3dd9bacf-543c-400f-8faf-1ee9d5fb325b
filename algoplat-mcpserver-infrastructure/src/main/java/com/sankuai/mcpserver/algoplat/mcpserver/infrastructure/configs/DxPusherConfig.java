package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs;

import com.dianping.cat.Cat;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/20
 */

@Slf4j
@Configuration
public class DxPusherConfig {

    @Value("${dx.key}")
    private String key;

    @Value("${dx.pubId}")
    private long fromUid;

    private String token;

    private static final String DX_TOKEN = "dx.token";

    public static final short TO_APPID = 1;
    public static final short CHANNELLD = 0;

    /**
     * 动态创建PusherInfo实例
     *
     * @param appKey    应用key
     * @param fromUid   发送者ID
     * @param toAppId   接收应用ID
     * @param channelId 频道ID
     * @return PusherInfo实例
     */
    public PusherInfo createPusherInfo(String appKey, long fromUid, short toAppId, short channelId, String token) {
        if (token == null) {
            initKeys();
        }
        PusherInfo pusherInfo = new PusherInfo();
        pusherInfo.setAppkey(appKey);
        pusherInfo.setToken(token);
        pusherInfo.setFromUid(fromUid);
        pusherInfo.setToAppid(toAppId);
        pusherInfo.setChannelId(channelId);
        return pusherInfo;
    }

    /**
     * 使用默认配置创建PusherInfo实例
     *
     * @return PusherInfo实例
     */
    public PusherInfo createDefaultPusherInfo() {
        if (token == null) {
            initKeys();
        }
        return createPusherInfo(key, fromUid, TO_APPID, CHANNELLD, token);
    }

    /**
     * 提供一个PusherInfo工厂Bean，用于动态创建PusherInfo实例
     * 使用原型模式，每次获取都会创建新实例
     *
     * @return PusherInfoFactory实例
     */
    @Bean
    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    public PusherInfoFactory pusherInfoFactory() {
        initKeys();
        return new PusherInfoFactory(this);
    }

    private void initKeys() {
        try {
            this.token = Kms.getByName(MdpContextUtils.getAppKey(), DX_TOKEN);
            log.info("获取大象KMS配置成功");
        } catch (KmsResultNullException e) {
            
            log.error("获取大象KMS配置失败", e);
        }
    }

    /**
     * PusherInfo工厂类，用于动态创建PusherInfo实例
     */
    public static class PusherInfoFactory {
        private final DxPusherConfig config;

        public PusherInfoFactory(DxPusherConfig config) {
            this.config = config;
        }

        /**
         * 创建自定义PusherInfo
         *
         * @param appKey  应用key
         * @param fromUid 发送者ID
         * @return PusherInfo实例
         */
        public PusherInfo create(String appKey, long fromUid, String token) {
            try {
                PusherInfo pusherInfo = config.createPusherInfo(appKey, fromUid, TO_APPID, CHANNELLD, token);
                return pusherInfo;
            } catch (Exception e) {
                
                log.error("创建大象PusherInfo失败", e);
                return null;
            }
        }
    }
}