package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.factory;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

public class ThreadPoolFactory {
    private ThreadPoolFactory() {
    }

    static class CallerRunsPolicy implements RejectedExecutionHandler {
        private String name;

        public CallerRunsPolicy(String name) {
            this.name = name;
        }

        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            if (!e.isShutdown()) {
                r.run();
            }
        }
    }

    private static ThreadPool invokeMissionThreadPool = Rhino.newThreadPool("invoke_mission_thread_pool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(20).withMaxSize(256).withMaxQueueSize(100)
                    .withRejectHandler(new CallerRunsPolicy("submitMissionThreadPool")));

    private static ThreadPool modelTestThreadPool = Rhino.newThreadPool("modelTestThreadPool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(10).withMaxSize(128).withMaxQueueSize(20)
                    .withRejectHandler(new CallerRunsPolicy("modelTestThreadPool")));

    private static ThreadPool fridayMissionThreadPool = Rhino.newThreadPool("fridayMissionThreadPool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(10).withMaxSize(128).withMaxQueueSize(20)
                    .withRejectHandler(new CallerRunsPolicy("fridayMissionThreadPool")));


    public static ThreadPoolExecutor getInvokeMissionThreadPool() {
        return invokeMissionThreadPool.getExecutor();
    }
    public static ThreadPoolExecutor getModelTestThreadPool() {
        return modelTestThreadPool.getExecutor();
    }

    public static ThreadPoolExecutor getFridayMissionThreadPool() {
        return fridayMissionThreadPool.getExecutor();
    }

}
