package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对应bml_mcp_server_tools_register表的long_term_task_type字段，长期任务类型 1-Thrift长任务 2-自动唤醒Agent长任务 3-非自动唤醒Agent长任务，默认1
 */
@Getter
@AllArgsConstructor
public enum LongTermTaskTypeEnum {

    /**
     * Thrift长任务
     */
    THRIFT(1, "THRIFT"),

    /**
     * 自动唤醒Agent长任务
     */
    AUTO_AGENT(2, "AUTO_AGENT"),

    /**
     * 非自动唤醒Agent长任务
     */
    NON_AUTO_AGENT(3, "NON_AUTO_AGENT");

    /**
     * 枚举值
     */
    private final Integer code;
    private final String value;
}
