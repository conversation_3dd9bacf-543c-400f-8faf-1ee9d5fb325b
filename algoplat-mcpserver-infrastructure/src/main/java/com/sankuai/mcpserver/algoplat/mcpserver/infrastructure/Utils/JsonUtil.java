package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.dianping.cat.Cat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.*;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Exceptions.JsonSerializationException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.TimeZone;

public class JsonUtil {
    private static final Logger LOG = LoggerFactory.getLogger(JsonUtil.class);
    private static final ObjectMapper DEFAULT_OBJECT_MAPPER;
    private static final ObjectMapper DEFAULT_NOT_NULL_OBJECT_MAPPER;

    static {
        JsonFactory factory = new JsonFactory().disable(JsonFactory.Feature.INTERN_FIELD_NAMES);
        DEFAULT_OBJECT_MAPPER = new ObjectMapper(factory);

        //确保输入的new BigDecimal("xxx.00") 能够序列化成xxx.00而不是xxx.0
        DEFAULT_OBJECT_MAPPER.setNodeFactory(JsonNodeFactory.withExactBigDecimals(true));

        //如果不设置成false最终date变成timestamp 如1641447010269，反之是2022-01-06T05:31:04.532+0000 可以看到这个时区使用了默认格林威治0时区所以相差8小时，使用DEFAULT_OBJECT_MAPPER.setTimeZone设置时间
        DEFAULT_OBJECT_MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        //使用东八区(防止时间序列化变成0时区时间),由于序列化带了时区，反序列化的时候无需指定时区
        DEFAULT_OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("GMT+8"));

//        DEFAULT_OBJECT_MAPPER.configure(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true);
        DEFAULT_OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        DEFAULT_OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        DEFAULT_OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        //风险Floats double 最终默认都输出BigDecimal,需要使用Float.class 和Double.class 才能反序列化到具体类型，使用Object.class反序列化就是BigDecimal
        DEFAULT_OBJECT_MAPPER.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);

        //json 对于这种序列化JsonUtil.object2Json(new Double(123))结果是123.0而不是123
    }

    static {
        JsonFactory factory = new JsonFactory().disable(JsonFactory.Feature.INTERN_FIELD_NAMES);
        DEFAULT_NOT_NULL_OBJECT_MAPPER = new ObjectMapper(factory);

        //确保输入的new BigDecimal("xxx.00") 能够序列化成xxx.00而不是xxx.0
        DEFAULT_NOT_NULL_OBJECT_MAPPER.setNodeFactory(JsonNodeFactory.withExactBigDecimals(true));

        //如果不设置成false最终date变成timestamp 如1641447010269，反之是2022-01-06T05:31:04.532+0000 可以看到这个时区使用了默认格林威治0时区所以相差8小时，DEFAULT_OBJECT_MAPPER.setTimeZone
        DEFAULT_NOT_NULL_OBJECT_MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        //使用东八区(防止时间序列化变成0时区时间),由于序列化带了时区，反序列化的时候无需指定时区
        DEFAULT_OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("GMT+8"));

        DEFAULT_NOT_NULL_OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
//        DEFAULT_NOT_NULL_OBJECT_MAPPER.configure(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true);
        DEFAULT_NOT_NULL_OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        DEFAULT_NOT_NULL_OBJECT_MAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        DEFAULT_NOT_NULL_OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        //风险Floats double 最终默认都输出BigDecimal,需要使用Float.class 和Double.class 才能反序列化到具体类型，使用Object.class反序列化就是BigDecimal
        DEFAULT_NOT_NULL_OBJECT_MAPPER.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);

        //json 对于这种序列化JsonUtil.object2Json(new Double(123))结果是123.0而不是123
    }

    public static <T> T json2Object(String json, Class<T> clazz) throws JsonParseException {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return DEFAULT_OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            JsonParseException exception = new JsonParseException(e.getMessage());
            LOG.error(exception.getMessage(), exception);
            throw exception;
        }
    }

    public static <T> T json2Object(String json, TypeReference<T> clazz) throws JsonParseException {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return DEFAULT_OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            JsonParseException exception = new JsonParseException(e.getMessage());
            LOG.error(exception.getMessage(), exception);
            throw exception;
        }
    }

    public static JsonNode json2JsonNode(String json) throws JsonParseException {
        return json2Object(json, JsonNode.class);
    }

    public static ObjectNode json2ObjectNode(String json) throws JsonParseException {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.rocs.common.utils.JsonUtil.json2ObjectNode(java.lang.String)");
        return json2Object(json, ObjectNode.class);
    }

    public static String object2Json(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return DEFAULT_OBJECT_MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            JsonSerializationException exception = new JsonSerializationException("JsonUtil.object2Json error !", e);
            LOG.error(exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * null 会被序列化为 "null"
     *
     * @param object
     * @return
     */
    public static String object2JsonNullSerialize(Object object) {
        try {
            return DEFAULT_OBJECT_MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            JsonSerializationException exception = new JsonSerializationException("JsonUtil.object2Json error !", e);
            LOG.error(exception.getMessage(), exception);
            throw exception;
        }
    }

    public static String object2JsonNonNull(Object object) throws JsonParseException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.rocs.common.utils.JsonUtil.object2JsonNonNull(java.lang.Object)");
        if (object == null) {
            return null;
        }
        try {
            return DEFAULT_NOT_NULL_OBJECT_MAPPER.writeValueAsString(object);
        } catch (IOException e) {
            JsonParseException exception = new JsonParseException(e.getMessage());
            LOG.error(exception.getMessage(), exception);
            throw exception;
        }
    }

    public static String object2Json(Object object, boolean nullValueAllow) throws JsonParseException {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.rocs.common.utils.JsonUtil.object2Json(java.lang.Object,boolean)");
        if (object == null) {
            return null;
        }
        try {
            if (!nullValueAllow) {
                return DEFAULT_NOT_NULL_OBJECT_MAPPER.writeValueAsString(object);
            }
            return object2Json(object);
        } catch (IOException e) {
            JsonParseException exception = new JsonParseException(e.getMessage());
            LOG.error(exception.getMessage(), exception);
            throw exception;
        }
    }

    public static boolean isNullNode(JsonNode jsonNode) {
        return jsonNode == null || (jsonNode instanceof NullNode) || (jsonNode instanceof MissingNode);
    }

    public static boolean isNotNullNode(JsonNode jsonNode) {
        return !isNullNode(jsonNode);
    }

    public static boolean isBlankJsonNode(JsonNode jsonNode) {
        return jsonNode == null || jsonNode.isNull() || jsonNode.isMissingNode()
                || ((jsonNode instanceof ObjectNode) && jsonNode.size() == 0)
                || ((jsonNode instanceof TextNode) && StringUtils.isBlank(jsonNode.asText()))
                || ((jsonNode instanceof POJONode) && ((POJONode) jsonNode).getPojo() == null);
    }

    public static boolean isEmptyObjectNode(ObjectNode objectNode) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.rocs.common.utils.JsonUtil.isEmptyObjectNode(com.fasterxml.jackson.databind.node.ObjectNode)");
        return objectNode == null || objectNode.size() == 0;
    }

    public static ObjectNode createObjectNode() {
        return DEFAULT_OBJECT_MAPPER.createObjectNode();
    }

    public static ArrayNode createArrayNode() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.rocs.common.utils.JsonUtil.createArrayNode()");
        return DEFAULT_OBJECT_MAPPER.createArrayNode();
    }

    public static NullNode createNullNode() {
        return NullNode.instance;
    }

    private JsonUtil() {
    }
}
