package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 *
 *   表名: bml_mcp_server_tool_http_interface_info
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HttpToolInfoPo {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: note
     *   说明: 说明信息
     */
    private String note;

    /**
     *   字段: url
     *   说明: 方法url
     */
    private String url;

    /**
     *   字段: method_name
     *   说明: 方法名
     */
    private String methodName;

    /**
     *   字段: headers
     *   说明: 请求头信息
     */
    private Map<String, String> headers;

    /**
     *   字段: status
     *   说明: 0-不启用 1-启用
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 记录创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 记录更新时间
     */
    private Date updateTime;
}