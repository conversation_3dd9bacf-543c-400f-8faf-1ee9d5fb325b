package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: bml_mcp_server_tool_thrift_interface_info
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThriftToolInfoPo {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: note
     *   说明: 说明信息
     */
    private String note;

    /**
     *   字段: app_key
     *   说明: 提供thrift接口的服务appkey
     */
    private String appKey;

    /**
     *   字段: interface_name
     *   说明: 接口全路径名
     */
    private String interfaceName;

    /**
     *   字段: method_name
     *   说明: 方法名
     */
    private String methodName;

    /**
     *   字段: cell
     *   说明: 路由信息
     */
    private String cell;

    /**
     *   字段: ip
     *   说明: ip地址
     */
    private String ip;

    /**
     *   字段: port
     *   说明: 端口号
     */
    private String port;

    /**
     *   字段: status
     *   说明: 0-不启用 1-启用
     */
    private Integer status;

    /**
     *   字段: add_time
     *   说明: 记录创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 记录更新时间
     */
    private Date updateTime;
}