package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;

public class InputStreamDeserializer extends JsonDeserializer<InputStream> {

    @Override
    public InputStream deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String base64String = jsonParser.getText();
        byte[] decodedBytes = Base64.getDecoder().decode(base64String);
        return new ByteArrayInputStream(decodedBytes);
    }
}