package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: testnew
 * <AUTHOR>
 * @Date 2025/4/8
 */
public class MethodInfoParseUtil {

    public static Map<String, Object> getInterfaceInfoByMethodMetaData(Method method) {
        if (method == null) {
            return new HashMap<>();
        }
        Map<String, Object> methodDoc = new HashMap<>();
        String methodRealName = method.getName();
        methodDoc.put("methodRealName", methodRealName);

        Parameter[] parameters = method.getParameters();
        ParametersAnalyzer methodTypeAnalyzer = new ParametersAnalyzer();
        Map<String, Object> analyParametersResult = methodTypeAnalyzer.analyParameters(parameters);
        List<Map<String, Object>> requestParms = new ArrayList<>();
        for (Parameter parameter : parameters) {
            Map<String, Object> parametersDoc = new HashMap<>();
            parametersDoc.put("parameterName", parameter.getName());
            parametersDoc.put("parameterTypeName", parameter.getType().getName());
            parametersDoc.put("paramDescription", analyParametersResult.get(parameter.getName()));
            requestParms.add(parametersDoc);
        }
        methodDoc.put("requestParams", JSONObject.toJSONString(requestParms));
        Class<?> returnType = method.getReturnType();
        Map<String, Object> returnParamDescription = methodTypeAnalyzer.buildTypeInfo(returnType);
        methodDoc.put("returnValueDescription", returnParamDescription);
        return methodDoc;
    }


}
