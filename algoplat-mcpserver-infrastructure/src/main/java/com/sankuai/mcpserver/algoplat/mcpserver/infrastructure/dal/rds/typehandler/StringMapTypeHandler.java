package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.Map;

/**
 * 专门用于处理Map<String, String>类型的TypeHandler
 */

@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes({Map.class})
public class StringMapTypeHandler extends JsonMapTypeHandler<String, String> {
    public StringMapTypeHandler() {
        super(String.class, String.class);
    }
}