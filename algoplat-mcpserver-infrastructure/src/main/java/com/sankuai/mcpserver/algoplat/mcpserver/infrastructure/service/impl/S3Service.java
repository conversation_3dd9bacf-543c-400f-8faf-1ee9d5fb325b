package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.service.impl;

import com.alibaba.fastjson.JSON;
import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.google.common.base.Preconditions;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs.S3Properties;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.S3UploadRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.net.URLDecoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Service
@Slf4j
public class S3Service {

    @Resource
    private S3Properties s3Properties;

    @Resource
    private AmazonS3 amazonS3;

    public String uploadFileToS3AndGetFileUrl(File file, String s3dataSetName, Long useTime) throws Exception {
        try (InputStream inputStream = new FileInputStream(file)) {
            S3UploadRequest s3UploadRequest = new S3UploadRequest(inputStream, s3dataSetName);
            String s3Url = uploadFileToS3AndGetFileUrl(s3UploadRequest, useTime);
            // 设置文件编码为UTF-8
            Map<String, String> objectInfo = extractBucketAndObjectName(s3Url);
            if (objectInfo != null) {
                updateObjectHttpUTF8(objectInfo.get("bucketName"), objectInfo.get("objectName"));
            }
            log.info("标注数据已上传到S3，URL: {}", s3Url);
            return s3Url;
        } catch (Exception e) {
            log.error("上传标注数据到S3失败", e);
            throw new Exception("上传标注数据到S3失败: " + e.getMessage());
        }
    }

    public String uploadFileToS3AndGetFileUrl(S3UploadRequest s3UploadRequest, Long useTime) {
        InputStream inputStream = s3UploadRequest.getInputStream();
        if (inputStream == null) {
            IllegalArgumentException illegalArgumentException = new IllegalArgumentException("uploadFileToS3AndGetFileUrl file is null, can not upload file");
            log.error(illegalArgumentException.getMessage());
            Cat.logError(illegalArgumentException);
            throw illegalArgumentException;
        }
        String originalFilename = s3UploadRequest.getOriginalFileName();
        if (StringUtils.isBlank(originalFilename)) {
            throw new IllegalArgumentException("uploadFileToS3AndGetFileUrl file name is blank, can not upload file");
        }
        if (inputStream == null) {
            throw new IllegalArgumentException("uploadFileToS3AndGetFileUrl file inputStream is null, can not upload file");
        }
        log.info("Received file: {}, Content-Type: {}", originalFilename, null);
        String s3Url = upload2S3(inputStream, originalFilename, useTime);
        return s3Url;
    }


    public String upload2S3(InputStream inputStream, String fileName, Long expireTime) {
        Preconditions.checkNotNull(inputStream);
        Preconditions.checkNotNull(fileName);

        PutObjectRequest req = new PutObjectRequest(
                s3Properties.getBucketName(), fileName, inputStream, new ObjectMetadata());

        try {
            amazonS3.putObject(req);
        } catch (AmazonServiceException e) {
            throw new RuntimeException("S3PutObject error: " + fileName, e);
        }
        GeneratePresignedUrlRequest request =
                new GeneratePresignedUrlRequest(s3Properties.getBucketName(), fileName);
        request.setMethod(HttpMethod.GET);
        request.setExpiration(getExpiration(expireTime));
        URL s3Url = amazonS3.generatePresignedUrl(request);
        log.info("upload2S3: file:{}, url:{}", fileName, JSON.toJSONString(s3Url));
        return s3Url.toString();
    }


    public S3Object getObject(GetObjectRequest getObjectRequest) {
        return amazonS3.getObject(getObjectRequest);
    }

    /**
     * 获取S3对象的元数据
     *
     * @param metadataRequest 元数据请求
     * @return 对象元数据
     */
    public ObjectMetadata getObjectMetadata(GetObjectMetadataRequest metadataRequest) {
        return amazonS3.getObjectMetadata(metadataRequest);
    }

    private static Date getExpiration(Long expireTime) {
        long milliSeconds = System.currentTimeMillis();
        if (expireTime == null) {
            expireTime = 1000L * 60 * 60 * 24 * 30;
        }
        milliSeconds += expireTime;
        return new Date(milliSeconds);
    }

    public void updateObjectHttpUTF8(String bucketName, String objectName) {
        updateObjectHttp(bucketName, objectName, "UTF-8");
    }

    public void updateObjectHttp(String bucketName, String objectName, String encodingName) {
        try {
            // 获取原始对象元数据
            GetObjectMetadataRequest req = new GetObjectMetadataRequest(bucketName, objectName);
            ObjectMetadata objectMeta = amazonS3.getObjectMetadata(req);
            // 更新元数据
            objectMeta.setContentEncoding(encodingName);
            // 创建更新请求
            CopyObjectRequest copyObjRequest = new CopyObjectRequest(bucketName, objectName, bucketName, objectName);
            copyObjRequest.setNewObjectMetadata(objectMeta);
            amazonS3.copyObject(copyObjRequest);
        } catch (AmazonServiceException ase) {
            log.error("AmazonServiceException occurred while updating object metadata: {}", ase.getMessage(), ase);
            throw new RuntimeException("S3 updateObject error: " + objectName, ase);
        } catch (AmazonClientException ace) {
            log.error("AmazonClientException occurred while updating object metadata: {}", ace.getMessage(), ace);
            throw new RuntimeException("S3 client error: " + objectName, ace);
        }
    }

    public Map<String, String> extractBucketAndObjectName(String url) {
        try {
            Preconditions.checkNotNull(url, "URL cannot be null");
            // 使用正则表达式提取 bucketName 和 objectName
            String regex = "https?://[^/]+/([^/]+)/(.+?)(\\?|$)";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(url);

            if (matcher.find()) {
                String bucketName = matcher.group(1); // 提取 bucketName
                String encodedObjectName = matcher.group(2); // 提取 objectName（可能是百分号编码的）

                // 解码 objectName
                String objectName = URLDecoder.decode(encodedObjectName, "UTF-8");

                log.info("Extracted bucketName: {}, objectName: {}", bucketName, objectName);

                Map<String, String> result = new HashMap<>();
                result.put("bucketName", bucketName);
                result.put("objectName", objectName);
                return result;
            } else {
                log.error("Failed to extract bucketName and objectName from URL: {}", url);
                return null;
            }
        } catch (Exception e) {
            log.error("Error occurred while extracting bucketName and objectName: {}", e.getMessage(), e);
            return null;
        }
    }

    public Map<String, String> getObjectInfoAndSetUTF8(String inputurl) {
        Map<String, String> map = extractBucketAndObjectName(inputurl);
        String bucketName = map.get("bucketName");
        String objectName = map.get("objectName");
        updateObjectHttpUTF8(bucketName, objectName);
        return map;
    }


}
