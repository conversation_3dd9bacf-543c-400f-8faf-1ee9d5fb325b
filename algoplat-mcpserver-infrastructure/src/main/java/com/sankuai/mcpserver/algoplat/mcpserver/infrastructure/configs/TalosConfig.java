package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs;

import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.Talos;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Talos配置类
 *
 * <AUTHOR>
 */
@Data
@Configuration
@Slf4j
public class TalosConfig {

    /**
     * Talos账户名
     */
    private String username = "talos_data_tzgc";

    /**
     * Talos账户密码
     */
    private String password;

    @Bean
    public AsyncTalosClient talosClient() {
        log.info("创建Talos客户端Bean...");
        initKeys();
        // 创建AsyncTalosClient实例，只使用账号密码
        AsyncTalosClient talosClient = new AsyncTalosClient(username, password);


        try {
            // 开启session
            talosClient.openSession(new Talos.RequestOption(60, 60, 60, TimeUnit.SECONDS));
            log.info("Talos客户端会话开启成功");
        } catch (TalosException e) {
            log.error("Talos客户端会话开启失败", e);
        }

        return talosClient;
    }

    /**
     * 创建定时任务，定期刷新Talos会话
     *
     * @param talosClient Talos客户端
     * @return 定时任务调度器
     */
    @Bean
    public ThreadPoolTaskScheduler taskScheduler(AsyncTalosClient talosClient) {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(2);
        taskScheduler.setThreadNamePrefix("renew-token-pool-");
        taskScheduler.setWaitForTasksToCompleteOnShutdown(true);
        taskScheduler.setAwaitTerminationSeconds(120);
        taskScheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        taskScheduler.initialize();

        // 每6小时刷新一次会话（2160000毫秒 = 6小时）
        taskScheduler.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                try {
                    log.info("定时刷新Talos会话...");
                    talosClient.openSession(new Talos.RequestOption(60, 60, 60, TimeUnit.SECONDS));
                    log.info("Talos会话刷新成功");
                } catch (TalosException e) {
                    log.error("Talos会话刷新失败", e);
                }
            }
        }, 2160000); // 6小时

        return taskScheduler;
    }
    private void initKeys() {
        this.password = KmsConfigs.getTalosKey();
    }
}