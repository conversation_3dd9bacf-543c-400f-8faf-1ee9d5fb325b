package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums;

/**
 * 工具类型枚举
 */
public enum ToolTypeEnum {

    /**
     * Pigeon RPC调用
     */
    PIGEON("PIGEON"),

    /**
     * HTTP调用
     */
    HTTP("HTTP"),

    /**
     * Thrift RPC调用
     */
    THRIFT("THRIFT"),

    SystemDefault("SystemDefault"),

    ToolAnnotation("ToolAnnotation");

    /**
     * 枚举值
     */
    private final String value;

    /**
     * 构造函数
     * @param value 枚举值
     */
    ToolTypeEnum(String value) {
        this.value = value;
    }

    /**
     * 获取枚举值
     * @return 枚举值
     */
    public String getValue() {
        return this.value;
    }
}
