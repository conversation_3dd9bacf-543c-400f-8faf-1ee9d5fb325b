package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * 处理JSON字符串和Map对象之间的转换的通用TypeHandler
 * @param <K> Map的键类型
 * @param <V> Map的值类型
 */
public class JsonMapTypeHandler<K, V> extends BaseTypeHandler<Map<K, V>> {
    private static final Logger log = LoggerFactory.getLogger(JsonMapTypeHandler.class);
    private final Class<K> keyClass;
    private final Class<V> valueClass;

    public JsonMapTypeHandler(Class<K> keyClass, Class<V> valueClass) {
        if (keyClass == null || valueClass == null) {
            throw new IllegalArgumentException("Type arguments cannot be null");
        }
        this.keyClass = keyClass;
        this.valueClass = valueClass;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<K, V> parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = JacksonUtil.toJsonStr(parameter);
            ps.setString(i, json);
        } catch (Exception e) {
            log.error("Error converting Map to JSON string", e);
            throw new SQLException("Error converting Map to JSON string", e);
        }
    }

    @Override
    public Map<K, V> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public Map<K, V> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public Map<K, V> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private Map<K, V> parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        
        try {
            return JacksonUtil.toMap(json, keyClass, valueClass);
        } catch (Exception e) {
            log.error("Error parsing JSON string to Map<{}, {}>: {}", keyClass.getSimpleName(), valueClass.getSimpleName(), json, e);
            return null;
        }
    }
}