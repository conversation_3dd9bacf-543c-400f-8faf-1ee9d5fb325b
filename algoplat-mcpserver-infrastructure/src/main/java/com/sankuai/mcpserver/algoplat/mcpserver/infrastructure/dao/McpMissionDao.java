package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpMissionInfoExample;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.McpMissionInfoMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpMissionInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/13
 */
@Service
@Slf4j
public class McpMissionDao {

    @Resource
    private McpMissionInfoMapper mcpMissionInfoMapper;

    public McpMissionInfo getMcpMissionInfoByMissionId(String missionId) {
        Cat.logEvent("McpMissionInfo", "getMissionInfoByMissionId" + missionId);
        log.info("McpMissionDao getMissionInfoByMissionId missionId:{}", missionId);
        if (StringUtils.isBlank(missionId)) {
            log.error("McpMissionDao getMissionInfoByMissionId missionId is null");
            return null;
        }
        McpMissionInfoExample missionInfoExample = new McpMissionInfoExample();
        missionInfoExample.createCriteria().andMissionIdEqualTo(missionId);
        List<McpMissionInfo> mcpMissionInfos = mcpMissionInfoMapper.selectByExample(missionInfoExample);
        log.info("McpMissionDao getFridayMissionInfoByMissionId missionId:{}, mcpMissionInfos:{}", missionId, JSONObject.toJSONString(mcpMissionInfos));
        return mcpMissionInfos.isEmpty() ? null : mcpMissionInfos.get(0);
    }

    public int insertMcpMissionInfo(McpMissionInfo mcpMissionInfo) {
        Cat.logEvent("McpMissionDao", "insertMcpMissionInfo");
        log.info("McpMissionDao insertMcpMissionInfo mcpMissionInfo:{}", JSONObject.toJSONString(mcpMissionInfo));
        return mcpMissionInfoMapper.insertSelective(mcpMissionInfo);
    }

    public int updateMissionInfo(McpMissionInfo mcpMissionInfo) {
        Cat.logEvent("McpMissionDao", "updateFridayMissionInfo");
        log.info("McpMissionDao updateFridayMissionInfo mcpMissionInfo:{}", JSONObject.toJSONString(mcpMissionInfo));
        return mcpMissionInfoMapper.updateByPrimaryKeySelective(mcpMissionInfo);
    }

    public List<McpMissionInfo> getMcpMissionInfoByStatusAndType(Integer type, List<Integer> status) {
        Cat.logEvent("McpMissionInfo", "getMcpMissionInfoByStatusAndType");
        log.info("McpMissionDao getMcpMissionInfoByStatusAndType type:{}, status:{}", type, status);
        if (type == null || status == null) {
            log.error("McpMissionDao getMcpMissionInfoByStatusAndType type or status is null");
            return null;
        }
        McpMissionInfoExample missionInfoExample = new McpMissionInfoExample();
        missionInfoExample.createCriteria().andTypeEqualTo(type).andStatusIn(status);
        return mcpMissionInfoMapper.selectByExample(missionInfoExample);
    }
}
