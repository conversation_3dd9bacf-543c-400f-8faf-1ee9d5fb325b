package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: testnew
 * <AUTHOR>
 * @Date 2025/4/8
 */
@Slf4j
@Service
public class InterfaceInfoParseUtil {

    public Map<String, Object> getInterfaceInfoContext(String className, String methodName) {
        try {
            Class<?> aClass = Class.forName(className);
            Method[] methods = aClass.getMethods();
            Map<String, Object> methodBaseInfo = new HashMap<>();
            // 获取基础类信息、方法信息、参数信息。
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    methodBaseInfo = MethodInfoParseUtil.getInterfaceInfoByMethodMetaData(method);
                }
            }
            // 通过ASM读取字节码文件，获取类注解、方法注解中的内容
            Map<String, Object> annotationParseResult = AnnotationParserUtil.processAnnInfo(aClass.getName(), methodName);
            if (annotationParseResult == null) {
                annotationParseResult = new HashMap<>();
            }
            Map<String, Object> mergeMapInterfaceInfo = mergeMapInterfaceInfo(methodBaseInfo, annotationParseResult);
            Map<String, Object> result = new HashMap<>();
            result.put("mergeMapInterfaceInfo", mergeMapInterfaceInfo);
            result.put("annotationParseResult", annotationParseResult);
            result.put("methodBaseInfo", methodBaseInfo);
            return result;
        } catch (ClassNotFoundException e) {
            
            throw new RuntimeException(e);
        }
    }

    private static Map<String, Object> mergeMapInterfaceInfo(Map<String, Object> methodBaseInfo, Map<String, Object> annotationParseResult) {

        Map<String, Object> standardInterfaceInfo = new HashMap<>();
        Type mapType = new TypeToken<Map<String, Object>>() {
        }.getType();
        Type listMapType = new TypeToken<List<Map<String, Object>>>() {
        }.getType();
        //接口类信息
        Object interfaceAnnDocObj = annotationParseResult.get("interfaceAnnDoc");
        standardInterfaceInfo.put("interfaceAnnDoc", interfaceAnnDocObj);
        // 方法和参数信息
        Object methodAnnDocListObj = annotationParseResult.get("methodAnnDocList");
        List<Map<String, Object>> methodAnnDocList = new ArrayList<>();
        if (methodAnnDocListObj != null) {
            methodAnnDocList = JSONObject.parseObject(String.valueOf(methodAnnDocListObj), listMapType);
        }
        Map<String, Object> standardMethodDoc = new HashMap<>();
        for (Map<String, Object> methodAnnDoc : methodAnnDocList) {
            String methodName = methodAnnDoc.get("methodName") == null ? "UnknownMethod" : methodAnnDoc.get("methodName").toString();
            standardMethodDoc.put("methodName", methodName);
            Object methodDocObj = methodAnnDoc.get("methodDoc");
            Map<String, Object> methodDoc = new HashMap<>();
            if (methodDocObj != null) {
                methodDoc = JSONObject.parseObject(methodDocObj.toString(), mapType);
            }
            // Tool描述信息
            String description = methodDoc.get("description") == null ? JSONObject.toJSONString(methodBaseInfo) : methodDoc.get("description").toString();
            standardMethodDoc.put("methodDecription", description);

            // Tool名称
            String displayName = methodDoc.get("displayName") == null ? methodName : methodDoc.get("displayName").toString();
            standardMethodDoc.put("methodDisplayName", displayName);

            Object returnValueDescriptionObj = methodDoc.get("returnValueDescription");
            String baseReturnValueDescription = methodBaseInfo.get("returnValueDescription") == null ? "" : methodBaseInfo.get("returnValueDescription").toString();
            String returnValueDescription = returnValueDescriptionObj == null ? baseReturnValueDescription : returnValueDescriptionObj + baseReturnValueDescription;
            //返回值描述
            standardMethodDoc.put("returnValueDescription", returnValueDescription);

            //参数信息,这里以RPC基础参数信息为视角
            List<Map<String, Object>> parameters = methodDoc.get("parameters") == null ? new ArrayList<>() : JSONObject.parseObject(methodDoc.get("parameters").toString(), listMapType);
            List<Map<String, Object>> baseRequestParams = methodBaseInfo.get("requestParams") == null ? new ArrayList<>() : JSONObject.parseObject(methodBaseInfo.get("requestParams").toString(), listMapType);
            Map<String, String> parametersMap = generateParameterMap(parameters);
            for (Map<String, Object> baseParam : baseRequestParams) {
                Object nameObj = baseParam.get("parameterName");
                if (nameObj == null) {
                    continue;
                }
                String name = nameObj.toString();
                String annParamDesc = parametersMap.get(name) == null ? "" : parametersMap.get(name);
                String paramDescription = baseParam.get("paramDescription") == null ? "" : JSONObject.toJSONString(baseParam.get("paramDescription"));
                baseParam.put("paramDescription", annParamDesc + paramDescription);
            }
            // 参数信息
            standardMethodDoc.put("requestParams", baseRequestParams);
            standardInterfaceInfo.put("standardMethodDoc", standardMethodDoc);


        }
        return standardInterfaceInfo;

    }

    public static Map<String, String> generateParameterMap(List<Map<String, Object>> parameters) {
        return (parameters != null ? parameters : Collections.<Map<String, Object>>emptyList())
                .stream()
                .filter(paramMap -> paramMap != null
                        && paramMap.containsKey("name")
                        && paramMap.get("name") != null
                )
                .collect(Collectors.toMap(
                        paramMap -> paramMap.get("name").toString(),
                        paramMap -> paramMap.containsKey("description")
                                ? paramMap.get("description").toString()
                                : "",
                        (existing, replacement) -> existing, // 重复键处理策略
                        LinkedHashMap::new // 保持插入顺序
                ));
    }


}


