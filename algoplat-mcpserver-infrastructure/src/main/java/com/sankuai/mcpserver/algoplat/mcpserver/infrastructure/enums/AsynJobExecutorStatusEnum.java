package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums;

public enum AsynJobExecutorStatusEnum {
  UNEXIST(-3, "任务不存在"),    // 等待执行
  PENDING(-2, "等待执行"),    // 等待执行
  RUNNING(0, "执行中"),    // 正在执行
  COMPLETED(1, "执行完成"),  // 执行完成
  PULLED(2, "通知推送完成"),  // 通知推送完成
  FAILED(-1, "执行失败"); // 执行失败

  AsynJobExecutorStatusEnum(Integer status, String desc) {
    this.status = status;
    this.desc = desc;
  }

  private Integer status;
  private String desc;

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public String getDesc() {
    return desc;
  }

  public void setDesc(String desc) {
    this.desc = desc;
  }

  /**
   * 根据状态获取枚举
   * @param status
   * @return
   */
  public static AsynJobExecutorStatusEnum getByStatus(Integer status) {
      if (status == null) {
          return UNEXIST;
      }

      for (AsynJobExecutorStatusEnum statusEnum : AsynJobExecutorStatusEnum.values()) {
          if (statusEnum.getStatus().equals(status)) {
              return statusEnum;
          }
      }

      return UNEXIST;
  }
  
}