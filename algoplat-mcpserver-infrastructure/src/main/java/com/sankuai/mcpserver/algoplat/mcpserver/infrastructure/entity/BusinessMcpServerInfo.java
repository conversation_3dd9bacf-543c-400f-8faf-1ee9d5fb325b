package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.tools.Tool;
import java.util.List;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessMcpServerInfo {

    private BusinessLine businessLine;

    private List<McpServerEntity> mcpServerEntityList;
}
