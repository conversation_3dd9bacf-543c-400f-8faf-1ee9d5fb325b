package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolParameter {
    /**
     * 参数名
     */
    private String name;

    /**
     * 参数类型，例如Long
     */
    private String type = "String";

    /**
     * 自定义参数类型
     */
    private String customType;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 是否必传
     */
    private Boolean required = true;
}