package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface CatTransaction {

    String DEFAULT_TYPE = "ASPECT_TRANSACTION`";

    /**
     * transaction type
     *
     * @return String
     */
    String type() default "ASPECT_TRANSACTION";

    /**
     * transaction name
     *
     * @return String
     */
    String name() default "";

    String fieldValAsName() default "";
}

