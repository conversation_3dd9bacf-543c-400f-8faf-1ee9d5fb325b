package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.oceanus.http.client.okhttp.OceanusInterceptor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.client.utils.URIBuilder;
import org.jetbrains.annotations.Nullable;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HttpUtil {
    private static String OCTO_APP_KEY = "com.sankuai.octo.openapi";
    private static OkHttpClient client;
    private static OkHttpClient octoClient;

    static {
        client = new OkHttpClient.Builder().build();
        octoClient = new OkHttpClient.Builder()
                .addInterceptor(new OceanusInterceptor(OCTO_APP_KEY))
                .build();
    }

    /**
     * http get请求
     */
    public static String httpGet(String url, Map<String, String> param, Map<String, String> headerMap) {
        return doHttpGet(url, param, headerMap, client);
    }
    public static String httpGetWithOutTime(String url, Map<String, String> param, Map<String, String> headerMap,int timeoutSeconds) {
        return doHttpGet(url, param, headerMap, client, timeoutSeconds);
    }

    /**
     * http post请求
     */
    public static String httpPost(String url, Map<String, String> param, Map<String, String> headerMap) {
        return doHttpPost(url, param, headerMap, client);
    }

    private static String doHttpPost(String url, Map<String, String> param, Map<String, String> headerMap, OkHttpClient client) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            FormBody.Builder formBuilder = new FormBody.Builder();
            if (!MapUtils.isEmpty(param)) {
                param.forEach(formBuilder::add);
            }
            RequestBody requestBody = formBuilder.build();

            Request.Builder builder = new Request.Builder().url(url).post(requestBody);
            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }
            Request request = builder.build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                resStr = Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
            log.error("HttpUtil.httpPost error ", e);
        } finally {
            log.info("HttpUtil.httpPost url:{}, resp:{}, cost:{}", url, resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }

    public static String httpPostJson(String url, String json, Map<String, String> headerMap) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            RequestBody requestBody = RequestBody.create(json, MediaType.parse("application/json; charset=utf-8"));

            Request.Builder builder = new Request.Builder().url(url).post(requestBody);
            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }
            Request request = builder.build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                resStr = Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
            log.error("HttpUtil.httpPostJson error ", e);
        } finally {
            log.info("HttpUtil.httpPostJson url:{}, resp:{}, cost:{}", url, resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }

    /**
     * http get请求
     */
    public static String octoHttpGet(String url, Map<String, String> param, Map<String, String> headerMap) {
        return doHttpGet(url, param, headerMap, octoClient);
    }


    @Nullable
    private static String doHttpGet(String url, Map<String, String> param, Map<String, String> headerMap, OkHttpClient client) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            if (!MapUtils.isEmpty(param)) {
                URIBuilder builder = new URIBuilder(url);
                param.forEach(builder::setParameter);
                url = builder.build().toString();
            }

            Request.Builder builder = new Request.Builder().url(url);
            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }
            Request request = builder.build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                resStr = Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
            log.error("HttpUtil.httpGet error ", e);
        } finally {
            log.info("HttpUtil.httpGet url:{}, resp:{}, cost:{}", url, resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }

    @Nullable
    private static String doHttpGet(String url, Map<String, String> param, Map<String, String> headerMap,
                                    OkHttpClient client, int timeoutSeconds) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            // 创建新的 client 并设置超时
            OkHttpClient newClient = client.newBuilder()
                    .connectTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .writeTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .build();

            if (!MapUtils.isEmpty(param)) {
                URIBuilder builder = new URIBuilder(url);
                param.forEach(builder::setParameter);
                url = builder.build().toString();
            }

            Request.Builder builder = new Request.Builder().url(url);
            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }
            Request request = builder.build();
            Response response = newClient.newCall(request).execute();
            if (response.isSuccessful()) {
                resStr = Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
            log.error("HttpUtil.httpGet error ", e);
        } finally {
            log.info("HttpUtil.httpGet url:{}, resp:{}, cost:{}", url, resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }

    public static String httpPostWithTimeout(String url, String param, int timeOutSecond, Map<String, String> header) {
        long t = System.currentTimeMillis();
        String resStr = "";
        try {
            HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(url)).newBuilder();
            Request request = new Request.Builder()
                    .url(urlBuilder.build())
                    .headers(Headers.of(header))
                    .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), param))
                    .build();

            Response response = new OkHttpClient.Builder()
                    .connectTimeout(5, TimeUnit.SECONDS)
                    .readTimeout(timeOutSecond, TimeUnit.SECONDS)
                    .build()
                    .newCall(request)
                    .execute();
            log.info("HttpUtil.httpPostWithHeader url:{} param:{}, responseHeader：{}",
                    url, param,
                    JSONObject.toJSONString(response.headers()));
            resStr = Objects.requireNonNull(response.body()).string();
        } catch (Exception e) {
            log.error("HttpUtil.httpPostWithHeader error: url:{}, ex:", url, e);
        } finally {
            log.info("HttpUtil.httpPostWithHeader req:{}, res:{}, cost:{}", JSONObject.toJSONString(param),
                    resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }
    public static String httpPostFormUrlencoded(String url, String jsonStr, Map<String, String> headerMap) {
        long t = System.currentTimeMillis();
        String resStr = null;
        try {
            // 将 JSON 字符串转换为 Map
            Map<String, Object> jsonMap = JSONObject.parseObject(jsonStr);

            // 构建 form-urlencoded 格式的字符串
            StringBuilder formData = new StringBuilder();
            for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
                if (formData.length() > 0) {
                    formData.append("&");
                }
                formData.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                        .append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()), "UTF-8"));
            }

            if (headerMap == null) {
                headerMap = new HashMap<>();
            }
            headerMap.put("Content-Type", "application/x-www-form-urlencoded");

            RequestBody requestBody = RequestBody.create(
                    MediaType.parse("application/x-www-form-urlencoded; charset=utf-8"),
                    formData.toString()
            );

            Request.Builder builder = new Request.Builder()
                    .url(url)
                    .post(requestBody);

            if (!MapUtils.isEmpty(headerMap)) {
                headerMap.forEach(builder::addHeader);
            }

            Request request = builder.build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                resStr = Objects.requireNonNull(response.body()).string();
            } else {
                // 添加错误响应的日志
                resStr = Objects.requireNonNull(response.body()).string();
                log.error("Request failed with code: {}, response: {}", response.code(), resStr);
            }
        } catch (Exception e) {
            log.error("HttpUtil.httpPostFormUrlencoded error ", e);
        } finally {
            log.info("HttpUtil.httpPostFormUrlencoded url:{}, resp:{}, cost:{}", url, resStr, System.currentTimeMillis() - t);
        }
        return resStr;
    }

}