package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import io.modelcontextprotocol.server.McpAsyncServerExchange;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import io.modelcontextprotocol.spec.McpServerSession;
import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.web.servlet.function.ServerResponse;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/6/12
 */
@Slf4j
public class ToolContextUtil {
    public static String querySessionIdFromToolContext(ToolContext toolContext) {
        if (toolContext == null) {
            return null;
        }
        Map<String, Object> context = toolContext.getContext();
        if (MapUtils.isEmpty(context)) {
            return null;
        }
        Object exchange = context.get("exchange");
        if (exchange == null) {
            return null;
        }
        try {
            McpSyncServerExchange mcpAsyncServerExchange = (McpSyncServerExchange) exchange;
            // 获取 exchange 字段
            Field exchangeField = McpSyncServerExchange.class.getDeclaredField("exchange");
            exchangeField.setAccessible(true);
            McpAsyncServerExchange asyncExchange = (McpAsyncServerExchange) exchangeField.get(mcpAsyncServerExchange);

            // 获取 session 字段
            Field sessionField = McpAsyncServerExchange.class.getDeclaredField("session");
            sessionField.setAccessible(true);
            McpServerSession mcpServerSession = (McpServerSession) sessionField.get(asyncExchange);

            // 获取 transport 字段
            Field transportField = mcpServerSession.getClass().getDeclaredField("transport");
            transportField.setAccessible(true);
            Object transport = transportField.get(mcpServerSession);

            Field sseBuilderField = transport.getClass().getDeclaredField("sseBuilder");
            sseBuilderField.setAccessible(true);
            ServerResponse.SseBuilder sseBuilder = (ServerResponse.SseBuilder) sseBuilderField.get(transport);

            Field httpHeadersField = sseBuilder.getClass().getDeclaredField("httpHeaders");
            httpHeadersField.setAccessible(true);

            Object ojb = httpHeadersField.get(sseBuilder);
            Map<String, Object> httpHeaders = JSONObject.parseObject(JSONObject.toJSONString(ojb), Map.class);
            Object sessionId = httpHeaders.get("sessionId");
            if (!Objects.isNull(sessionId)) {
                return sessionId.toString();
            }
            return null;
        } catch (Exception e) {
            log.error("querySessionIdFromToolContext error", e);
            
            return null;
        }
    }
    
    public static String querySessionIdByToolContext(ToolContext toolContext) {
        Object exchange = null;
        if (toolContext == null || MapUtils.isEmpty(toolContext.getContext()) || (exchange = toolContext.getContext().get("exchange")) == null) {
            return "";
        }

        try {
            McpSyncServerExchange mcpSyncServerExchange = (McpSyncServerExchange)exchange;
            if (mcpSyncServerExchange.getClientInfo() == null) {
                return "";
            }

            return mcpSyncServerExchange.getClientInfo().name();
        } catch (Exception e) {
            log.error("Unexpected error when accessing private members", e);
            return "";
        }
    }

}
