package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.InputStreamDeserializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.InputStream;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/5/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class S3UploadRequest {

   @JsonDeserialize(using = InputStreamDeserializer.class)
   private InputStream inputStream;

   private String originalFileName;
}
