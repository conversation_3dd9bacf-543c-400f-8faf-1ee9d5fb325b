package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;

import java.lang.reflect.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/8
 */
public class MethodTypeAnalyzerUtil {
    private static final Set<Class<?>> processedTypes = new HashSet<>();
    private static final Set<Class<?>> BASIC_TYPES = new HashSet<>(Arrays.asList(
            byte.class, short.class, int.class, long.class,
            float.class, double.class, char.class, boolean.class,
            Byte.class, Short.class, Integer.class, Long.class,
            Float.class, Double.class, Character.class, Boolean.class,
            String.class, Void.class
    ));

    public static String analyzeMethod(Method method) {
        processedTypes.clear();
        Map<String, Object> result = new LinkedHashMap<>();

        // 处理参数
        List<Map<String, Object>> params = new ArrayList<>();
        for (Parameter param : method.getParameters()) {
            Map<String, Object> paramInfo = new LinkedHashMap<>();
            paramInfo.put("name", param.getName());
            paramInfo.put("type", buildTypeInfo(param.getParameterizedType()));
            params.add(paramInfo);
        }
        result.put("parameters", params);

        // 处理返回类型
        Map<String, Object> returnType = new LinkedHashMap<>();
        returnType.put("type", buildTypeInfo(method.getGenericReturnType()));
        result.put("returnType", returnType);
        return new com.google.gson.GsonBuilder()
                .setPrettyPrinting()
                .create()
                .toJson(returnType);
    }

    private static Map<String, Object> buildTypeInfo(Type type) {
        Map<String, Object> typeInfo = new LinkedHashMap<>();

        if (type instanceof Class) {
            Class<?> clazz = (Class<?>) type;
            typeInfo.put("type", clazz.getName());

            if (isCustomType(clazz)) {
                if (processedTypes.add(clazz)) {
                    typeInfo.put("fields", getFieldInfo(clazz));
                } else {
                    typeInfo.put("fields", "[Circular Reference]");
                }
            } else {
                typeInfo.put("isBasic", true);
            }

            if (clazz.isArray()) {
                typeInfo.put("componentType", buildTypeInfo(clazz.getComponentType()));
            }
        } else if (type instanceof ParameterizedType) {
            ParameterizedType pType = (ParameterizedType) type;
            Class<?> rawType = (Class<?>) pType.getRawType();
            typeInfo.put("type", rawType.getName());

            List<Map<String, Object>> typeArgs = Arrays.stream(pType.getActualTypeArguments())
                    .map(MethodTypeAnalyzerUtil::buildTypeInfo)
                    .collect(Collectors.toList());
            typeInfo.put("typeArguments", typeArgs);
        } else if (type instanceof TypeVariable) {
            TypeVariable<?> tv = (TypeVariable<?>) type;
            typeInfo.put("type", tv.getName());
            typeInfo.put("isTypeVariable", true);
        } else if (type instanceof GenericArrayType) {
            GenericArrayType gat = (GenericArrayType) type;
            typeInfo.put("type", "Array");
            typeInfo.put("componentType", buildTypeInfo(gat.getGenericComponentType()));
        }

        return typeInfo;
    }

    private static List<Map<String, Object>> getFieldInfo(Class<?> clazz) {
        return Arrays.stream(clazz.getDeclaredFields())
                .map(field -> {
                    Map<String, Object> fieldInfo = new LinkedHashMap<>();
                    fieldInfo.put("name", field.getName());
                    fieldInfo.put("type", buildTypeInfo(field.getGenericType()));
                    return fieldInfo;
                })
                .collect(Collectors.toList());
    }

    private static boolean isCustomType(Class<?> clazz) {
        return !clazz.isPrimitive() &&
                !BASIC_TYPES.contains(clazz) &&
                !clazz.getName().startsWith("java.") &&
                !clazz.getName().startsWith("javax.");
    }
}
