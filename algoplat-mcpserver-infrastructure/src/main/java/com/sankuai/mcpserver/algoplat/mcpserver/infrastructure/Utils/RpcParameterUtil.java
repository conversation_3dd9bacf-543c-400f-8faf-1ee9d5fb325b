package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.ToolParameter;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.ParameterTypeEnum;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

public class RpcParameterUtil {

    public static ObjectMapper mapper = new ObjectMapper();
    public static final String WithOUT_REPLACE_N = "withoutReplaceN";

    public static Pair<List<String>, List<String>> parseParamTypeAndValues(List<ToolParameter> parameters, Map<String, Object> toolRequest, Object jsonReplaceRule) throws JsonProcessingException {
        List<String> paramTypes = Lists.newArrayList();
        List<String> paramValues = Lists.newArrayList();
        JsonNode params = mapper.readTree(JSONObject.toJSONString(toolRequest));
        parameters.forEach(parameter -> {
            Object value = toolRequest.get(parameter.getName());
            if ("CustomObject".equals(parameter.getType())) {
                paramTypes.add(parameter.getCustomType());
                paramValues.add(sanitizeJsonString(jsonReplaceRule, params.get(parameter.getName())));
                return;
            } else {
                paramTypes.add(ParameterTypeEnum.getRealTypeByCode(parameter.getType()));
            }
            String jsonStr;
            if (value instanceof String) {
                jsonStr = (String) value;
            } else {
                // 使用 Jackson 或 Gson 将对象转换为 JSON 字符串
                try {
                    jsonStr = mapper.writeValueAsString(value);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
            if (ParameterTypeEnum.LIST.getCode().equals(parameter.getType())) {
                paramValues.add(JacksonUtils.originalSerialize(new Gson().fromJson(jsonStr, new TypeToken<List<Object>>() {
                }.getType())));
            } else if (ParameterTypeEnum.SET.getCode().equals(parameter.getType())) {
                paramValues.add(JacksonUtils.originalSerialize(new Gson().fromJson(jsonStr, new TypeToken<Set<Object>>() {
                }.getType())));
            } else if (ParameterTypeEnum.MAP.getCode().equals(parameter.getType())) {
                paramValues.add(JacksonUtils.originalSerialize(new Gson().fromJson(jsonStr, new TypeToken<Map<String, Object>>() {
                }.getType())));
            } else {
                paramValues.add(JacksonUtils.originalSerialize(value));
            }
        });
        return Pair.of(paramTypes, paramValues);
    }

    /**
     * 处理JSON字符串，移除多余引号并正确处理转义字符
     *
     * @param jsonNode 需要处理的JsonNode对象
     * @return 处理后的JSON字符串
     */
    public static String sanitizeJsonString(Object jsonReplaceRule, JsonNode jsonNode) {
        if (jsonNode == null) {
            return null;
        }
        String jsonString = JacksonUtils.simpleSerialize(jsonNode);
        return sanitizeJsonString(jsonReplaceRule, jsonString);
    }

    /**
     * 处理JSON字符串，移除多余引号并正确处理转义字符
     *
     * @param jsonString 需要处理的JSON字符串
     * @return 处理后的JSON字符串
     */
    public static String sanitizeJsonString(Object jsonReplaceRule, String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return jsonString;
        }
        // 处理开头和结尾的多余引号
        if (jsonString.startsWith("\"") && jsonString.endsWith("\"") && jsonString.length() > 1) {
            jsonString = jsonString.substring(1, jsonString.length() - 1);
        }
        // 处理转义字符，但保持换行符的转义状态
        if (jsonReplaceRule != null && jsonReplaceRule instanceof List && ((List) jsonReplaceRule).contains(WithOUT_REPLACE_N)) {
            return jsonString.replace("\\\"", "\"")
                    .replace("\\\\", "\\")
                    .replace("\\/", "/");
        } else {
            return jsonString.replace("\\\"", "\"")
                    .replace("\\\\", "\\")
                    .replace("\\n", "\n")
                    .replace("\\/", "/");  // 只替换转义的斜杠，不移除所有斜杠
        }
    }
}
