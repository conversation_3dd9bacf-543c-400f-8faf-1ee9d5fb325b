package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 *
 *   表名: bml_mcp_server
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class McpServerEntityPo {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: business_line_id
     *   说明: 所属业务线
     */
    private Long businessLineId;

    /**
     *   字段: mcp_server_name
     *   说明: mcpServer名称
     */
    private String mcpServerName;

    /**
     *   字段: status
     *   说明: 0关闭 1开启
     */
    private Integer status;

    /**
     *   字段: owner
     *   说明: mcpServer的负责人
     */
    private String owner;

    /**
     *   字段: add_time
     *   说明: 记录创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 记录更新时间
     */
    private Date updateTime;

    /**
     *   字段: description
     *   说明: mcpServer的描述信息
     */
    private String description;

    /**
     *   字段: default_tool_name_list
     *   说明: 默认工具名称列表，如["a","b","c"]
     */
    private List<String> defaultToolNameList;
}