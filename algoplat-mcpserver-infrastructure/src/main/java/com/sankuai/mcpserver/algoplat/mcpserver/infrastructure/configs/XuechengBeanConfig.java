package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.configs;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.dxenterprise.open.gateway.service.citadel.api.CitadelService;
import com.sankuai.meituan.org.opensdk.client.RemoteServiceClient;
import com.sankuai.meituan.org.opensdk.service.EmpService;
import com.sankuai.meituan.org.opensdk.service.impl.EmpServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/16
 */
@Configuration
public class XuechengBeanConfig {
    @Bean(name = "xmAuthService", destroyMethod = "destroy")
    ThriftClientProxy xmAuthService() {
        ThriftClientProxy clientProxy = new ThriftClientProxy();
        clientProxy.setServiceInterface(com.sankuai.xm.openplatform.auth.service.XmAuthServiceI.class);
        clientProxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        clientProxy.setTimeout(8000);
        clientProxy.setRemoteUniProto(true);
        return clientProxy;
    }

    @Bean(name = "xmKmService", destroyMethod = "destroy")
    ThriftClientProxy xmKmService() {
        ThriftClientProxy clientProxy = new ThriftClientProxy();
        clientProxy.setServiceInterface(com.sankuai.xm.openplatform.api.service.open.XmOpenKmServiceI.class);
        clientProxy.setRemoteAppkey("com.sankuai.dxenterprise.open.gateway");
        clientProxy.setTimeout(8000);
        clientProxy.setRemoteUniProto(true);
        return clientProxy;
    }

    @Bean
    public CitadelService citadelService() {
        return new CitadelService();
    }
}
