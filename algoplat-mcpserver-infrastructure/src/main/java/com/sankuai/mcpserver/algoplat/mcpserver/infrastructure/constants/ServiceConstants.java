package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.constants;

/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/4
 */
public final class ServiceConstants {

    public static final String APP_KEY = "com.sankuai.algoplatform.mcpserver";

    public static final String JOB_NAME = "ToolInvoke";

    public static final String TRANSPORT_PROVIDER = "_TransportProvider";

    public static final String ROUTER_FUNCTION = "_RouterFunction";

    public static final String SERVER = "_Server";

    public static final String DEFAULT = "DEFAULT";

    public static final String XUECHENG_KEY = "bml_agent_xuecheng_test_cookie_";

    public static final String OFFLINE_DOC_URL = "https://km.it.test.sankuai.com/collabpage/";

    public static final String ONLINE_DOC_URL = "https://km.sankuai.com/collabpage/";

    public final static String AGENT_MIS_ID = "LongTermTask";
}
