package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils;
import java.util.HashMap;
import java.util.Map;
/**
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/8
 */

public class TypeUtil {
    // 预定义类型映射表（全限定类名 -> 简化类型名）
    private static final Map<String, String> TYPE_MAP = new HashMap<>();

    static {
        TYPE_MAP.put("java.util.Map", "Map");
        TYPE_MAP.put("java.util.Set", "Set");
        TYPE_MAP.put("java.util.List", "List");
        TYPE_MAP.put("java.lang.Boolean", "Boolean");
        TYPE_MAP.put("java.lang.Double", "Double");
        TYPE_MAP.put("java.lang.Long", "Long");
        TYPE_MAP.put("java.lang.Integer", "Integer");
        TYPE_MAP.put("java.lang.String", "String");
    }

    /**
     * 将全限定类名转换为简化类型名
     * @param typeName 全限定类名字符串
     * @return 简化后的类型名，自定义类型返回"Object"，无效输入返回null
     */
    public static String convertTypeName(String typeName) {

        if (typeName == null || typeName.isEmpty()) {
            return null;
        }
        String simpleName = TYPE_MAP.get(typeName);
        if (simpleName != null) {
            return simpleName;
        }
        if (typeName.contains("com")) {
            return "Object";
        }
        return null;
    }
}
