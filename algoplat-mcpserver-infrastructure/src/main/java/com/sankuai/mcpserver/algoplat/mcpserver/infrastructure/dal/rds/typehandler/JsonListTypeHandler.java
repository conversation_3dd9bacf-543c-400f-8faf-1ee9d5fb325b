package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.typehandler;

import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.Utils.JacksonUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 处理JSON字符串和List对象之间的转换的通用TypeHandler
 * @param <T> List中的元素类型
 */
@MappedTypes(List.class)
public class JsonListTypeHandler<T> extends BaseTypeHandler<List<T>> {
    private static final Logger log = LoggerFactory.getLogger(JsonListTypeHandler.class);
    private final Class<T> clazz;

    public JsonListTypeHandler(Class<T> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("Type argument cannot be null");
        }
        this.clazz = clazz;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<T> parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = JacksonUtil.toJsonStr(parameter);
            ps.setString(i, json);
        } catch (Exception e) {
            log.error("Error converting List to JSON string", e);
            throw new SQLException("Error converting List to JSON string", e);
        }
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String json = rs.getString(columnName);
        return parseJson(json);
    }

    @Override
    public List<T> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return parseJson(json);
    }

    @Override
    public List<T> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return parseJson(json);
    }

    private List<T> parseJson(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return JacksonUtil.toList(json, clazz);
        } catch (Exception e) {
            log.error("Error parsing JSON string to List<{}>: {}", clazz.getSimpleName(), json, e);
            return null;
        }
    }
}