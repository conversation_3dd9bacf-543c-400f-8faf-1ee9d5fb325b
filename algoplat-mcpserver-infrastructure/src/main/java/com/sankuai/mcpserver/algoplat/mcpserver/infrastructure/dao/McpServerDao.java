package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dao;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example.McpServerEntityPoExample;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.mapper.McpServerEntityPoMapper;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.po.McpServerEntityPo;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.entity.McpServerEntity;
import com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * McpServer数据访问对象
 *
 * @program: algoplat-mcpserver
 * <AUTHOR>
 * @Date 2025/4/20
 */
@Slf4j
@Service
public class McpServerDao {

    @Resource
    private McpServerEntityPoMapper mcpServerEntityPoMapper;

    /**
     * 查询所有状态为1的McpServer
     *
     * @return 所有可用的McpServer列表
     */
    public List<McpServerEntity> selectAllMcpServer() {
        try {
            McpServerEntityPoExample mcpServerEntityPoExample = new McpServerEntityPoExample();
            mcpServerEntityPoExample.createCriteria().andStatusEqualTo(1);
            List<McpServerEntityPo> mcpServerEntityPos = mcpServerEntityPoMapper.selectByExampleWithBLOBs(mcpServerEntityPoExample);

            if (CollectionUtils.isEmpty(mcpServerEntityPos)) {
                return new ArrayList<>();
            }

            return mcpServerEntityPos.stream()
                    .map(this::convert)
                    .filter(entity -> entity != null)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询所有McpServer失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据ID查询McpServer
     *
     * @param id McpServer的ID
     * @return 查询到的McpServer，如果不存在或状态为0则返回null
     */
    public McpServerEntity selectMcpServerById(Long id) {
        if (id == null) {
            log.warn("查询McpServer时ID不能为空");
            return null;
        }

        try {
            McpServerEntityPo mcpServerEntityPo = mcpServerEntityPoMapper.selectByPrimaryKey(id);
            return convert(mcpServerEntityPo);
        } catch (Exception e) {
            log.error("根据ID查询McpServer失败, id={}", id, e);
            return null;
        }
    }

    /**
     * 根据业务线ID查询所有状态为1的McpServer
     *
     * @param businessLineId 业务线ID
     * @return 该业务线下所有可用的McpServer列表
     */
    public List<McpServerEntity> selectMcpServerByBusinessLineId(Long businessLineId, StatusEnum statusEnum) {
        if (businessLineId == null) {
            log.warn("查询McpServer时业务线ID不能为空");
            return new ArrayList<>();
        }

        try {
            McpServerEntityPoExample mcpServerEntityPoExample = new McpServerEntityPoExample();
            mcpServerEntityPoExample.createCriteria().andBusinessLineIdEqualTo(businessLineId).andStatusEqualTo(statusEnum.getCode());
            List<McpServerEntityPo> mcpServerEntityPos = mcpServerEntityPoMapper.selectByExampleWithBLOBs(mcpServerEntityPoExample);

            if (CollectionUtils.isEmpty(mcpServerEntityPos)) {
                return new ArrayList<>();
            }

            return mcpServerEntityPos.stream()
                    .map(this::convert)
                    .filter(entity -> entity != null)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据业务线ID查询McpServer失败, businessLineId={}", businessLineId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据名称查询McpServer
     *
     * @param mcpServerName McpServer名称
     * @return 查询到的McpServer列表，只返回状态为1的
     */
    public List<McpServerEntity> selectMcpServerByName(String mcpServerName) {
        if (mcpServerName == null || mcpServerName.trim().isEmpty()) {
            log.warn("查询McpServer时名称不能为空");
            return new ArrayList<>();
        }

        try {
            McpServerEntityPoExample mcpServerEntityPoExample = new McpServerEntityPoExample();
            mcpServerEntityPoExample.createCriteria().andMcpServerNameEqualTo(mcpServerName.trim()).andStatusEqualTo(1);
            List<McpServerEntityPo> mcpServerEntityPos = mcpServerEntityPoMapper.selectByExampleWithBLOBs(mcpServerEntityPoExample);

            if (CollectionUtils.isEmpty(mcpServerEntityPos)) {
                return new ArrayList<>();
            }

            return mcpServerEntityPos.stream()
                    .map(this::convert)
                    .filter(entity -> entity != null)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据名称查询McpServer失败, mcpServerName={}", mcpServerName, e);
            return new ArrayList<>();
        }
    }

    /**
     * 插入新的McpServer
     *
     * @param mcpServerEntity 要插入的McpServer实体
     * @return 插入结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer insert(McpServerEntity mcpServerEntity) {
        if (mcpServerEntity == null) {
            log.warn("插入McpServer时实体不能为空");
            return 0;
        }

        try {
            // 设置创建和更新时间
            Date now = new Date();
            mcpServerEntity.setAddTime(now);
            mcpServerEntity.setUpdateTime(now);
            // 设置状态为1（启用）
            mcpServerEntity.setStatus(1);
            return mcpServerEntityPoMapper.insert(mcpServerEntity);
        } catch (Exception e) {
            log.error("插入McpServer失败, mcpServerEntity={}", mcpServerEntity, e);
            throw e; // 抛出异常以触发事务回滚
        }
    }

    /**
     * 创建新的McpServer并返回创建后的实体
     *
     * @param mcpServerEntity 要创建的McpServer实体
     * @return 创建后的McpServer实体（包含ID）
     */
    @Transactional(rollbackFor = Exception.class)
    public McpServerEntity createMcpServer(McpServerEntity mcpServerEntity) {
        if (mcpServerEntity == null) {
            log.warn("创建McpServer时实体不能为空");
            return null;
        }

        if (mcpServerEntity.getMcpServerName() == null || mcpServerEntity.getMcpServerName().trim().isEmpty()) {
            log.warn("创建McpServer时名称不能为空");
            return null;
        }

        try {
            // 检查名称是否已存在
            List<McpServerEntity> existingServers = selectMcpServerByName(mcpServerEntity.getMcpServerName().trim());
            if (!existingServers.isEmpty()) {
                log.warn("创建McpServer时名称已存在, mcpServerName={}", mcpServerEntity.getMcpServerName());
                return null; // 名称已存在，返回null
            }

            // 设置创建和更新时间
            Date now = new Date();
            mcpServerEntity.setAddTime(now);
            mcpServerEntity.setUpdateTime(now);
            // 设置状态为1（启用）
            mcpServerEntity.setStatus(1);
            // 确保名称已去除前后空格
            mcpServerEntity.setMcpServerName(mcpServerEntity.getMcpServerName().trim());

            // 插入数据并获取ID
            mcpServerEntityPoMapper.insertSelective(mcpServerEntity);

            // 返回创建后的完整对象
            return selectMcpServerById(mcpServerEntity.getId());
        } catch (Exception e) {
            log.error("创建McpServer失败, mcpServerEntity={}", mcpServerEntity, e);
            throw e;
        }
    }

    /**
     * 更新McpServer信息（不允许修改名称）
     *
     * @param mcpServerEntity 要更新的McpServer实体
     * @return 更新后的McpServer实体
     */
    @Transactional(rollbackFor = Exception.class)
    public McpServerEntity updateMcpServer(McpServerEntity mcpServerEntity) {
        if (mcpServerEntity == null || mcpServerEntity.getId() == null) {
            log.warn("更新McpServer时ID不能为空");
            return null;
        }

        try {
            // 获取原始数据
            McpServerEntityPo originalPo = mcpServerEntityPoMapper.selectByPrimaryKey(mcpServerEntity.getId());
            if (originalPo == null || originalPo.getStatus() != 1) {
                log.warn("更新McpServer时原始数据不存在或已被删除, id={}", mcpServerEntity.getId());
                return null;
            }
            // 创建更新对象，只允许更新描述、负责人等信息，不允许更新名称
            McpServerEntityPo updatePo = new McpServerEntityPo();
            updatePo.setId(mcpServerEntity.getId());
            updatePo.setDescription(mcpServerEntity.getDescription());
            updatePo.setOwner(mcpServerEntity.getOwner());
            updatePo.setUpdateTime(new Date());
            updatePo.setDefaultToolNameList(mcpServerEntity.getDefaultToolNameList());
            updatePo.setStatus(1);
            // 执行更新
            int result = mcpServerEntityPoMapper.updateByPrimaryKeySelective(updatePo);
            if (result <= 0) {
                log.warn("更新McpServer失败, id={}", mcpServerEntity.getId());
                return null;
            }

            // 返回更新后的完整对象
            return selectMcpServerById(mcpServerEntity.getId());
        } catch (Exception e) {
            log.error("更新McpServer失败, mcpServerEntity={}", mcpServerEntity, e);
            throw e;
        }
    }

    /**
     * 删除McpServer（将状态设置为0）
     *
     * @param id McpServer的ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteMcpServer(Long id) {
        if (id == null) {
            log.warn("删除McpServer时ID不能为空");
            return 0;
        }
        try {
            McpServerEntityPo mcpServerEntityPo = mcpServerEntityPoMapper.selectByPrimaryKey(id);
            if (mcpServerEntityPo == null || mcpServerEntityPo.getStatus() != 1) {
                log.warn("删除McpServer时原始数据不存在或已被删除, id={}", id);
                return 0;
            }
            // 创建更新对象，将状态设置为0
            McpServerEntityPo updatePo = new McpServerEntityPo();
            updatePo.setId(id);
            updatePo.setStatus(0);
            updatePo.setUpdateTime(new Date());
            // 更新McpServer状态
            int result = mcpServerEntityPoMapper.updateByPrimaryKeySelective(updatePo);
            return result;
        } catch (Exception e) {
            
            log.error("删除McpServer失败, id={}", id, e);
            throw e;
        }
    }


    /**
     * 根据ID列表批量查询McpServer
     *
     * @param ids McpServer的ID列表
     * @return 查询到的McpServer列表，只返回状态为1的
     */
    public List<McpServerEntity> batchSelectMcpServerByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("批量查询McpServer时ID列表不能为空");
            return new ArrayList<>();
        }

        try {
            McpServerEntityPoExample example = new McpServerEntityPoExample();
            example.createCriteria().andIdIn(ids).andStatusEqualTo(1);

            List<McpServerEntityPo> mcpServerEntityPos = mcpServerEntityPoMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(mcpServerEntityPos)) {
                return new ArrayList<>();
            }

            return mcpServerEntityPos.stream()
                    .map(this::convert)
                    .filter(entity -> entity != null)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("批量查询McpServer失败, ids={}", ids, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将McpServerEntityPo转换为McpServerEntity
     *
     * @param mcpServerEntityPo 数据库实体
     * @return 业务实体
     */
    private McpServerEntity convert(McpServerEntityPo mcpServerEntityPo) {
        if (mcpServerEntityPo == null) {
            return null;
        }

        try {
            McpServerEntity mcpServerEntity = new McpServerEntity();
            BeanUtils.copyProperties(mcpServerEntityPo, mcpServerEntity);
            return mcpServerEntity;
        } catch (Exception e) {
            log.error("转换McpServerEntityPo为McpServerEntity失败", e);
            return null;
        }
    }

    /**
     * 检查指定ID的McpServer是否存在且状态为1
     *
     * @param id McpServer的ID
     * @return 是否存在
     */
    public boolean existsActiveMcpServer(Long id) {
        if (id == null) {
            log.warn("检查McpServer是否存在时ID不能为空");
            return false;
        }

        try {
            McpServerEntityPoExample example = new McpServerEntityPoExample();
            example.createCriteria().andIdEqualTo(id).andStatusEqualTo(1);

            return mcpServerEntityPoMapper.countByExample(example) > 0;
        } catch (Exception e) {
            log.error("检查McpServer是否存在失败, id={}", id, e);
            return false;
        }
    }

    /**
     * 检查指定名称的McpServer是否存在且状态为1
     *
     * @param mcpServerName McpServer名称
     * @return 是否存在
     */
    public boolean existsActiveMcpServerByName(String mcpServerName) {
        if (mcpServerName == null || mcpServerName.trim().isEmpty()) {
            log.warn("检查McpServer是否存在时名称不能为空");
            return false;
        }
        try {
            McpServerEntityPoExample example = new McpServerEntityPoExample();
            example.createCriteria().andMcpServerNameEqualTo(mcpServerName.trim()).andStatusEqualTo(1);
            return mcpServerEntityPoMapper.countByExample(example) > 0;
        } catch (Exception e) {
            log.error("检查McpServer是否存在失败, mcpServerName={}", mcpServerName, e);
            return false;
        }
    }

    public List<McpServerEntity> selectByBussIdServerIdAndOwner(Long businessLineId, String owner) {
        McpServerEntityPoExample example = new McpServerEntityPoExample();
        example.createCriteria().andBusinessLineIdEqualTo(businessLineId).andOwnerEqualTo(owner).andStatusEqualTo(1);
        List<McpServerEntityPo> mcpServerEntityPos = mcpServerEntityPoMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(mcpServerEntityPos)) {
            return new ArrayList<>();
        }
        List<McpServerEntity> collect = mcpServerEntityPos.stream().map(this::convert).collect(Collectors.toList());
        return collect;
    }
}
