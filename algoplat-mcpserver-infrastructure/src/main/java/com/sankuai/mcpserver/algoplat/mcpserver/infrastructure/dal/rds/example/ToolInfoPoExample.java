package com.sankuai.mcpserver.algoplat.mcpserver.infrastructure.dal.rds.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ToolInfoPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ToolInfoPoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }
        public Criteria andMcpServerIdIsNull() {
            addCriterion("mcp_server_id is null");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdIsNotNull() {
            addCriterion("mcp_server_id is not null");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdEqualTo(Long value) {
            addCriterion("mcp_server_id =", value, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdNotEqualTo(Long value) {
            addCriterion("mcp_server_id <>", value, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdGreaterThan(Long value) {
            addCriterion("mcp_server_id >", value, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mcp_server_id >=", value, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdLessThan(Long value) {
            addCriterion("mcp_server_id <", value, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdLessThanOrEqualTo(Long value) {
            addCriterion("mcp_server_id <=", value, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdIn(List<Long> values) {
            addCriterion("mcp_server_id in", values, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdNotIn(List<Long> values) {
            addCriterion("mcp_server_id not in", values, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdBetween(Long value1, Long value2) {
            addCriterion("mcp_server_id between", value1, value2, "mcpServerId");
            return (Criteria) this;
        }

        public Criteria andMcpServerIdNotBetween(Long value1, Long value2) {
            addCriterion("mcp_server_id not between", value1, value2, "mcpServerId");
            return (Criteria) this;
        }


        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessLineIsNull() {
            addCriterion("business_line is null");
            return (Criteria) this;
        }

        public Criteria andBusinessLineIsNotNull() {
            addCriterion("business_line is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessLineEqualTo(String value) {
            addCriterion("business_line =", value, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNotEqualTo(String value) {
            addCriterion("business_line <>", value, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineGreaterThan(String value) {
            addCriterion("business_line >", value, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineGreaterThanOrEqualTo(String value) {
            addCriterion("business_line >=", value, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineLessThan(String value) {
            addCriterion("business_line <", value, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineLessThanOrEqualTo(String value) {
            addCriterion("business_line <=", value, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineLike(String value) {
            addCriterion("business_line like", value, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNotLike(String value) {
            addCriterion("business_line not like", value, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineIn(List<String> values) {
            addCriterion("business_line in", values, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNotIn(List<String> values) {
            addCriterion("business_line not in", values, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineBetween(String value1, String value2) {
            addCriterion("business_line between", value1, value2, "businessLine");
            return (Criteria) this;
        }

        public Criteria andBusinessLineNotBetween(String value1, String value2) {
            addCriterion("business_line not between", value1, value2, "businessLine");
            return (Criteria) this;
        }

        public Criteria andToolUuidIsNull() {
            addCriterion("tool_uuid is null");
            return (Criteria) this;
        }

        public Criteria andToolUuidIsNotNull() {
            addCriterion("tool_uuid is not null");
            return (Criteria) this;
        }

        public Criteria andToolUuidEqualTo(String value) {
            addCriterion("tool_uuid =", value, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidNotEqualTo(String value) {
            addCriterion("tool_uuid <>", value, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidGreaterThan(String value) {
            addCriterion("tool_uuid >", value, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidGreaterThanOrEqualTo(String value) {
            addCriterion("tool_uuid >=", value, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidLessThan(String value) {
            addCriterion("tool_uuid <", value, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidLessThanOrEqualTo(String value) {
            addCriterion("tool_uuid <=", value, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidLike(String value) {
            addCriterion("tool_uuid like", value, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidNotLike(String value) {
            addCriterion("tool_uuid not like", value, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidIn(List<String> values) {
            addCriterion("tool_uuid in", values, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidNotIn(List<String> values) {
            addCriterion("tool_uuid not in", values, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidBetween(String value1, String value2) {
            addCriterion("tool_uuid between", value1, value2, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andToolUuidNotBetween(String value1, String value2) {
            addCriterion("tool_uuid not between", value1, value2, "toolUuid");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdIsNull() {
            addCriterion("http_tool_id is null");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdIsNotNull() {
            addCriterion("http_tool_id is not null");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdEqualTo(Long value) {
            addCriterion("http_tool_id =", value, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdNotEqualTo(Long value) {
            addCriterion("http_tool_id <>", value, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdGreaterThan(Long value) {
            addCriterion("http_tool_id >", value, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdGreaterThanOrEqualTo(Long value) {
            addCriterion("http_tool_id >=", value, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdLessThan(Long value) {
            addCriterion("http_tool_id <", value, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdLessThanOrEqualTo(Long value) {
            addCriterion("http_tool_id <=", value, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdIn(List<Long> values) {
            addCriterion("http_tool_id in", values, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdNotIn(List<Long> values) {
            addCriterion("http_tool_id not in", values, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdBetween(Long value1, Long value2) {
            addCriterion("http_tool_id between", value1, value2, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andHttpToolIdNotBetween(Long value1, Long value2) {
            addCriterion("http_tool_id not between", value1, value2, "httpToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdIsNull() {
            addCriterion("thrift_tool_id is null");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdIsNotNull() {
            addCriterion("thrift_tool_id is not null");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdEqualTo(Long value) {
            addCriterion("thrift_tool_id =", value, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdNotEqualTo(Long value) {
            addCriterion("thrift_tool_id <>", value, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdGreaterThan(Long value) {
            addCriterion("thrift_tool_id >", value, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdGreaterThanOrEqualTo(Long value) {
            addCriterion("thrift_tool_id >=", value, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdLessThan(Long value) {
            addCriterion("thrift_tool_id <", value, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdLessThanOrEqualTo(Long value) {
            addCriterion("thrift_tool_id <=", value, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdIn(List<Long> values) {
            addCriterion("thrift_tool_id in", values, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdNotIn(List<Long> values) {
            addCriterion("thrift_tool_id not in", values, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdBetween(Long value1, Long value2) {
            addCriterion("thrift_tool_id between", value1, value2, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andThriftToolIdNotBetween(Long value1, Long value2) {
            addCriterion("thrift_tool_id not between", value1, value2, "thriftToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdIsNull() {
            addCriterion("pigeon_tool_id is null");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdIsNotNull() {
            addCriterion("pigeon_tool_id is not null");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdEqualTo(Long value) {
            addCriterion("pigeon_tool_id =", value, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdNotEqualTo(Long value) {
            addCriterion("pigeon_tool_id <>", value, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdGreaterThan(Long value) {
            addCriterion("pigeon_tool_id >", value, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pigeon_tool_id >=", value, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdLessThan(Long value) {
            addCriterion("pigeon_tool_id <", value, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdLessThanOrEqualTo(Long value) {
            addCriterion("pigeon_tool_id <=", value, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdIn(List<Long> values) {
            addCriterion("pigeon_tool_id in", values, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdNotIn(List<Long> values) {
            addCriterion("pigeon_tool_id not in", values, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdBetween(Long value1, Long value2) {
            addCriterion("pigeon_tool_id between", value1, value2, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andPigeonToolIdNotBetween(Long value1, Long value2) {
            addCriterion("pigeon_tool_id not between", value1, value2, "pigeonToolId");
            return (Criteria) this;
        }

        public Criteria andToolParamsIsNull() {
            addCriterion("tool_params is null");
            return (Criteria) this;
        }

        public Criteria andToolParamsIsNotNull() {
            addCriterion("tool_params is not null");
            return (Criteria) this;
        }

        public Criteria andToolParamsEqualTo(String value) {
            addCriterion("tool_params =", value, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsNotEqualTo(String value) {
            addCriterion("tool_params <>", value, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsGreaterThan(String value) {
            addCriterion("tool_params >", value, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsGreaterThanOrEqualTo(String value) {
            addCriterion("tool_params >=", value, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsLessThan(String value) {
            addCriterion("tool_params <", value, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsLessThanOrEqualTo(String value) {
            addCriterion("tool_params <=", value, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsLike(String value) {
            addCriterion("tool_params like", value, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsNotLike(String value) {
            addCriterion("tool_params not like", value, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsIn(List<String> values) {
            addCriterion("tool_params in", values, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsNotIn(List<String> values) {
            addCriterion("tool_params not in", values, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsBetween(String value1, String value2) {
            addCriterion("tool_params between", value1, value2, "toolParams");
            return (Criteria) this;
        }

        public Criteria andToolParamsNotBetween(String value1, String value2) {
            addCriterion("tool_params not between", value1, value2, "toolParams");
            return (Criteria) this;
        }

        public Criteria andTimeOutIsNull() {
            addCriterion("time_out is null");
            return (Criteria) this;
        }

        public Criteria andTimeOutIsNotNull() {
            addCriterion("time_out is not null");
            return (Criteria) this;
        }

        public Criteria andTimeOutEqualTo(Integer value) {
            addCriterion("time_out =", value, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutNotEqualTo(Integer value) {
            addCriterion("time_out <>", value, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutGreaterThan(Integer value) {
            addCriterion("time_out >", value, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutGreaterThanOrEqualTo(Integer value) {
            addCriterion("time_out >=", value, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutLessThan(Integer value) {
            addCriterion("time_out <", value, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutLessThanOrEqualTo(Integer value) {
            addCriterion("time_out <=", value, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutIn(List<Integer> values) {
            addCriterion("time_out in", values, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutNotIn(List<Integer> values) {
            addCriterion("time_out not in", values, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutBetween(Integer value1, Integer value2) {
            addCriterion("time_out between", value1, value2, "timeOut");
            return (Criteria) this;
        }

        public Criteria andTimeOutNotBetween(Integer value1, Integer value2) {
            addCriterion("time_out not between", value1, value2, "timeOut");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionIsNull() {
            addCriterion("llm_gen_description is null");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionIsNotNull() {
            addCriterion("llm_gen_description is not null");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionEqualTo(Integer value) {
            addCriterion("llm_gen_description =", value, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionNotEqualTo(Integer value) {
            addCriterion("llm_gen_description <>", value, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionGreaterThan(Integer value) {
            addCriterion("llm_gen_description >", value, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionGreaterThanOrEqualTo(Integer value) {
            addCriterion("llm_gen_description >=", value, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionLessThan(Integer value) {
            addCriterion("llm_gen_description <", value, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionLessThanOrEqualTo(Integer value) {
            addCriterion("llm_gen_description <=", value, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionIn(List<Integer> values) {
            addCriterion("llm_gen_description in", values, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionNotIn(List<Integer> values) {
            addCriterion("llm_gen_description not in", values, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionBetween(Integer value1, Integer value2) {
            addCriterion("llm_gen_description between", value1, value2, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andLlmGenDescriptionNotBetween(Integer value1, Integer value2) {
            addCriterion("llm_gen_description not between", value1, value2, "llmGenDescription");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andToolVersionIsNull() {
            addCriterion("tool_version is null");
            return (Criteria) this;
        }

        public Criteria andToolVersionIsNotNull() {
            addCriterion("tool_version is not null");
            return (Criteria) this;
        }

        public Criteria andToolVersionEqualTo(Integer value) {
            addCriterion("tool_version =", value, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionNotEqualTo(Integer value) {
            addCriterion("tool_version <>", value, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionGreaterThan(Integer value) {
            addCriterion("tool_version >", value, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("tool_version >=", value, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionLessThan(Integer value) {
            addCriterion("tool_version <", value, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionLessThanOrEqualTo(Integer value) {
            addCriterion("tool_version <=", value, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionIn(List<Integer> values) {
            addCriterion("tool_version in", values, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionNotIn(List<Integer> values) {
            addCriterion("tool_version not in", values, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionBetween(Integer value1, Integer value2) {
            addCriterion("tool_version between", value1, value2, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andToolVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("tool_version not between", value1, value2, "toolVersion");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("owner is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("owner is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("owner =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("owner <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("owner >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("owner >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("owner <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("owner <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("owner like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("owner not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("owner in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("owner not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("owner between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("owner not between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeIsNull() {
            addCriterion("avitor_script_code is null");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeIsNotNull() {
            addCriterion("avitor_script_code is not null");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeEqualTo(String value) {
            addCriterion("avitor_script_code =", value, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeNotEqualTo(String value) {
            addCriterion("avitor_script_code <>", value, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeGreaterThan(String value) {
            addCriterion("avitor_script_code >", value, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeGreaterThanOrEqualTo(String value) {
            addCriterion("avitor_script_code >=", value, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeLessThan(String value) {
            addCriterion("avitor_script_code <", value, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeLessThanOrEqualTo(String value) {
            addCriterion("avitor_script_code <=", value, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeLike(String value) {
            addCriterion("avitor_script_code like", value, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeNotLike(String value) {
            addCriterion("avitor_script_code not like", value, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeIn(List<String> values) {
            addCriterion("avitor_script_code in", values, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeNotIn(List<String> values) {
            addCriterion("avitor_script_code not in", values, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeBetween(String value1, String value2) {
            addCriterion("avitor_script_code between", value1, value2, "avitorScriptCode");
            return (Criteria) this;
        }

        public Criteria andAvitorScriptCodeNotBetween(String value1, String value2) {
            addCriterion("avitor_script_code not between", value1, value2, "avitorScriptCode");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}