<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.mcpserver</groupId>
        <artifactId>algoplat-mcpserver</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>algoplat-mcpserver-api</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>algoplat-mcpserver-api</name>

    <properties>
        <java.version>8</java.version>
    </properties>

    <dependencies>
    </dependencies>

</project>
