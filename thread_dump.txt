2025-05-19 17:37:48
Full thread dump OpenJDK 64-Bit Server VM (17.0.14+7-LTS mixed mode, emulated-client, sharing):

Threads class SMR info:
_java_thread_list=0x00006000032b9360, length=281, elements={
0x000000012a0b3800, 0x000000012a0b6000, 0x000000014a00ae00, 0x000000014a009a00,
0x000000014a00e400, 0x000000014a00ea00, 0x000000014a017000, 0x0000000129808800,
0x000000012a0be200, 0x000000012b80a400, 0x000000012a0be800, 0x000000012981ae00,
0x000000012b0df000, 0x000000012b47fc00, 0x000000012bc4e800, 0x000000014a237800,
0x000000012a39ac00, 0x000000012a39b200, 0x000000012bc84a00, 0x000000012bc85e00,
0x000000012bc4ee00, 0x000000012bc4f400, 0x000000012a0ac200, 0x000000012b80b200,
0x000000012b80c400, 0x000000012b80b800, 0x000000012a330e00, 0x000000012a1c2600,
0x000000012b80be00, 0x000000012b80aa00, 0x0000000149bbae00, 0x000000012b4c3000,
0x0000000129acda00, 0x0000000129ace000, 0x0000000129ad7800, 0x0000000129ad7e00,
0x0000000129ad8400, 0x0000000129ad8a00, 0x0000000129ad9000, 0x0000000129adc800,
0x0000000129adce00, 0x000000012b4c3600, 0x000000012b507e00, 0x000000012b4f6400,
0x000000012b4f6a00, 0x000000012b4f9000, 0x000000012b4f9600, 0x000000012b4f9c00,
0x000000012b512200, 0x000000012b512800, 0x000000012b512e00, 0x000000012bcb5800,
0x000000012a3bd200, 0x000000012a53f600, 0x000000012bcb7800, 0x000000012a532800,
0x000000012a53ea00, 0x000000012b8c9a00, 0x000000012bc9d800, 0x000000012a532e00,
0x000000012a533400, 0x000000012a547400, 0x000000012bc9ca00, 0x000000012bc9d000,
0x0000000149920a00, 0x000000014a251a00, 0x000000014a252000, 0x000000012bcc5c00,
0x000000012bce4000, 0x000000012bcc6200, 0x000000012b64c400, 0x000000012a53f000,
0x000000012b64dc00, 0x000000014a26c200, 0x000000012b64ae00, 0x0000000129bc2400,
0x000000012a0ab000, 0x000000012a551a00, 0x000000012b651600, 0x000000012bce5800,
0x000000012a3f7e00, 0x000000012b650c00, 0x000000012bce5e00, 0x000000012bce6400,
0x000000012b647c00, 0x000000012bcf2600, 0x000000012bcf2c00, 0x000000012bce4c00,
0x000000012bce5200, 0x000000012bce9e00, 0x000000012b4c5800, 0x000000012a552600,
0x000000012b64ca00, 0x000000012bcea400, 0x000000012bd6ec00, 0x000000012bd6f200,
0x000000012b64d000, 0x000000012bce7c00, 0x000000014a2cf800, 0x000000014a26d600,
0x000000012b637000, 0x000000012b64d600, 0x0000000149be6800, 0x000000012b653800,
0x0000000149ad7000, 0x000000012a546200, 0x000000012a546800, 0x000000012b4caa00,
0x000000012a546e00, 0x000000014a26dc00, 0x0000000129bc6c00, 0x000000014a26c800,
0x000000014a26ce00, 0x0000000149ad7600, 0x0000000129bc3200, 0x000000012500a000,
0x0000000125809c00, 0x000000012a40d200, 0x000000012a40d800, 0x000000012a449000,
0x0000000149bfa800, 0x0000000149a1f200, 0x0000000149a1ec00, 0x0000000149a19200,
0x0000000129b7bc00, 0x0000000129b7c800, 0x000000014a2f6e00, 0x000000014a2ccc00,
0x000000012a409800, 0x0000000149b57400, 0x000000012bd82000, 0x0000000129b7b400,
0x000000012bcbd200, 0x000000012b4cb600, 0x000000012bd83000, 0x0000000149b57a00,
0x000000014a2f6800, 0x0000000126808a00, 0x0000000126809000, 0x000000012580aa00,
0x000000012680a200, 0x000000014a2cd200, 0x000000014a011400, 0x0000000149bb5c00,
0x00000001499efa00, 0x0000000129b7a200, 0x0000000149b55800, 0x0000000149b56e00,
0x0000000129bb8e00, 0x0000000149b56600, 0x0000000129bb8800, 0x00000001499ef400,
0x0000000149a1ba00, 0x0000000149a1c000, 0x000000012b4e2a00, 0x0000000129bbb600,
0x0000000129bb9c00, 0x00000001499f8000, 0x000000012b4e0400, 0x000000012b4bd200,
0x0000000129bbc600, 0x0000000129b7fa00, 0x0000000129bc6400, 0x000000014a015a00,
0x000000014a37ea00, 0x0000000149ad2000, 0x0000000129b7d800, 0x0000000129bbac00,
0x000000014a385a00, 0x000000012a40a800, 0x000000012a504600, 0x000000012a507a00,
0x000000012a508000, 0x000000012a508600, 0x000000012a459400, 0x000000012a459a00,
0x000000012a45a000, 0x000000012a467200, 0x000000012a45a600, 0x000000012a46ba00,
0x0000000129b7e600, 0x0000000129b7f400, 0x000000012b4b8200, 0x000000012b4b8800,
0x000000012b0a8200, 0x000000012b4e0a00, 0x000000012bd8ea00, 0x000000012bd87a00,
0x000000012bd8b200, 0x000000012bd8aa00, 0x000000012bd93a00, 0x000000012bd93200,
0x000000012bd92a00, 0x000000012b0aca00, 0x000000012b0af200, 0x0000000149a04800,
0x000000014a38ce00, 0x000000014a38d400, 0x0000000129b7ec00, 0x0000000129eb4000,
0x0000000129eb4600, 0x000000012a470a00, 0x000000012b6c7000, 0x000000012bdaf400,
0x000000012bd8f600, 0x000000012bdafa00, 0x0000000129b7de00, 0x0000000129eaae00,
0x0000000129eab400, 0x0000000149a05800, 0x0000000149a05e00, 0x000000014a396400,
0x000000014a38da00, 0x000000014a39ec00, 0x0000000129eb4c00, 0x000000014a38e800,
0x000000014a38e000, 0x000000012b6c9800, 0x0000000129eb5200, 0x0000000129ec0200,
0x000000012bdb0a00, 0x0000000149a02c00, 0x0000000149a00200, 0x000000012b6c9e00,
0x000000012b6cac00, 0x0000000129eb6000, 0x000000012b6cd400, 0x0000000129eccc00,
0x0000000129eb5800, 0x000000012b6d7e00, 0x0000000149a03a00, 0x0000000149a03200,
0x000000014a3abe00, 0x0000000149832200, 0x0000000129ed1a00, 0x0000000149a04000,
0x0000000129ecd200, 0x000000012b6ca400, 0x000000012b6cbc00, 0x000000012680a800,
0x0000000126020400, 0x000000012b6cc200, 0x000000012b6d8400, 0x0000000129b72800,
0x000000012b61e200, 0x000000012b61b800, 0x000000014a3ad200, 0x000000014a362e00,
0x000000014a3ae200, 0x0000000149a01200, 0x0000000149a01800, 0x0000000129bbec00,
0x0000000129ae2600, 0x000000014a2fee00, 0x000000012b5f9000, 0x0000000149c2b400,
0x0000000149c29600, 0x0000000149a4f200, 0x000000012b5fec00, 0x000000012b5ffc00,
0x000000012b5ff200, 0x000000012b621400, 0x0000000129bbf800, 0x0000000129b64c00,
0x0000000129edb000, 0x0000000129eda600, 0x0000000129bbfe00, 0x000000012bdb1e00,
0x0000000129bc0400, 0x0000000129b5c400, 0x0000000129ed9200, 0x000000012b5f0e00,
0x000000014a2fa600, 0x0000000129ed9800, 0x0000000129b59400, 0x0000000149ed6400,
0x000000014a2fac00, 0x0000000149ed5200, 0x000000012be25400, 0x000000014a2fbc00,
0x000000012a479c00
}

"Reference Handler" #2 daemon prio=10 os_prio=31 cpu=4.03ms elapsed=393.22s tid=0x000000012a0b3800 nid=0x5c03 waiting on condition  [0x000000016c48e000]
   java.lang.Thread.State: RUNNABLE
	at java.lang.ref.Reference.waitForReferencePendingList(java.base@17.0.14/Native Method)
	at java.lang.ref.Reference.processPendingReferences(java.base@17.0.14/Reference.java:253)
	at java.lang.ref.Reference$ReferenceHandler.run(java.base@17.0.14/Reference.java:215)

"Finalizer" #3 daemon prio=8 os_prio=31 cpu=0.47ms elapsed=393.22s tid=0x000000012a0b6000 nid=0x5d03 in Object.wait()  [0x000000016c69a000]
   java.lang.Thread.State: WAITING (on object monitor)
	at java.lang.Object.wait(java.base@17.0.14/Native Method)
	- waiting on <no object reference available>
	at java.lang.ref.ReferenceQueue.remove(java.base@17.0.14/ReferenceQueue.java:155)
	- locked <0x00000006e0a9fcc8> (a java.lang.ref.ReferenceQueue$Lock)
	at java.lang.ref.ReferenceQueue.remove(java.base@17.0.14/ReferenceQueue.java:176)
	at java.lang.ref.Finalizer$FinalizerThread.run(java.base@17.0.14/Finalizer.java:172)

"Signal Dispatcher" #4 daemon prio=9 os_prio=31 cpu=0.11ms elapsed=393.21s tid=0x000000014a00ae00 nid=0x6503 waiting on condition  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"Service Thread" #5 daemon prio=9 os_prio=31 cpu=78.21ms elapsed=393.21s tid=0x000000014a009a00 nid=0x6703 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"Monitor Deflation Thread" #6 daemon prio=9 os_prio=31 cpu=29.28ms elapsed=393.21s tid=0x000000014a00e400 nid=0x6903 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"C1 CompilerThread0" #7 daemon prio=9 os_prio=31 cpu=2451.62ms elapsed=393.21s tid=0x000000014a00ea00 nid=0x7403 waiting on condition  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE
   No compile task

"Sweeper thread" #11 daemon prio=9 os_prio=31 cpu=39.41ms elapsed=393.21s tid=0x000000014a017000 nid=0x6b03 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"Common-Cleaner" #12 daemon prio=8 os_prio=31 cpu=2.08ms elapsed=393.20s tid=0x0000000129808800 nid=0x7303 in Object.wait()  [0x000000016d3fa000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(java.base@17.0.14/Native Method)
	- waiting on <no object reference available>
	at java.lang.ref.ReferenceQueue.remove(java.base@17.0.14/ReferenceQueue.java:155)
	- locked <0x00000006e0aa6618> (a java.lang.ref.ReferenceQueue$Lock)
	at jdk.internal.ref.CleanerImpl.run(java.base@17.0.14/CleanerImpl.java:140)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)
	at jdk.internal.misc.InnocuousThread.run(java.base@17.0.14/InnocuousThread.java:162)

"JDWP Transport Listener: dt_socket" #13 daemon prio=10 os_prio=31 cpu=3.65ms elapsed=393.19s tid=0x000000012a0be200 nid=0x7203 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"JDWP Event Helper Thread" #14 daemon prio=10 os_prio=31 cpu=275.09ms elapsed=393.19s tid=0x000000012b80a400 nid=0x6e03 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"JDWP Command Reader" #15 daemon prio=10 os_prio=31 cpu=2.05ms elapsed=393.19s tid=0x000000012a0be800 nid=0x6f03 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"Notification Thread" #16 daemon prio=9 os_prio=31 cpu=19.56ms elapsed=392.87s tid=0x000000012981ae00 nid=0x8903 runnable  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"RMI TCP Accept-0" #18 daemon prio=5 os_prio=31 cpu=3.92ms elapsed=392.78s tid=0x000000012b0df000 nid=0x8b03 runnable  [0x000000016ea7e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.accept(java.base@17.0.14/Native Method)
	at sun.nio.ch.NioSocketImpl.accept(java.base@17.0.14/NioSocketImpl.java:760)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:675)
	at java.net.ServerSocket.platformImplAccept(java.base@17.0.14/ServerSocket.java:641)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:617)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:574)
	at java.net.ServerSocket.accept(java.base@17.0.14/ServerSocket.java:532)
	at sun.management.jmxremote.LocalRMIServerSocketFactory$1.accept(jdk.management.agent@17.0.14/LocalRMIServerSocketFactory.java:52)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.executeAcceptLoop(java.rmi@17.0.14/TCPTransport.java:413)
	at sun.rmi.transport.tcp.TCPTransport$AcceptLoop.run(java.rmi@17.0.14/TCPTransport.java:377)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"xmd-remote-log-listenThread" #20 daemon prio=5 os_prio=31 cpu=2.30ms elapsed=392.39s tid=0x000000012b47fc00 nid=0xa003 runnable  [0x000000016f4fa000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.accept(java.base@17.0.14/Native Method)
	at sun.nio.ch.NioSocketImpl.accept(java.base@17.0.14/NioSocketImpl.java:760)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:675)
	at java.net.ServerSocket.platformImplAccept(java.base@17.0.14/ServerSocket.java:641)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:617)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:574)
	at java.net.ServerSocket.accept(java.base@17.0.14/ServerSocket.java:532)
	at com.meituan.inf.xmdlog.remote.RemoteLogChangeServer$2.run(RemoteLogChangeServer.java:63)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Attach Listener" #41 daemon prio=9 os_prio=31 cpu=4.82ms elapsed=392.12s tid=0x000000012bc4e800 nid=0xb703 waiting on condition  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"AzUpdate-Schedule-1-thread-1" #43 daemon prio=5 os_prio=31 cpu=5.05ms elapsed=392.04s tid=0x000000014a237800 nid=0xeb03 waiting on condition  [0x000000017a8ea000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e16c0250> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"RMI Scheduler(0)" #45 daemon prio=5 os_prio=31 cpu=3.08ms elapsed=392.00s tid=0x000000012a39ac00 nid=0xe803 waiting on condition  [0x000000017ad02000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e14d58a8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Thread-18" #48 daemon prio=5 os_prio=31 cpu=274.43ms elapsed=391.86s tid=0x000000012a39b200 nid=0xe607 runnable  [0x000000017af0e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e16be088> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e16be030> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"cat-netty-channel-health-check" #49 daemon prio=5 os_prio=31 cpu=40.85ms elapsed=391.80s tid=0x000000012bc84a00 nid=0xe403 waiting on condition  [0x000000017b11a000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.cat.message.io.ChannelManager.run(ChannelManager.java:447)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)
	at com.dianping.cat.util.Threads$RunnableThread.run(Threads.java:289)

"cat-cat-local-falcon-sender" #52 daemon prio=5 os_prio=31 cpu=20.66ms elapsed=391.79s tid=0x000000012bc85e00 nid=0xe303 waiting on condition  [0x000000017b326000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.cat.httpagent.falcon.FalconHttpAgentSender.run(FalconHttpAgentSender.java:214)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)
	at com.dianping.cat.util.Threads$RunnableThread.run(Threads.java:289)

"cat-heartbeat-task" #53 daemon prio=5 os_prio=31 cpu=154.71ms elapsed=391.79s tid=0x000000012bc4ee00 nid=0xe103 waiting on condition  [0x000000017b532000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.cat.status.StatusUpdateTask.run(StatusUpdateTask.java:270)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)
	at com.dianping.cat.util.Threads$RunnableThread.run(Threads.java:289)

"cat-netty-tcp-data-sender" #54 daemon prio=5 os_prio=31 cpu=306.57ms elapsed=391.79s tid=0x000000012bc4f400 nid=0xdf03 waiting on condition  [0x000000017b73e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e16c72c8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ArrayBlockingQueue.poll(java.base@17.0.14/ArrayBlockingQueue.java:435)
	at com.dianping.cat.message.queue.PriorityMessageQueue.poll(PriorityMessageQueue.java:46)
	at com.dianping.cat.message.io.TcpSocketSender.processMessage(TcpSocketSender.java:131)
	at com.dianping.cat.message.io.TcpSocketSender.run(TcpSocketSender.java:162)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)
	at com.dianping.cat.util.Threads$RunnableThread.run(Threads.java:289)

"cat-local-data-aggregator" #55 daemon prio=5 os_prio=31 cpu=45.01ms elapsed=391.79s tid=0x000000012a0ac200 nid=0xdd03 waiting on condition  [0x000000017b94a000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.cat.analyzer.LocalAggregator$DataUploader.run(LocalAggregator.java:247)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)
	at com.dianping.cat.util.Threads$RunnableThread.run(Threads.java:289)

"lion-meta-refresh-thread-1" #56 daemon prio=5 os_prio=31 cpu=12.51ms elapsed=391.74s tid=0x000000012b80b200 nid=0xdc03 waiting on condition  [0x000000017bb56000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.lion.client.meta.MetaConfigManager$1.run(MetaConfigManager.java:148)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-http-sync-thread-1" #57 daemon prio=5 os_prio=31 cpu=0.07ms elapsed=391.73s tid=0x000000012b80c400 nid=0xbd03 waiting on condition  [0x000000017bd62000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.lion.client.http.HttpConfigLoader$SyncTask.run(HttpConfigLoader.java:157)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-upload-stat-thread-1" #58 daemon prio=5 os_prio=31 cpu=17.92ms elapsed=391.73s tid=0x000000012b80b800 nid=0xbe03 waiting on condition  [0x000000017bf6e000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.lion.client.ConfigManagerImpl$StatisticsMergeTask.run(ConfigManagerImpl.java:761)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-http-poll-0-thread-1" #59 daemon prio=5 os_prio=31 cpu=67.75ms elapsed=391.64s tid=0x000000012a330e00 nid=0xc003 runnable  [0x000000017c179000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.NioSocketImpl.park(java.base@17.0.14/NioSocketImpl.java:186)
	at sun.nio.ch.NioSocketImpl.timedRead(java.base@17.0.14/NioSocketImpl.java:290)
	at sun.nio.ch.NioSocketImpl.implRead(java.base@17.0.14/NioSocketImpl.java:314)
	at sun.nio.ch.NioSocketImpl.read(java.base@17.0.14/NioSocketImpl.java:355)
	at sun.nio.ch.NioSocketImpl$1.read(java.base@17.0.14/NioSocketImpl.java:808)
	at java.net.Socket$SocketInputStream.read(java.base@17.0.14/Socket.java:966)
	at com.dianping.lion.shade.org.apache.http.impl.conn.LoggingInputStream.read(LoggingInputStream.java:84)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.streamRead(SessionInputBufferImpl.java:137)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.fillBuffer(SessionInputBufferImpl.java:153)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.readLine(SessionInputBufferImpl.java:280)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at com.dianping.lion.shade.org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at com.dianping.lion.shade.org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:163)
	at com.dianping.lion.shade.org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:157)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:272)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at com.dianping.lion.shade.org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPollWithRetry(AbstractPollWorker.java:299)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:253)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:223)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.run(AbstractPollWorker.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-PollWorker-nodeCheck--thread-1" #60 daemon prio=5 os_prio=31 cpu=42.09ms elapsed=391.64s tid=0x000000012a1c2600 nid=0xd903 waiting on condition  [0x000000017c386000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.lion.client.http.AbstractPollWorker$NodeHealthCheckTask.run(AbstractPollWorker.java:453)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-task-scheduler-thread-1" #61 daemon prio=5 os_prio=31 cpu=0.06ms elapsed=391.57s tid=0x000000012b80be00 nid=0xd703 waiting on condition  [0x000000017c592000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e1851428> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-http-long-poll-0-thread-1" #62 daemon prio=5 os_prio=31 cpu=128.78ms elapsed=391.56s tid=0x000000012b80aa00 nid=0xc203 runnable  [0x000000017c79d000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.NioSocketImpl.park(java.base@17.0.14/NioSocketImpl.java:186)
	at sun.nio.ch.NioSocketImpl.timedRead(java.base@17.0.14/NioSocketImpl.java:290)
	at sun.nio.ch.NioSocketImpl.implRead(java.base@17.0.14/NioSocketImpl.java:314)
	at sun.nio.ch.NioSocketImpl.read(java.base@17.0.14/NioSocketImpl.java:355)
	at sun.nio.ch.NioSocketImpl$1.read(java.base@17.0.14/NioSocketImpl.java:808)
	at java.net.Socket$SocketInputStream.read(java.base@17.0.14/Socket.java:966)
	at com.dianping.lion.shade.org.apache.http.impl.conn.LoggingInputStream.read(LoggingInputStream.java:84)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.streamRead(SessionInputBufferImpl.java:137)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.fillBuffer(SessionInputBufferImpl.java:153)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.readLine(SessionInputBufferImpl.java:280)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at com.dianping.lion.shade.org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at com.dianping.lion.shade.org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:163)
	at com.dianping.lion.shade.org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:157)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:272)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at com.dianping.lion.shade.org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPollWithRetry(AbstractPollWorker.java:299)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:253)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:223)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.run(AbstractPollWorker.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-LongPollWorker-nodeCheck--thread-1" #63 daemon prio=5 os_prio=31 cpu=32.02ms elapsed=391.56s tid=0x0000000149bbae00 nid=0xd503 waiting on condition  [0x000000017c9aa000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.lion.client.http.AbstractPollWorker$NodeHealthCheckTask.run(AbstractPollWorker.java:453)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-client-govern-NoRealtimeUploadRunner" #67 daemon prio=5 os_prio=31 cpu=0.23ms elapsed=391.44s tid=0x000000012b4c3000 nid=0xd403 waiting on condition  [0x000000017cdc2000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233b448> (a com.dianping.lion.client.govern.runner.NoRealtimeUploadRunner)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at com.dianping.lion.client.govern.runner.NoRealtimeUploadRunner.run(NoRealtimeUploadRunner.java:59)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"pool-6-thread-1" #68 daemon prio=5 os_prio=31 cpu=444.75ms elapsed=391.40s tid=0x0000000129acda00 nid=0xc40b waiting on condition  [0x000000017cbb6000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233b690> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"AsyncScribeAppender-ScribeAsyncAppender" #69 daemon prio=5 os_prio=31 cpu=2.40ms elapsed=391.40s tid=0x0000000129ace000 nid=0xc703 waiting on condition  [0x000000017cfce000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233b8d0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at org.apache.logging.log4j.scribe.appender.AsyncScribeAppender$AsyncThread.run(AsyncScribeAppender.java:703)

"XMDFileAppender-xmdTraceAppender" #70 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x0000000129ad7800 nid=0xc803 waiting on condition  [0x000000017d1da000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233bad0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdSquirrelAppender" #71 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x0000000129ad7e00 nid=0xd103 waiting on condition  [0x000000017d3e6000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233bcd0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-infoAppender" #72 daemon prio=5 os_prio=31 cpu=0.48ms elapsed=391.40s tid=0x0000000129ad8400 nid=0xcf03 waiting on condition  [0x000000017d5f2000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233bed0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdSharkAppender" #73 daemon prio=5 os_prio=31 cpu=0.02ms elapsed=391.40s tid=0x0000000129ad8a00 nid=0xce03 waiting on condition  [0x000000017d7fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233c0d0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdRhinoAppender" #74 daemon prio=5 os_prio=31 cpu=0.02ms elapsed=391.40s tid=0x0000000129ad9000 nid=0xca03 waiting on condition  [0x000000017da0a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e2339870> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdMafkaAppender" #75 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x0000000129adc800 nid=0xcb03 waiting on condition  [0x000000017dc16000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e2339a70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"pool-7-thread-1" #76 daemon prio=5 os_prio=31 cpu=367.72ms elapsed=391.40s tid=0x0000000129adce00 nid=0x15403 waiting on condition  [0x000000017de22000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233c298> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"AsyncScribeAppender-AsyncScribeAppender_com.sankuai.algoplatform.mcpserver.tool.invoke.context.info.log" #77 daemon prio=5 os_prio=31 cpu=0.39ms elapsed=391.40s tid=0x000000012b4c3600 nid=0x15303 waiting on condition  [0x000000017e02e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e2339c48> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at org.apache.logging.log4j.scribe.appender.AsyncScribeAppender$AsyncThread.run(AsyncScribeAppender.java:703)

"XMDFileAppender-errorAppender" #78 daemon prio=5 os_prio=31 cpu=0.08ms elapsed=391.40s tid=0x000000012b507e00 nid=0x15103 waiting on condition  [0x000000017e23a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e2339e48> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-warnAppender" #79 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x000000012b4f6400 nid=0x10203 waiting on condition  [0x000000017e446000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e233a148> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdZebraAppender" #80 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x000000012b4f6a00 nid=0x10303 waiting on condition  [0x000000017e652000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e2345c90> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdCraneAppender" #81 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x000000012b4f9000 nid=0x14d03 waiting on condition  [0x000000017e85e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e2345e90> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdPumaAppender" #82 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x000000012b4f9600 nid=0x10403 waiting on condition  [0x000000017ea6a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e2346090> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdPigeonAppender" #83 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x000000012b4f9c00 nid=0x14a03 waiting on condition  [0x000000017ec76000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e2346290> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdKmsAppender" #84 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x000000012b512200 nid=0x14803 waiting on condition  [0x000000017ee82000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e234b248> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdLionAppender" #85 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x000000012b512800 nid=0x14603 waiting on condition  [0x000000017f08e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e234b448> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"XMDFileAppender-xmdPigeonAccessAppender" #86 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=391.40s tid=0x000000012b512e00 nid=0x10603 waiting on condition  [0x000000017f29a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e234b648> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at com.meituan.inf.xmdlog.XMDFileAppender$AsyncThread.run(XMDFileAppender.java:515)

"lion-key2project-backup-thread-1" #93 daemon prio=5 os_prio=31 cpu=1.32ms elapsed=390.57s tid=0x000000012bcb5800 nid=0x921b waiting on condition  [0x000000016f706000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e4d0db10> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-task-runner-thread-1" #95 daemon prio=5 os_prio=31 cpu=19.12ms elapsed=390.44s tid=0x000000012a3bd200 nid=0x9e0b waiting on condition  [0x000000016fb1e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e19d40e0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Catalina-utility-1" #97 prio=1 os_prio=31 cpu=37.41ms elapsed=389.64s tid=0x000000012a53f600 nid=0x9b07 waiting on condition  [0x000000016ff36000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65ae990> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Catalina-utility-2" #98 prio=1 os_prio=31 cpu=31.98ms elapsed=389.64s tid=0x000000012bcb7800 nid=0x9807 waiting on condition  [0x0000000178206000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65ae990> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"container-0" #99 prio=5 os_prio=31 cpu=2.07ms elapsed=389.63s tid=0x000000012a532800 nid=0xaa07 waiting on condition  [0x0000000178412000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at org.apache.catalina.core.StandardServer.await(StandardServer.java:568)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer$1.run(TomcatWebServer.java:197)

"kms-refresh-1" #100 daemon prio=5 os_prio=31 cpu=25.00ms elapsed=389.51s tid=0x000000012a53ea00 nid=0xff07 waiting on condition  [0x000000017861e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65a3c48> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-http-poll-1-thread-1" #102 daemon prio=5 os_prio=31 cpu=13.51ms elapsed=389.39s tid=0x000000012b8c9a00 nid=0xfe0b runnable  [0x0000000178829000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.NioSocketImpl.park(java.base@17.0.14/NioSocketImpl.java:186)
	at sun.nio.ch.NioSocketImpl.timedRead(java.base@17.0.14/NioSocketImpl.java:290)
	at sun.nio.ch.NioSocketImpl.implRead(java.base@17.0.14/NioSocketImpl.java:314)
	at sun.nio.ch.NioSocketImpl.read(java.base@17.0.14/NioSocketImpl.java:355)
	at sun.nio.ch.NioSocketImpl$1.read(java.base@17.0.14/NioSocketImpl.java:808)
	at java.net.Socket$SocketInputStream.read(java.base@17.0.14/Socket.java:966)
	at com.dianping.lion.shade.org.apache.http.impl.conn.LoggingInputStream.read(LoggingInputStream.java:84)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.streamRead(SessionInputBufferImpl.java:137)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.fillBuffer(SessionInputBufferImpl.java:153)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.readLine(SessionInputBufferImpl.java:280)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at com.dianping.lion.shade.org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at com.dianping.lion.shade.org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:163)
	at com.dianping.lion.shade.org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:157)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:272)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at com.dianping.lion.shade.org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPollWithRetry(AbstractPollWorker.java:299)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:253)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:223)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.run(AbstractPollWorker.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-PollWorker-nodeCheck--thread-1" #103 daemon prio=5 os_prio=31 cpu=34.23ms elapsed=389.39s tid=0x000000012bc9d800 nid=0xae07 waiting on condition  [0x0000000178a36000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.lion.client.http.AbstractPollWorker$NodeHealthCheckTask.run(AbstractPollWorker.java:453)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Zebra-AppInfo-Upload-Pool-2-Thread-1" #105 daemon prio=5 os_prio=31 cpu=0.07ms elapsed=388.81s tid=0x000000012a532e00 nid=0xfc0b waiting on condition  [0x0000000178c42000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65a3e78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Zebra-AppInfo-Pool-3-Thread-1" #106 daemon prio=5 os_prio=31 cpu=34.58ms elapsed=388.81s tid=0x000000012a533400 nid=0xfa07 waiting on condition  [0x0000000178e4e000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at java.lang.Thread.sleep(java.base@17.0.14/Thread.java:344)
	at java.util.concurrent.TimeUnit.sleep(java.base@17.0.14/TimeUnit.java:446)
	at com.dianping.zebra.filter.appInfo.AppInfoFilter$ScheduleReportTask.run(AppInfoFilter.java:143)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Zebra-Report-Pool-4-Thread-1" #107 daemon prio=5 os_prio=31 cpu=0.04ms elapsed=388.81s tid=0x000000012a547400 nid=0xf807 waiting on condition  [0x000000017905a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65a40e8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at com.dianping.zebra.filter.appInfo.AppInfoFilter$ReportZebraInfoTask.run(AppInfoFilter.java:401)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Zebra-CloseDataSourceTask" #108 daemon prio=5 os_prio=31 cpu=0.10ms elapsed=388.67s tid=0x000000012bc9ca00 nid=0xb007 waiting on condition  [0x0000000179266000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b9a00> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at com.dianping.zebra.single.manager.DefaultSingleDataSourceManager$CloseDataSourceTask.run(DefaultSingleDataSourceManager.java:66)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"mysql-cj-abandoned-connection-cleanup" #109 daemon prio=5 os_prio=31 cpu=19.84ms elapsed=388.66s tid=0x000000012bc9d000 nid=0xf707 in Object.wait()  [0x0000000179472000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(java.base@17.0.14/Native Method)
	- waiting on <no object reference available>
	at java.lang.ref.ReferenceQueue.remove(java.base@17.0.14/ReferenceQueue.java:155)
	- locked <0x00000006e65b2818> (a java.lang.ref.ReferenceQueue$Lock)
	at com.mysql.jdbc.AbandonedConnectionCleanupThread.run(AbandonedConnectionCleanupThread.java:80)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"HikariPool-1 housekeeper" #111 daemon prio=5 os_prio=31 cpu=4.11ms elapsed=388.44s tid=0x0000000149920a00 nid=0xf407 waiting on condition  [0x000000017988a000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65a4408> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-http-poll-2-thread-1" #112 daemon prio=5 os_prio=31 cpu=8.68ms elapsed=388.38s tid=0x000000014a251a00 nid=0xf50b runnable  [0x000000017967d000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.NioSocketImpl.park(java.base@17.0.14/NioSocketImpl.java:186)
	at sun.nio.ch.NioSocketImpl.timedRead(java.base@17.0.14/NioSocketImpl.java:290)
	at sun.nio.ch.NioSocketImpl.implRead(java.base@17.0.14/NioSocketImpl.java:314)
	at sun.nio.ch.NioSocketImpl.read(java.base@17.0.14/NioSocketImpl.java:355)
	at sun.nio.ch.NioSocketImpl$1.read(java.base@17.0.14/NioSocketImpl.java:808)
	at java.net.Socket$SocketInputStream.read(java.base@17.0.14/Socket.java:966)
	at com.dianping.lion.shade.org.apache.http.impl.conn.LoggingInputStream.read(LoggingInputStream.java:84)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.streamRead(SessionInputBufferImpl.java:137)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.fillBuffer(SessionInputBufferImpl.java:153)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.readLine(SessionInputBufferImpl.java:280)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at com.dianping.lion.shade.org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at com.dianping.lion.shade.org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:163)
	at com.dianping.lion.shade.org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:157)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:272)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at com.dianping.lion.shade.org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPollWithRetry(AbstractPollWorker.java:299)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:253)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:223)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.run(AbstractPollWorker.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-PollWorker-nodeCheck--thread-1" #113 daemon prio=5 os_prio=31 cpu=31.68ms elapsed=388.38s tid=0x000000014a252000 nid=0xb307 waiting on condition  [0x0000000179a96000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.lion.client.http.AbstractPollWorker$NodeHealthCheckTask.run(AbstractPollWorker.java:453)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Zebra-Health-Checker-Pool-5-Thread-1" #115 daemon prio=5 os_prio=31 cpu=24.41ms elapsed=388.23s tid=0x000000012bcc5c00 nid=0xb507 waiting on condition  [0x0000000179eae000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b9c58> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Zebra-DataSourceConfigRefresh" #116 daemon prio=5 os_prio=31 cpu=14.65ms elapsed=388.23s tid=0x000000012bce4000 nid=0x14403 waiting on condition  [0x000000017f4a6000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at java.lang.Thread.sleep(java.base@17.0.14/Thread.java:344)
	at java.util.concurrent.TimeUnit.sleep(java.base@17.0.14/TimeUnit.java:446)
	at com.dianping.zebra.group.config.DataSourceConfigRefresh$DataSourceConfigRefreshTask.run(DataSourceConfigRefresh.java:128)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Zebra-DataSourceConfigMgrRefreshTask" #117 daemon prio=5 os_prio=31 cpu=4.92ms elapsed=388.23s tid=0x000000012bcc6200 nid=0x10803 waiting on condition  [0x000000017f6b2000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at java.lang.Thread.sleep(java.base@17.0.14/Thread.java:344)
	at java.util.concurrent.TimeUnit.sleep(java.base@17.0.14/TimeUnit.java:446)
	at com.dianping.zebra.group.config.DataSourceConfigRefresh$DataSourceConfigRefreshTask.run(DataSourceConfigRefresh.java:128)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"HikariPool-2 housekeeper" #118 daemon prio=5 os_prio=31 cpu=3.04ms elapsed=388.02s tid=0x000000012b64c400 nid=0x14203 waiting on condition  [0x000000017f8be000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34eb000> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"TraceCollector" #120 daemon prio=5 os_prio=31 cpu=81.45ms elapsed=388.00s tid=0x000000012a53f000 nid=0x14103 waiting on condition  [0x000000017faca000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.meituan.mtrace.collector.sg.AbstractCollector$Dispatcher.run(AbstractCollector.java:309)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"mtrace-config" #121 daemon prio=5 os_prio=31 cpu=25.23ms elapsed=387.96s tid=0x000000012b64dc00 nid=0x13f03 waiting on condition  [0x000000017fcd6000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e35460c0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-Config-Report-2-thread-1" #124 daemon prio=5 os_prio=31 cpu=0.06ms elapsed=387.68s tid=0x000000014a26c200 nid=0x7807 waiting on condition  [0x0000000300206000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34d67a8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MnsInvoker-Schedule-1-thread-1" #125 daemon prio=5 os_prio=31 cpu=9.24ms elapsed=387.65s tid=0x000000012b64ae00 nid=0x13c03 waiting on condition  [0x0000000300412000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e1dccf38> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-idcGetter-Executor-3-thread-1" #126 daemon prio=5 os_prio=31 cpu=1.20ms elapsed=387.61s tid=0x0000000129bc2400 nid=0x10d03 waiting on condition  [0x000000030061e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e355cd18> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"LoadInfoUtil-4-thread-1" #127 daemon prio=5 os_prio=31 cpu=46.63ms elapsed=387.60s tid=0x000000012a0ab000 nid=0x13903 waiting on condition  [0x000000030082a000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34eb250> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"rhino-thread-1" #128 daemon prio=5 os_prio=31 cpu=2.67ms elapsed=387.57s tid=0x000000012a551a00 nid=0x13803 waiting on condition  [0x0000000300a36000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34d69f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"RhinoHttpSeverBossGroup-2-1" #129 prio=5 os_prio=31 cpu=0.73ms elapsed=387.51s tid=0x000000012b651600 nid=0x13703 runnable  [0x0000000300c42000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e352f758> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e352f700> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Rhino-FalconHttpAsyncSender" #131 daemon prio=5 os_prio=31 cpu=192.50ms elapsed=387.51s tid=0x000000012bce5800 nid=0x13603 waiting on condition  [0x0000000300e4e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e3573930> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.LinkedBlockingQueue.poll(java.base@17.0.14/LinkedBlockingQueue.java:460)
	at com.dianping.rhino.falcon.queue.DefaultFalconMsgQueue.drain(DefaultFalconMsgQueue.java:46)
	at com.dianping.rhino.falcon.sender.FalconHttpAsyncSender.processMessage(FalconHttpAsyncSender.java:62)
	at com.dianping.rhino.falcon.sender.FalconHttpAsyncSender.run(FalconHttpAsyncSender.java:43)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"rhino-thread-2" #132 daemon prio=5 os_prio=31 cpu=0.94ms elapsed=387.44s tid=0x000000012a3f7e00 nid=0x11103 waiting on condition  [0x000000030105a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34d69f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"rhino-thread-3" #135 daemon prio=5 os_prio=31 cpu=0.59ms elapsed=387.43s tid=0x000000012b650c00 nid=0x11503 waiting on condition  [0x0000000301472000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34d69f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"mtthrift-falcon-monitor-7-thread-1" #136 daemon prio=5 os_prio=31 cpu=0.56ms elapsed=387.42s tid=0x000000012bce5e00 nid=0x11307 waiting on condition  [0x0000000301266000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34eb4a0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DefaultAuthHandler-8-thread-1" #137 daemon prio=5 os_prio=31 cpu=1.17ms elapsed=387.42s tid=0x000000012bce6400 nid=0x13303 waiting on condition  [0x000000030167e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e3501df8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DefaultSignHandler-10-thread-1" #138 daemon prio=5 os_prio=31 cpu=6.91ms elapsed=387.41s tid=0x000000012b647c00 nid=0x11603 waiting on condition  [0x000000030188a000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34d6c48> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"rhino-thread-4" #139 daemon prio=5 os_prio=31 cpu=0.06ms elapsed=387.40s tid=0x000000012bcf2600 nid=0x13103 waiting on condition  [0x0000000301a96000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e34d69f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"TimeoutScheduler-15-thread-1" #140 daemon prio=5 os_prio=31 cpu=739.04ms elapsed=387.39s tid=0x000000012bcf2c00 nid=0x11803 waiting on condition  [0x0000000301ca2000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at io.netty.util.HashedWheelTimer$Worker.waitForNextTick(HashedWheelTimer.java:598)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:494)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-1" #141 daemon prio=5 os_prio=31 cpu=0.08ms elapsed=387.38s tid=0x000000012bce4c00 nid=0x12f03 waiting on condition  [0x0000000301eae000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-2" #142 daemon prio=5 os_prio=31 cpu=0.04ms elapsed=387.38s tid=0x000000012bce5200 nid=0x12d03 waiting on condition  [0x00000003020ba000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-3" #143 daemon prio=5 os_prio=31 cpu=0.05ms elapsed=387.38s tid=0x000000012bce9e00 nid=0x12b03 waiting on condition  [0x00000003022c6000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-4" #144 daemon prio=5 os_prio=31 cpu=0.04ms elapsed=387.38s tid=0x000000012b4c5800 nid=0x11b03 waiting on condition  [0x00000003024d2000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-5" #145 daemon prio=5 os_prio=31 cpu=0.04ms elapsed=387.38s tid=0x000000012a552600 nid=0x11c03 waiting on condition  [0x00000003026de000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-6" #146 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=387.38s tid=0x000000012b64ca00 nid=0x11d03 waiting on condition  [0x00000003028ea000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-7" #147 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=387.38s tid=0x000000012bcea400 nid=0x11e03 waiting on condition  [0x0000000302af6000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-8" #148 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=387.38s tid=0x000000012bd6ec00 nid=0x12603 waiting on condition  [0x0000000302d02000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-9" #149 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=387.38s tid=0x000000012bd6f200 nid=0x12003 waiting on condition  [0x0000000302f0e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-heartbeat-Pool-17-thread-10" #150 daemon prio=5 os_prio=31 cpu=0.03ms elapsed=387.38s tid=0x000000012b64d000 nid=0x12203 waiting on condition  [0x000000030311a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e352fc78> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MTthrift-Heartbeat-Timer-16-thread-1" #151 daemon prio=5 os_prio=31 cpu=14.43ms elapsed=387.38s tid=0x000000012bce7c00 nid=0x12403 waiting on condition  [0x0000000303326000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e35464a0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoTimeoutScheduler-pool-1-thread-1" #152 daemon prio=5 os_prio=31 cpu=729.61ms elapsed=387.36s tid=0x000000014a2cf800 nid=0x15503 sleeping [0x0000000303532000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at io.netty.util.HashedWheelTimer$Worker.waitForNextTick(HashedWheelTimer.java:598)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:494)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"RiskLogFilter-thread" #153 prio=5 os_prio=31 cpu=1.57ms elapsed=387.34s tid=0x000000014a26d600 nid=0x9407 waiting on condition  [0x000000016f912000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e3530388> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MnsCacheManager-Schedule-3-thread-1" #155 daemon prio=5 os_prio=31 cpu=365.56ms elapsed=386.68s tid=0x000000012b637000 nid=0x15607 waiting on condition  [0x000000030373e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e356c480> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"ConfigCacheManager-Schedule-5-thread-1" #156 daemon prio=5 os_prio=31 cpu=15.75ms elapsed=386.68s tid=0x000000012b64d600 nid=0x15803 waiting on condition  [0x000000030394a000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e35023c8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"pool-11-thread-1" #157 prio=5 os_prio=31 cpu=0.04ms elapsed=386.68s tid=0x0000000149be6800 nid=0x1fe03 waiting on condition  [0x0000000303b56000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e3573c20> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-1" #159 daemon prio=5 os_prio=31 cpu=1.90ms elapsed=386.12s tid=0x000000012b653800 nid=0x15b07 runnable  [0x0000000303d62000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e34ebac8> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e34eba70> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-2" #160 daemon prio=5 os_prio=31 cpu=1.06ms elapsed=386.11s tid=0x0000000149ad7000 nid=0x15c03 runnable  [0x0000000303f6e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e3518c58> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e3518c00> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"OctoAgentCluster-12-thread-1" #161 daemon prio=5 os_prio=31 cpu=80.08ms elapsed=386.09s tid=0x000000012a546200 nid=0x1fc03 waiting on condition  [0x000000030417a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e35025f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"OctoAgentCluster-12-thread-2" #162 daemon prio=5 os_prio=31 cpu=81.09ms elapsed=386.09s tid=0x000000012a546800 nid=0x1fb03 waiting on condition  [0x0000000304386000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e35025f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DyeRoutingDumpThread" #163 daemon prio=5 os_prio=31 cpu=25.88ms elapsed=386.00s tid=0x000000012b4caa00 nid=0x15f03 runnable  [0x0000000304592000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e35026b8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"KmsTokenImpl-refresh-thread" #165 daemon prio=5 os_prio=31 cpu=114.99ms elapsed=385.51s tid=0x000000012a546e00 nid=0x16107 waiting on condition  [0x000000030479e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e41f2968> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"auth-refresh-thread" #166 daemon prio=5 os_prio=31 cpu=11.56ms elapsed=385.51s tid=0x000000014a26dc00 nid=0x16303 waiting on condition  [0x00000003049aa000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e41dac18> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"nettyHttpServerBossGroup-6-1" #167 prio=5 os_prio=31 cpu=0.23ms elapsed=385.50s tid=0x0000000129bc6c00 nid=0x1f903 runnable  [0x0000000304bb6000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e41ffa70> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e41ffa18> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Uds-Heartbeat-Timer-pool-3-thread-1" #169 prio=5 os_prio=31 cpu=330.10ms elapsed=385.50s tid=0x000000014a26c800 nid=0x1f803 waiting on condition  [0x0000000304dc2000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e358a778> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-1" #170 prio=5 os_prio=31 cpu=31.95ms elapsed=385.42s tid=0x000000014a26ce00 nid=0x1f603 runnable  [0x0000000304fce000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-2" #171 prio=5 os_prio=31 cpu=19.42ms elapsed=385.42s tid=0x0000000149ad7600 nid=0x1f503 runnable  [0x00000003051da000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-3" #172 prio=5 os_prio=31 cpu=16.96ms elapsed=385.42s tid=0x0000000129bc3200 nid=0x16803 runnable  [0x00000003053e6000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"OctoAgentCluster-12-thread-3" #174 daemon prio=5 os_prio=31 cpu=85.06ms elapsed=385.08s tid=0x000000012500a000 nid=0x1f307 waiting on condition  [0x00000003055f2000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e35025f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"OctoAgentCluster-12-thread-4" #175 daemon prio=5 os_prio=31 cpu=85.24ms elapsed=385.08s tid=0x0000000125809c00 nid=0x1f103 waiting on condition  [0x00000003057fe000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e35025f8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-4" #178 prio=5 os_prio=31 cpu=31.05ms elapsed=384.41s tid=0x000000012a40d200 nid=0x1ee03 runnable  [0x0000000305c16000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-5" #179 prio=5 os_prio=31 cpu=18.37ms elapsed=384.41s tid=0x000000012a40d800 nid=0x16b03 runnable  [0x0000000305e22000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-6" #180 prio=5 os_prio=31 cpu=16.85ms elapsed=384.41s tid=0x000000012a449000 nid=0x16c03 runnable  [0x000000030602e000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-3" #181 daemon prio=5 os_prio=31 cpu=0.85ms elapsed=384.38s tid=0x0000000149bfa800 nid=0x16d03 runnable  [0x000000030623a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e34dec30> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e34debd8> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-4" #182 daemon prio=5 os_prio=31 cpu=0.36ms elapsed=384.38s tid=0x0000000149a1f200 nid=0x1ea03 runnable  [0x0000000306446000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35699f0> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e3569940> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-5" #185 daemon prio=5 os_prio=31 cpu=0.49ms elapsed=384.33s tid=0x0000000149a1ec00 nid=0x1e703 runnable  [0x000000030685e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e3562178> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35620c8> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-6" #186 daemon prio=5 os_prio=31 cpu=0.38ms elapsed=384.32s tid=0x0000000149a19200 nid=0x17003 runnable  [0x0000000306a6a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e3565a28> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e3565978> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-7" #188 daemon prio=5 os_prio=31 cpu=0.37ms elapsed=384.25s tid=0x0000000129b7bc00 nid=0x1e403 runnable  [0x0000000306e82000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e350d768> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e350d6b8> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-8" #189 daemon prio=5 os_prio=31 cpu=0.29ms elapsed=384.25s tid=0x0000000129b7c800 nid=0x1e303 runnable  [0x000000030708e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e3510fc8> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e3510f18> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-9" #191 daemon prio=5 os_prio=31 cpu=0.38ms elapsed=384.16s tid=0x000000014a2f6e00 nid=0x1e103 runnable  [0x00000003074a6000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35da200> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35db228> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-10" #192 daemon prio=5 os_prio=31 cpu=0.25ms elapsed=384.15s tid=0x000000014a2ccc00 nid=0x1df03 runnable  [0x00000003076b2000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35d69a0> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35d79c8> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-11" #194 daemon prio=5 os_prio=31 cpu=0.51ms elapsed=383.91s tid=0x000000012a409800 nid=0x17603 runnable  [0x0000000307aca000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35d3140> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35d4168> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-7" #196 prio=5 os_prio=31 cpu=30.89ms elapsed=383.41s tid=0x0000000149b57400 nid=0x1db07 runnable  [0x0000000307cd6000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-8" #197 prio=5 os_prio=31 cpu=21.06ms elapsed=383.41s tid=0x000000012bd82000 nid=0x1da03 runnable  [0x0000000307ee2000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-9" #198 prio=5 os_prio=31 cpu=18.99ms elapsed=383.41s tid=0x0000000129b7b400 nid=0x1d803 runnable  [0x0000000310206000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-12" #200 daemon prio=5 os_prio=31 cpu=0.58ms elapsed=383.05s tid=0x000000012bcbd200 nid=0xf20b runnable  [0x0000000179ca2000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35cf8e0> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35d0908> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-13" #201 daemon prio=5 os_prio=31 cpu=0.51ms elapsed=383.05s tid=0x000000012b4cb600 nid=0x17907 runnable  [0x0000000310412000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35b4d58> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35b5d80> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-14" #202 daemon prio=5 os_prio=31 cpu=0.79ms elapsed=383.03s tid=0x000000012bd83000 nid=0x1d703 runnable  [0x000000031061e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35b14f8> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35b2520> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-15" #203 daemon prio=5 os_prio=31 cpu=0.51ms elapsed=383.02s tid=0x0000000149b57a00 nid=0x17c03 runnable  [0x000000031082a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35adc98> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35aecc0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-16" #204 daemon prio=5 os_prio=31 cpu=0.67ms elapsed=383.01s tid=0x000000014a2f6800 nid=0x1d603 runnable  [0x0000000310a36000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35aa438> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35ab460> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-17" #205 daemon prio=5 os_prio=31 cpu=0.84ms elapsed=382.99s tid=0x0000000126808a00 nid=0x17e03 runnable  [0x0000000310c42000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35a6bd8> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35a7c00> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-10" #207 prio=5 os_prio=31 cpu=30.72ms elapsed=382.41s tid=0x0000000126809000 nid=0x13e0b runnable  [0x000000017fee2000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-11" #208 prio=5 os_prio=31 cpu=19.43ms elapsed=382.41s tid=0x000000012580aa00 nid=0x1d307 runnable  [0x0000000310e4e000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-12" #209 prio=5 os_prio=31 cpu=17.09ms elapsed=382.41s tid=0x000000012680a200 nid=0x1d103 runnable  [0x000000031105a000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-18" #210 daemon prio=5 os_prio=31 cpu=0.80ms elapsed=382.35s tid=0x000000014a2cd200 nid=0x18003 runnable  [0x0000000311266000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e35a3378> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e35a43a0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-19" #212 daemon prio=5 os_prio=31 cpu=0.79ms elapsed=382.34s tid=0x000000014a011400 nid=0x1ce03 runnable  [0x000000031167e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e34ffce8> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e3500d10> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-20" #214 daemon prio=5 os_prio=31 cpu=0.39ms elapsed=381.83s tid=0x0000000149bb5c00 nid=0x1d00b runnable  [0x0000000311472000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e34fc488> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e34fd4b0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-21" #215 daemon prio=5 os_prio=31 cpu=0.34ms elapsed=381.82s tid=0x00000001499efa00 nid=0x18303 runnable  [0x000000031188a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e34f8c28> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e34f9c50> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-13" #216 prio=5 os_prio=31 cpu=27.30ms elapsed=381.40s tid=0x0000000129b7a200 nid=0x18503 runnable  [0x0000000311a96000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-14" #217 prio=5 os_prio=31 cpu=17.26ms elapsed=381.40s tid=0x0000000149b55800 nid=0x18603 runnable  [0x0000000311ca2000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-15" #218 prio=5 os_prio=31 cpu=15.64ms elapsed=381.40s tid=0x0000000149b56e00 nid=0x18803 runnable  [0x0000000311eae000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Auth-Info-Update-Schedule-1-thread-1" #220 daemon prio=5 os_prio=31 cpu=15.32ms elapsed=381.24s tid=0x0000000129bb8e00 nid=0x18907 waiting on condition  [0x00000003120ba000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b13a8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"rhino-threadpool-job" #221 prio=5 os_prio=31 cpu=5.54ms elapsed=381.23s tid=0x0000000149b56600 nid=0x1cb03 waiting on condition  [0x00000003122c6000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b2c58> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"java-sdk-http-connection-reaper" #222 daemon prio=5 os_prio=31 cpu=5.01ms elapsed=381.16s tid=0x0000000129bb8800 nid=0x18c03 waiting on condition  [0x00000003124d2000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.amazonaws.http.IdleConnectionReaper.run(IdleConnectionReaper.java:149)

"pool-14-thread-1" #223 prio=5 os_prio=31 cpu=57.59ms elapsed=381.12s tid=0x00000001499ef400 nid=0x18d03 waiting on condition  [0x00000003126de000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65ba058> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ArrayBlockingQueue.take(java.base@17.0.14/ArrayBlockingQueue.java:420)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-fileconfig-poll-0-thread-1" #224 daemon prio=5 os_prio=31 cpu=28.10ms elapsed=381.11s tid=0x0000000149a1ba00 nid=0x18e03 runnable  [0x00000003128e9000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.NioSocketImpl.park(java.base@17.0.14/NioSocketImpl.java:186)
	at sun.nio.ch.NioSocketImpl.timedRead(java.base@17.0.14/NioSocketImpl.java:290)
	at sun.nio.ch.NioSocketImpl.implRead(java.base@17.0.14/NioSocketImpl.java:314)
	at sun.nio.ch.NioSocketImpl.read(java.base@17.0.14/NioSocketImpl.java:355)
	at sun.nio.ch.NioSocketImpl$1.read(java.base@17.0.14/NioSocketImpl.java:808)
	at java.net.Socket$SocketInputStream.read(java.base@17.0.14/Socket.java:966)
	at com.dianping.lion.shade.org.apache.http.impl.conn.LoggingInputStream.read(LoggingInputStream.java:84)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.streamRead(SessionInputBufferImpl.java:137)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.fillBuffer(SessionInputBufferImpl.java:153)
	at com.dianping.lion.shade.org.apache.http.impl.io.SessionInputBufferImpl.readLine(SessionInputBufferImpl.java:280)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at com.dianping.lion.shade.org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at com.dianping.lion.shade.org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at com.dianping.lion.shade.org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:163)
	at com.dianping.lion.shade.org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:157)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at com.dianping.lion.shade.org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:272)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at com.dianping.lion.shade.org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at com.dianping.lion.shade.org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at com.dianping.lion.shade.org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPollWithRetry(AbstractPollWorker.java:299)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:253)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.doPoll(AbstractPollWorker.java:223)
	at com.dianping.lion.client.http.AbstractPollWorker$PollTask.run(AbstractPollWorker.java:199)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"lion-FileConfigPollWorker-nodeCheck--thread-1" #225 daemon prio=5 os_prio=31 cpu=37.20ms elapsed=381.11s tid=0x0000000149a1c000 nid=0x19003 waiting on condition  [0x0000000312af6000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.lion.client.http.AbstractPollWorker$NodeHealthCheckTask.run(AbstractPollWorker.java:453)
	at java.util.concurrent.Executors$RunnableAdapter.call(java.base@17.0.14/Executors.java:539)
	at java.util.concurrent.FutureTask.run$$$capture(java.base@17.0.14/FutureTask.java:264)
	at java.util.concurrent.FutureTask.run(java.base@17.0.14/FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Thread-53" #227 prio=5 os_prio=31 cpu=0.07ms elapsed=380.75s tid=0x000000012b4e2a00 nid=0x1c707 waiting on condition  [0x0000000312d02000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e4d49678> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.PriorityBlockingQueue.take(java.base@17.0.14/PriorityBlockingQueue.java:535)
	at com.taobao.tair3.client.configserver.ConfigServerUpdater.run(ConfigServerUpdater.java:82)

"tair-worker-share-pool-1-tair-netty-nio-thread-1" #229 daemon prio=5 os_prio=31 cpu=1.98ms elapsed=380.74s tid=0x0000000129bbb600 nid=0x1c603 runnable  [0x0000000312f0e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e65b2f68> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e65b2f10> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DefaultTimer5-pool-2-tair-netty-nio-thread-1" #228 daemon prio=5 os_prio=31 cpu=1298.54ms elapsed=380.73s tid=0x0000000129bb9c00 nid=0x19303 sleeping [0x000000031311a000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at io.netty.util.HashedWheelTimer$Worker.waitForNextTick(HashedWheelTimer.java:598)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:494)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"pool-18-thread-1" #230 prio=5 os_prio=31 cpu=0.09ms elapsed=380.60s tid=0x00000001499f8000 nid=0x19503 waiting on condition  [0x0000000313326000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e4ce5e70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Server-Statistics-Checker-10-thread-1" #232 daemon prio=5 os_prio=31 cpu=10.16ms elapsed=380.45s tid=0x000000012b4e0400 nid=0x19707 waiting on condition  [0x0000000313532000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.pigeon.remoting.provider.process.statistics.ProviderStatisticsChecker.run(ProviderStatisticsChecker.java:21)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Provider-SystemInfo-Thread-14-thread-1" #233 daemon prio=5 os_prio=31 cpu=10.20ms elapsed=380.43s tid=0x000000012b4bd200 nid=0x1c503 waiting on condition  [0x000000031373e000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e4cd21b0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-16" #234 prio=5 os_prio=31 cpu=31.92ms elapsed=380.39s tid=0x0000000129bbc600 nid=0x1c303 runnable  [0x000000031394a000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-17" #235 prio=5 os_prio=31 cpu=18.54ms elapsed=380.39s tid=0x0000000129b7fa00 nid=0x19903 runnable  [0x0000000313b56000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-18" #236 prio=5 os_prio=31 cpu=16.05ms elapsed=380.39s tid=0x0000000129bc6400 nid=0x19a03 runnable  [0x0000000313d62000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Statistics-Checker-26-thread-1" #237 daemon prio=5 os_prio=31 cpu=7.28ms elapsed=380.32s tid=0x000000014a015a00 nid=0x19c03 waiting on condition  [0x0000000313f6e000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.pigeon.remoting.invoker.process.statistics.InvokerStatisticsChecker.run(InvokerStatisticsChecker.java:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"RiskLogFilter-thread" #238 prio=5 os_prio=31 cpu=1.37ms elapsed=380.31s tid=0x000000014a37ea00 nid=0x19e03 waiting on condition  [0x000000031417a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e561d190> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Degrade-Checker-28-thread-1" #239 daemon prio=5 os_prio=31 cpu=15.72ms elapsed=380.30s tid=0x0000000149ad2000 nid=0x1a003 waiting on condition  [0x0000000314386000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.pigeon.remoting.invoker.process.DegradationManager$Checker.run(DegradationManager.java:642)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-ProviderAvailable-ThreadPool-30-thread-1" #250 daemon prio=5 os_prio=31 cpu=0.84ms elapsed=380.30s tid=0x0000000129b7d800 nid=0x1b703 waiting on condition  [0x0000000315a0a000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.pigeon.remoting.invoker.listener.ProviderAvailableListener.run(ProviderAvailableListener.java:60)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-TimeFork-Listener" #251 daemon prio=5 os_prio=31 cpu=0.04ms elapsed=380.29s tid=0x0000000129bbac00 nid=0x1ae03 waiting on condition  [0x0000000315c16000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e561d5c0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.DelayQueue.take(java.base@17.0.14/DelayQueue.java:217)
	at com.dianping.pigeon.remoting.invoker.cluster.HedgedCluster$HedgedListener.run(HedgedCluster.java:149)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Mns-Update-Scheduler-42-thread-1" #253 daemon prio=5 os_prio=31 cpu=7.77ms elapsed=380.12s tid=0x000000014a385a00 nid=0x1b607 waiting on condition  [0x0000000315e22000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e5619e70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #1" #254 daemon prio=5 os_prio=31 cpu=12.39ms elapsed=380.11s tid=0x000000012a40a800 nid=0x1b503 runnable  [0x000000031602e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561fb98> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561fb40> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #2" #255 daemon prio=5 os_prio=31 cpu=12.65ms elapsed=380.11s tid=0x000000012a504600 nid=0x1b103 runnable  [0x000000031623a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561e978> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561e920> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #3" #256 daemon prio=5 os_prio=31 cpu=12.17ms elapsed=380.11s tid=0x000000012a507a00 nid=0x1b203 runnable  [0x0000000316446000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561f1c0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561f168> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #4" #257 daemon prio=5 os_prio=31 cpu=12.27ms elapsed=380.11s tid=0x000000012a508000 nid=0x2a903 runnable  [0x0000000316652000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561e120> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561e0c8> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #5" #258 daemon prio=5 os_prio=31 cpu=12.37ms elapsed=380.11s tid=0x000000012a508600 nid=0x2a703 runnable  [0x000000031685e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561d8c0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561d868> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #6" #259 daemon prio=5 os_prio=31 cpu=12.53ms elapsed=380.11s tid=0x000000012a459400 nid=0x2a603 runnable  [0x0000000316a6a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561da38> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561d9e0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #7" #260 daemon prio=5 os_prio=31 cpu=12.99ms elapsed=380.11s tid=0x000000012a459a00 nid=0x20203 runnable  [0x0000000316c76000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561f480> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561f428> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #8" #261 daemon prio=5 os_prio=31 cpu=12.51ms elapsed=380.11s tid=0x000000012a45a000 nid=0x20303 runnable  [0x0000000316e82000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561ed10> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561ecb8> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #9" #262 daemon prio=5 os_prio=31 cpu=12.82ms elapsed=380.11s tid=0x000000012a467200 nid=0x2a303 runnable  [0x000000031708e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561ff28> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561fed0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #10" #263 daemon prio=5 os_prio=31 cpu=17.82ms elapsed=380.11s tid=0x000000012a45a600 nid=0x2a203 runnable  [0x000000031729a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561f728> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561f6d0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #11" #264 daemon prio=5 os_prio=31 cpu=12.09ms elapsed=380.11s tid=0x000000012a46ba00 nid=0x2a103 runnable  [0x00000003174a6000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561ee48> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561edf0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #12" #265 daemon prio=5 os_prio=31 cpu=14.42ms elapsed=380.11s tid=0x0000000129b7e600 nid=0x2a003 runnable  [0x00000003176b2000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e56201d0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5620178> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #13" #266 daemon prio=5 os_prio=31 cpu=12.57ms elapsed=380.11s tid=0x0000000129b7f400 nid=0x20803 runnable  [0x00000003178be000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5622288> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5622188> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #14" #267 daemon prio=5 os_prio=31 cpu=12.80ms elapsed=380.11s tid=0x000000012b4b8200 nid=0x20903 runnable  [0x0000000317aca000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e56239e8> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5620420> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #15" #268 daemon prio=5 os_prio=31 cpu=16.47ms elapsed=380.11s tid=0x000000012b4b8800 nid=0x20b03 runnable  [0x0000000317cd6000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5621018> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5620fc0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #16" #269 daemon prio=5 os_prio=31 cpu=12.12ms elapsed=380.11s tid=0x000000012b0a8200 nid=0x20d03 runnable  [0x0000000317ee2000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5623858> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5623758> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #17" #270 daemon prio=5 os_prio=31 cpu=12.45ms elapsed=380.11s tid=0x000000012b4e0a00 nid=0x29d03 runnable  [0x00000003180ee000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561c450> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561c3f8> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #18" #271 daemon prio=5 os_prio=31 cpu=14.76ms elapsed=380.11s tid=0x000000012bd8ea00 nid=0x29c03 runnable  [0x00000003182fa000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e56212c0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5621268> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #19" #272 daemon prio=5 os_prio=31 cpu=13.41ms elapsed=380.11s tid=0x000000012bd87a00 nid=0x21003 runnable  [0x0000000318506000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5623248> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5623148> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #20" #273 daemon prio=5 os_prio=31 cpu=15.75ms elapsed=380.11s tid=0x000000012bd8b200 nid=0x21203 runnable  [0x0000000318712000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e561c780> (a sun.nio.ch.Util$2)
	- locked <0x00000006e561c728> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #21" #274 daemon prio=5 os_prio=31 cpu=14.46ms elapsed=380.11s tid=0x000000012bd8aa00 nid=0x29a03 runnable  [0x000000031891e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5625158> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5625058> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #22" #275 daemon prio=5 os_prio=31 cpu=12.82ms elapsed=380.11s tid=0x000000012bd93a00 nid=0x29803 runnable  [0x0000000318b2a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e56262f8> (a sun.nio.ch.Util$2)
	- locked <0x00000006e56262a0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #23" #276 daemon prio=5 os_prio=31 cpu=12.12ms elapsed=380.11s tid=0x000000012bd93200 nid=0x21503 runnable  [0x0000000318d36000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5625ca8> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5625c50> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #24" #277 daemon prio=5 os_prio=31 cpu=12.08ms elapsed=380.11s tid=0x000000012bd92a00 nid=0x29603 runnable  [0x0000000318f42000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5626b98> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5626a98> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O server boss #25" #278 daemon prio=5 os_prio=31 cpu=0.04ms elapsed=380.11s tid=0x000000012b0aca00 nid=0x21703 runnable  [0x000000031914e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5625db0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5625d58> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at org.jboss.netty.channel.socket.nio.NioServerBoss.select(NioServerBoss.java:163)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.NioServerBoss.run(NioServerBoss.java:42)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"974592713@qtp-1977349148-0" #281 daemon prio=5 os_prio=31 cpu=0.94ms elapsed=379.90s tid=0x000000012b0af200 nid=0x29403 in Object.wait()  [0x000000031935a000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(java.base@17.0.14/Native Method)
	- waiting on <no object reference available>
	at org.mortbay.thread.QueuedThreadPool$PoolThread.run(QueuedThreadPool.java:626)
	- locked <0x00000006e5626430> (a org.mortbay.thread.QueuedThreadPool$PoolThread)

"1965312210@qtp-1977349148-1 - Acceptor0 SocketConnector@0.0.0.0:4082" #282 daemon prio=5 os_prio=31 cpu=0.12ms elapsed=379.90s tid=0x0000000149a04800 nid=0x21803 runnable  [0x0000000319566000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.accept(java.base@17.0.14/Native Method)
	at sun.nio.ch.NioSocketImpl.accept(java.base@17.0.14/NioSocketImpl.java:760)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:675)
	at java.net.ServerSocket.platformImplAccept(java.base@17.0.14/ServerSocket.java:641)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:617)
	at java.net.ServerSocket.implAccept(java.base@17.0.14/ServerSocket.java:574)
	at java.net.ServerSocket.accept(java.base@17.0.14/ServerSocket.java:532)
	at org.mortbay.jetty.bio.SocketConnector.accept(SocketConnector.java:99)
	at org.mortbay.jetty.AbstractConnector$Acceptor.run(AbstractConnector.java:708)
	at org.mortbay.thread.QueuedThreadPool$PoolThread.run(QueuedThreadPool.java:582)

"Timer-0" #283 daemon prio=5 os_prio=31 cpu=2.62ms elapsed=379.89s tid=0x000000014a38ce00 nid=0x21903 in Object.wait()  [0x0000000319772000]
   java.lang.Thread.State: TIMED_WAITING (on object monitor)
	at java.lang.Object.wait(java.base@17.0.14/Native Method)
	- waiting on <no object reference available>
	at java.util.TimerThread.mainLoop(java.base@17.0.14/Timer.java:563)
	- locked <0x00000006e562a130> (a java.util.TaskQueue)
	at java.util.TimerThread.run(java.base@17.0.14/Timer.java:516)

"Pigeon-Provider-Heartbeat-Sender-Thread-6-thread-1" #285 daemon prio=5 os_prio=31 cpu=2.54ms elapsed=379.89s tid=0x000000014a38d400 nid=0x21c03 waiting on condition  [0x0000000319b8a000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.pigeon.remoting.common.monitor.heartbeat.DefaultHeartbeatCollector$HeartbeatSenderTask.run(DefaultHeartbeatCollector.java:80)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Invoke-Timeout-Check-ThreadPool-4-thread-1" #286 daemon prio=5 os_prio=31 cpu=12.30ms elapsed=379.88s tid=0x0000000129b7ec00 nid=0x28f03 waiting on condition  [0x0000000319d96000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.pigeon.remoting.invoker.listener.InvocationTimeoutListener.run(InvocationTimeoutListener.java:43)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Loadbalance-ThreadPool-2-thread-1" #287 daemon prio=5 os_prio=31 cpu=61.48ms elapsed=379.88s tid=0x0000000129eb4000 nid=0x21e03 waiting on condition  [0x0000000319fa2000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.pigeon.remoting.invoker.route.balance.LoadBalanceManager$WeightFactorMaintainer.run(LoadBalanceManager.java:273)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Loadbalance-ThreadPool-2-thread-2" #288 daemon prio=5 os_prio=31 cpu=4.61ms elapsed=379.88s tid=0x0000000129eb4600 nid=0x21f03 waiting on condition  [0x000000031a1ae000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at com.dianping.pigeon.remoting.invoker.route.statistics.CapacityChecker.run(CapacityChecker.java:20)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-1" #289 daemon prio=5 os_prio=31 cpu=7.00ms elapsed=379.88s tid=0x000000012a470a00 nid=0x22103 waiting on condition  [0x000000031a3ba000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-2" #290 daemon prio=5 os_prio=31 cpu=6.14ms elapsed=379.88s tid=0x000000012b6c7000 nid=0x22203 waiting on condition  [0x000000031a5c6000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-3" #291 daemon prio=5 os_prio=31 cpu=7.30ms elapsed=379.88s tid=0x000000012bdaf400 nid=0x22303 waiting on condition  [0x000000031a7d2000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-4" #292 daemon prio=5 os_prio=31 cpu=7.48ms elapsed=379.88s tid=0x000000012bd8f600 nid=0x22403 waiting on condition  [0x000000031a9de000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-5" #293 daemon prio=5 os_prio=31 cpu=7.42ms elapsed=379.88s tid=0x000000012bdafa00 nid=0x28a03 waiting on condition  [0x000000031abea000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-6" #294 daemon prio=5 os_prio=31 cpu=7.03ms elapsed=379.88s tid=0x0000000129b7de00 nid=0x22703 waiting on condition  [0x000000031adf6000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-7" #295 daemon prio=5 os_prio=31 cpu=6.84ms elapsed=379.88s tid=0x0000000129eaae00 nid=0x22803 waiting on condition  [0x000000031b002000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-8" #296 daemon prio=5 os_prio=31 cpu=7.29ms elapsed=379.88s tid=0x0000000129eab400 nid=0x22903 waiting on condition  [0x000000031b20e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-9" #297 daemon prio=5 os_prio=31 cpu=7.95ms elapsed=379.88s tid=0x0000000149a05800 nid=0x28603 waiting on condition  [0x000000031b41a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-Response-Processor-24-thread-10" #298 daemon prio=5 os_prio=31 cpu=6.55ms elapsed=379.88s tid=0x0000000149a05e00 nid=0x22a03 waiting on condition  [0x000000031b626000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e562ed98> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue.take(ResizableLinkedBlockingQueue.java:444)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #26" #299 daemon prio=5 os_prio=31 cpu=37.56ms elapsed=379.82s tid=0x000000014a396400 nid=0x28303 runnable  [0x000000031b832000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5642e30> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5642a10> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #27" #300 daemon prio=5 os_prio=31 cpu=56.32ms elapsed=379.82s tid=0x000000014a38da00 nid=0x28203 runnable  [0x000000031ba3e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e562fb00> (a sun.nio.ch.Util$2)
	- locked <0x00000006e562faa8> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #28" #301 daemon prio=5 os_prio=31 cpu=53.49ms elapsed=379.82s tid=0x000000014a39ec00 nid=0x22c03 runnable  [0x000000031bc4a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e562cbc8> (a sun.nio.ch.Util$2)
	- locked <0x00000006e562cb70> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #29" #302 daemon prio=5 os_prio=31 cpu=46.19ms elapsed=379.82s tid=0x0000000129eb4c00 nid=0x27f03 runnable  [0x000000031be56000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5639c58> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5639c00> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #30" #303 daemon prio=5 os_prio=31 cpu=38.50ms elapsed=379.82s tid=0x000000014a38e800 nid=0x22d03 runnable  [0x000000031c062000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563b3c0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563afa0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #31" #304 daemon prio=5 os_prio=31 cpu=64.05ms elapsed=379.82s tid=0x000000014a38e000 nid=0x22f03 runnable  [0x000000031c26e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563eb18> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563eac0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #32" #305 daemon prio=5 os_prio=31 cpu=45.48ms elapsed=379.82s tid=0x000000012b6c9800 nid=0x27c03 runnable  [0x000000031c47a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563edc0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563ed68> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #33" #306 daemon prio=5 os_prio=31 cpu=13.89ms elapsed=379.82s tid=0x0000000129eb5200 nid=0x23003 runnable  [0x000000031c686000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5644048> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5643ff0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #34" #307 daemon prio=5 os_prio=31 cpu=12.58ms elapsed=379.82s tid=0x0000000129ec0200 nid=0x27a03 runnable  [0x000000031c892000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5645090> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5644f90> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #35" #308 daemon prio=5 os_prio=31 cpu=13.83ms elapsed=379.82s tid=0x000000012bdb0a00 nid=0x27903 runnable  [0x000000031ca9e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5642670> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5642570> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #36" #309 daemon prio=5 os_prio=31 cpu=12.28ms elapsed=379.82s tid=0x0000000149a02c00 nid=0x27703 runnable  [0x000000031ccaa000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e56420c0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5641fc0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #37" #310 daemon prio=5 os_prio=31 cpu=13.31ms elapsed=379.82s tid=0x0000000149a00200 nid=0x23403 runnable  [0x000000031ceb6000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563cfc0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563cec0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #38" #311 daemon prio=5 os_prio=31 cpu=14.00ms elapsed=379.82s tid=0x000000012b6c9e00 nid=0x23603 runnable  [0x000000031d0c2000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563f958> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563f848> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #39" #312 daemon prio=5 os_prio=31 cpu=13.18ms elapsed=379.82s tid=0x000000012b6cac00 nid=0x27503 runnable  [0x000000031d2ce000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563f598> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563f498> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #40" #313 daemon prio=5 os_prio=31 cpu=13.02ms elapsed=379.82s tid=0x0000000129eb6000 nid=0x23703 runnable  [0x000000031d4da000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563c000> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563bef0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #41" #314 daemon prio=5 os_prio=31 cpu=12.02ms elapsed=379.82s tid=0x000000012b6cd400 nid=0x23903 runnable  [0x000000031d6e6000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563aa38> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563a938> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #42" #315 daemon prio=5 os_prio=31 cpu=12.56ms elapsed=379.82s tid=0x0000000129eccc00 nid=0x23a03 runnable  [0x000000031d8f2000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e563bb10> (a sun.nio.ch.Util$2)
	- locked <0x00000006e563ba10> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #43" #316 daemon prio=5 os_prio=31 cpu=13.98ms elapsed=379.82s tid=0x0000000129eb5800 nid=0x27103 runnable  [0x000000031dafe000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5637d70> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5637c70> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #44" #317 daemon prio=5 os_prio=31 cpu=15.14ms elapsed=379.82s tid=0x000000012b6d7e00 nid=0x23b03 runnable  [0x000000031dd0a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5634a90> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5634990> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #45" #318 daemon prio=5 os_prio=31 cpu=16.82ms elapsed=379.82s tid=0x0000000149a03a00 nid=0x26f03 runnable  [0x000000031df16000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5637438> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5637338> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #46" #319 daemon prio=5 os_prio=31 cpu=13.12ms elapsed=379.82s tid=0x0000000149a03200 nid=0x23d03 runnable  [0x000000031e122000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5636e88> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5636d88> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #47" #320 daemon prio=5 os_prio=31 cpu=12.81ms elapsed=379.82s tid=0x000000014a3abe00 nid=0x26c03 runnable  [0x000000031e32e000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e56344c0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5630f10> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #48" #321 daemon prio=5 os_prio=31 cpu=12.57ms elapsed=379.82s tid=0x0000000149832200 nid=0x23e03 runnable  [0x000000031e53a000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5633500> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5633400> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O worker #49" #322 daemon prio=5 os_prio=31 cpu=12.70ms elapsed=379.82s tid=0x0000000129ed1a00 nid=0x26a03 runnable  [0x000000031e746000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5632468> (a sun.nio.ch.Util$2)
	- locked <0x00000006e5632368> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.AbstractNioWorker.run(AbstractNioWorker.java:89)
	at org.jboss.netty.channel.socket.nio.NioWorker.run(NioWorker.java:178)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"New I/O boss #50" #324 daemon prio=5 os_prio=31 cpu=27.24ms elapsed=379.82s tid=0x0000000149a04000 nid=0x26903 runnable  [0x000000031e952000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e5630870> (a sun.nio.ch.Util$2)
	- locked <0x00000006e56306c0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.jboss.netty.channel.socket.nio.SelectorUtil.select(SelectorUtil.java:68)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.select(AbstractNioSelector.java:434)
	at org.jboss.netty.channel.socket.nio.AbstractNioSelector.run(AbstractNioSelector.java:212)
	at org.jboss.netty.channel.socket.nio.NioClientBoss.run(NioClientBoss.java:42)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at org.jboss.netty.util.internal.DeadLockProofWorker$1.run(DeadLockProofWorker.java:42)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1136)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Hashed wheel timer #1" #323 daemon prio=5 os_prio=31 cpu=110.79ms elapsed=379.81s tid=0x0000000129ecd200 nid=0x26803 waiting on condition  [0x000000031eb5e000]
   java.lang.Thread.State: TIMED_WAITING (sleeping)
	at java.lang.Thread.sleep(java.base@17.0.14/Native Method)
	at org.jboss.netty.util.HashedWheelTimer$Worker.waitForNextTick(HashedWheelTimer.java:445)
	at org.jboss.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:364)
	at org.jboss.netty.util.ThreadRenamingRunnable.run(ThreadRenamingRunnable.java:108)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-ChannelPool-Check-Pool-18-thread-2" #325 daemon prio=5 os_prio=31 cpu=14.98ms elapsed=379.81s tid=0x000000012b6ca400 nid=0x26703 waiting on condition  [0x000000031ed6a000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e564afa0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-ChannelPool-Check-Pool-18-thread-1" #326 daemon prio=5 os_prio=31 cpu=15.40ms elapsed=379.81s tid=0x000000012b6cbc00 nid=0x26503 waiting on condition  [0x000000031ef76000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e564afa0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-HeartBeat-ThreadPool-thread-2" #327 daemon prio=5 os_prio=31 cpu=67.47ms elapsed=379.81s tid=0x000000012680a800 nid=0x24403 waiting on condition  [0x000000031f182000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e5649fe8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-HeartBeat-ThreadPool-thread-1" #328 daemon prio=5 os_prio=31 cpu=63.57ms elapsed=379.81s tid=0x0000000126020400 nid=0x26403 waiting on condition  [0x000000031f38e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e5649fe8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-HeartBeat-ThreadPool-thread-3" #329 daemon prio=5 os_prio=31 cpu=64.65ms elapsed=379.81s tid=0x000000012b6cc200 nid=0x24703 waiting on condition  [0x000000031f59a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e5649fe8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"Pigeon-Client-HeartBeat-ThreadPool-thread-4" #330 daemon prio=5 os_prio=31 cpu=62.93ms elapsed=379.81s tid=0x000000012b6d8400 nid=0x26203 waiting on condition  [0x000000031f7a6000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e5649fe8> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-19" #332 prio=5 os_prio=31 cpu=26.45ms elapsed=379.39s tid=0x0000000129b72800 nid=0x24807 runnable  [0x000000031997e000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-20" #333 prio=5 os_prio=31 cpu=17.50ms elapsed=379.39s tid=0x000000012b61e200 nid=0x21b07 runnable  [0x000000031f9b2000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-21" #334 prio=5 os_prio=31 cpu=15.32ms elapsed=379.39s tid=0x000000012b61b800 nid=0x24903 runnable  [0x000000031fbbe000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-22" #335 daemon prio=5 os_prio=31 cpu=0.68ms elapsed=379.31s tid=0x000000014a3ad200 nid=0x24b03 runnable  [0x000000031fdca000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e34f53c8> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e34f63f0> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-23" #336 daemon prio=5 os_prio=31 cpu=0.42ms elapsed=379.30s tid=0x000000014a362e00 nid=0x24d03 runnable  [0x000000031ffd6000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e34f1b68> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e34f2b90> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"MtthriftClientNioGroup-5-thread-24" #337 daemon prio=5 os_prio=31 cpu=0.45ms elapsed=379.29s tid=0x000000014a3ae200 nid=0x24e03 runnable  [0x00000003201e2000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e34ee308> (a io.netty.channel.nio.SelectedSelectionKeySet)
	- locked <0x00000006e34ef330> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:146)
	at io.netty.channel.nio.SelectedSelectionKeySetSelector.select(SelectedSelectionKeySetSelector.java:68)
	at io.netty.channel.nio.NioEventLoop.select(NioEventLoop.java:879)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:526)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-22" #340 prio=5 os_prio=31 cpu=30.60ms elapsed=378.39s tid=0x0000000149a01200 nid=0x24f0b runnable  [0x00000003203ee000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-23" #341 prio=5 os_prio=31 cpu=17.92ms elapsed=378.39s tid=0x0000000149a01800 nid=0x25003 runnable  [0x00000003205fa000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DoradoClientNettyUdsWorkerGroup-4-24" #342 prio=5 os_prio=31 cpu=17.17ms elapsed=378.39s tid=0x0000000129bbec00 nid=0x25103 runnable  [0x0000000320806000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"renew-token-pool-1" #347 prio=5 os_prio=31 cpu=1.35ms elapsed=377.31s tid=0x0000000129ae2600 nid=0x25803 waiting on condition  [0x0000000320e2a000]
   java.lang.Thread.State: TIMED_WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65c1e60> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.parkNanos(java.base@17.0.14/LockSupport.java:252)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(java.base@17.0.14/AbstractQueuedSynchronizer.java:1679)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1182)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"renew-token-pool-2" #348 prio=5 os_prio=31 cpu=0.39ms elapsed=377.28s tid=0x000000014a2fee00 nid=0x25603 waiting on condition  [0x0000000321036000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65c1e60> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:1177)
	at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(java.base@17.0.14/ScheduledThreadPoolExecutor.java:899)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"pool-10-thread-1" #350 prio=5 os_prio=31 cpu=3.56ms elapsed=376.93s tid=0x000000012b5f9000 nid=0x25507 waiting on condition  [0x0000000321242000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e3a81138> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-1" #352 daemon prio=5 os_prio=31 cpu=7.30ms elapsed=376.83s tid=0x0000000149c2b400 nid=0x2ab03 runnable  [0x000000032144e000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-2" #353 daemon prio=5 os_prio=31 cpu=8.12ms elapsed=376.83s tid=0x0000000149c29600 nid=0x3ff03 runnable  [0x000000032165a000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-3" #354 daemon prio=5 os_prio=31 cpu=8.93ms elapsed=376.83s tid=0x0000000149a4f200 nid=0x2ad03 runnable  [0x0000000321866000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-4" #355 daemon prio=5 os_prio=31 cpu=7.03ms elapsed=376.83s tid=0x000000012b5fec00 nid=0x2ae03 runnable  [0x0000000321a72000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-5" #356 daemon prio=5 os_prio=31 cpu=9.80ms elapsed=376.83s tid=0x000000012b5ffc00 nid=0x2b003 runnable  [0x0000000321c7e000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-6" #357 daemon prio=5 os_prio=31 cpu=8.44ms elapsed=376.83s tid=0x000000012b5ff200 nid=0x2b203 runnable  [0x0000000321e8a000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-7" #358 daemon prio=5 os_prio=31 cpu=10.29ms elapsed=376.83s tid=0x000000012b621400 nid=0x2b403 runnable  [0x0000000322096000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-8" #359 daemon prio=5 os_prio=31 cpu=7.35ms elapsed=376.83s tid=0x0000000129bbf800 nid=0x2b603 runnable  [0x00000003222a2000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-9" #360 daemon prio=5 os_prio=31 cpu=7.54ms elapsed=376.83s tid=0x0000000129b64c00 nid=0x3fc03 runnable  [0x00000003224ae000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-10" #361 daemon prio=5 os_prio=31 cpu=9.91ms elapsed=376.83s tid=0x0000000129edb000 nid=0x2b803 runnable  [0x00000003226ba000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-11" #362 daemon prio=5 os_prio=31 cpu=9.27ms elapsed=376.83s tid=0x0000000129eda600 nid=0x3fa03 runnable  [0x00000003228c6000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"reactor-http-kqueue-12" #363 daemon prio=5 os_prio=31 cpu=7.93ms elapsed=376.83s tid=0x0000000129bbfe00 nid=0x3f803 runnable  [0x0000000322ad2000]
   java.lang.Thread.State: RUNNABLE
	at io.netty.channel.kqueue.Native.keventWait(Native Method)
	at io.netty.channel.kqueue.Native.keventWait(Native.java:124)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:184)
	at io.netty.channel.kqueue.KQueueEventLoop.kqueueWait(KQueueEventLoop.java:176)
	at io.netty.channel.kqueue.KQueueEventLoop.run(KQueueEventLoop.java:245)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-1" #366 daemon prio=5 os_prio=31 cpu=130.40ms elapsed=376.11s tid=0x000000012bdb1e00 nid=0x3f717 waiting on condition  [0x0000000322cde000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-2" #367 daemon prio=5 os_prio=31 cpu=1.86ms elapsed=376.11s tid=0x0000000129bc0400 nid=0x2bc0f waiting on condition  [0x0000000322eea000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-3" #368 daemon prio=5 os_prio=31 cpu=58.32ms elapsed=376.11s tid=0x0000000129b5c400 nid=0x2be03 waiting on condition  [0x00000003230f6000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-4" #369 daemon prio=5 os_prio=31 cpu=1.85ms elapsed=376.11s tid=0x0000000129ed9200 nid=0x3f603 waiting on condition  [0x0000000323302000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-5" #370 daemon prio=5 os_prio=31 cpu=42.97ms elapsed=376.11s tid=0x000000012b5f0e00 nid=0x3f403 waiting on condition  [0x000000032350e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-6" #371 daemon prio=5 os_prio=31 cpu=2.21ms elapsed=376.11s tid=0x000000014a2fa600 nid=0x2c103 waiting on condition  [0x000000032371a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-7" #372 daemon prio=5 os_prio=31 cpu=56.55ms elapsed=376.11s tid=0x0000000129ed9800 nid=0x2c203 waiting on condition  [0x0000000323926000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-8" #373 daemon prio=5 os_prio=31 cpu=2.56ms elapsed=376.11s tid=0x0000000129b59400 nid=0x2c403 waiting on condition  [0x0000000323b32000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-9" #374 daemon prio=5 os_prio=31 cpu=40.09ms elapsed=376.11s tid=0x0000000149ed6400 nid=0x2c603 waiting on condition  [0x0000000323d3e000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-exec-10" #375 daemon prio=5 os_prio=31 cpu=2.57ms elapsed=376.11s tid=0x000000014a2fac00 nid=0x2c803 waiting on condition  [0x0000000323f4a000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e65b3c70> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:141)
	at org.apache.tomcat.util.threads.TaskQueue.take(TaskQueue.java:33)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1114)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1176)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-Poller" #376 daemon prio=5 os_prio=31 cpu=33.34ms elapsed=376.11s tid=0x0000000149ed5200 nid=0x3f203 runnable  [0x0000000324156000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.KQueue.poll(java.base@17.0.14/Native Method)
	at sun.nio.ch.KQueueSelectorImpl.doSelect(java.base@17.0.14/KQueueSelectorImpl.java:122)
	at sun.nio.ch.SelectorImpl.lockAndDoSelect(java.base@17.0.14/SelectorImpl.java:129)
	- locked <0x00000006e65bade0> (a sun.nio.ch.Util$2)
	- locked <0x00000006e65bac20> (a sun.nio.ch.KQueueSelectorImpl)
	at sun.nio.ch.SelectorImpl.select(java.base@17.0.14/SelectorImpl.java:141)
	at org.apache.tomcat.util.net.NioEndpoint$Poller.run(NioEndpoint.java:812)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"http-nio-8080-Acceptor" #377 daemon prio=5 os_prio=31 cpu=26.76ms elapsed=376.10s tid=0x000000012be25400 nid=0x3f003 runnable  [0x0000000324362000]
   java.lang.Thread.State: RUNNABLE
	at sun.nio.ch.Net.accept(java.base@17.0.14/Native Method)
	at sun.nio.ch.ServerSocketChannelImpl.implAccept(java.base@17.0.14/ServerSocketChannelImpl.java:425)
	at sun.nio.ch.ServerSocketChannelImpl.accept(java.base@17.0.14/ServerSocketChannelImpl.java:391)
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:548)
	at org.apache.tomcat.util.net.NioEndpoint.serverSocketAccept(NioEndpoint.java:79)
	at org.apache.tomcat.util.net.Acceptor.run(Acceptor.java:129)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"DestroyJavaVM" #378 prio=5 os_prio=31 cpu=6268.98ms elapsed=376.09s tid=0x000000014a2fbc00 nid=0x2203 waiting on condition  [0x0000000000000000]
   java.lang.Thread.State: RUNNABLE

"MnsInvoker-ServiceList-Listener-4-thread-1" #390 daemon prio=5 os_prio=31 cpu=0.91ms elapsed=155.70s tid=0x000000012a479c00 nid=0x2cc0b waiting on condition  [0x000000017a6de000]
   java.lang.Thread.State: WAITING (parking)
	at jdk.internal.misc.Unsafe.park(java.base@17.0.14/Native Method)
	- parking to wait for  <0x00000006e3a815b0> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at java.util.concurrent.locks.LockSupport.park(java.base@17.0.14/LockSupport.java:341)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(java.base@17.0.14/AbstractQueuedSynchronizer.java:506)
	at java.util.concurrent.ForkJoinPool.unmanagedBlock(java.base@17.0.14/ForkJoinPool.java:3465)
	at java.util.concurrent.ForkJoinPool.managedBlock(java.base@17.0.14/ForkJoinPool.java:3436)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(java.base@17.0.14/AbstractQueuedSynchronizer.java:1630)
	at java.util.concurrent.LinkedBlockingQueue.take(java.base@17.0.14/LinkedBlockingQueue.java:435)
	at java.util.concurrent.ThreadPoolExecutor.getTask(java.base@17.0.14/ThreadPoolExecutor.java:1062)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(java.base@17.0.14/ThreadPoolExecutor.java:1122)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(java.base@17.0.14/ThreadPoolExecutor.java:635)
	at java.lang.Thread.run(java.base@17.0.14/Thread.java:840)

"G1 Refine#1" os_prio=31 cpu=1.33ms elapsed=389.86s tid=0x000000012ab1efb0 nid=0x970b runnable  

"G1 Conc#2" os_prio=31 cpu=164.79ms elapsed=392.23s tid=0x000000012ab12330 nid=0xee03 runnable  

"G1 Conc#1" os_prio=31 cpu=160.40ms elapsed=392.23s tid=0x000000012aa27690 nid=0xf003 runnable  

"GC Thread#9" os_prio=31 cpu=132.86ms elapsed=392.72s tid=0x000000012a90b870 nid=0xa303 runnable  

"GC Thread#8" os_prio=31 cpu=129.25ms elapsed=392.72s tid=0x000000012a90b1b0 nid=0x8f03 runnable  

"GC Thread#7" os_prio=31 cpu=129.85ms elapsed=392.72s tid=0x000000012ad06440 nid=0xa403 runnable  

"VM Periodic Task Thread" os_prio=31 cpu=139.29ms elapsed=392.78s tid=0x000000012a812f00 nid=0xa503 waiting on condition  

"GC Thread#6" os_prio=31 cpu=133.83ms elapsed=392.97s tid=0x0000000129733260 nid=0xa603 runnable  

"GC Thread#5" os_prio=31 cpu=135.38ms elapsed=392.97s tid=0x00000001297329f0 nid=0x8603 runnable  

"GC Thread#4" os_prio=31 cpu=142.47ms elapsed=392.97s tid=0x0000000129732180 nid=0xa703 runnable  

"GC Thread#3" os_prio=31 cpu=133.00ms elapsed=392.97s tid=0x0000000129731910 nid=0x8303 runnable  

"GC Thread#2" os_prio=31 cpu=128.33ms elapsed=392.97s tid=0x00000001496088d0 nid=0xa803 runnable  

"GC Thread#1" os_prio=31 cpu=126.65ms elapsed=392.97s tid=0x0000000129731660 nid=0x8103 runnable  

"VM Thread" os_prio=31 cpu=107.87ms elapsed=393.22s tid=0x000000012970e640 nid=0x4603 runnable  

"G1 Service" os_prio=31 cpu=39.13ms elapsed=393.23s tid=0x000000012970a830 nid=0x4303 runnable  

"G1 Refine#0" os_prio=31 cpu=10.40ms elapsed=393.23s tid=0x0000000129709f90 nid=0x4203 runnable  

"G1 Conc#0" os_prio=31 cpu=168.06ms elapsed=393.23s tid=0x0000000129707ba0 nid=0x3803 runnable  

"G1 Main Marker" os_prio=31 cpu=2.39ms elapsed=393.23s tid=0x0000000129707310 nid=0x3403 runnable  

"GC Thread#0" os_prio=31 cpu=130.84ms elapsed=393.23s tid=0x0000000129706c60 nid=0x3203 runnable  

JNI global refs: 329, weak refs: 21492

